"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_tr_json";
exports.ids = ["_rsc_messages_tr_json"];
exports.modules = {

/***/ "(rsc)/./messages/tr.json":
/*!**************************!*\
  !*** ./messages/tr.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"Home":{"title":"1Music.cc – Ücretsiz yüksek kaliteli müzik indirme, FLAC/MP3 desteği ve WebDAV yükleme","description":"1Music.cc\'de geniş bir yüksek kaliteli müzik koleksiyonunu keşfedin. FLAC ve MP3 dosyalarını hızlıca indirin ve en iyi ses deneyimi için doğrudan WebDAV\'a yükleyin.","login":"Giriş Yap","darkMode":"Karanlık Mod","setLanguage":"Dili Ayarla","manageUser":"Kullanıcıyı Yönet"},"Login":{"email":"E-posta","password":"Şifre","login":"Giriş Yap","loginSuccess":"Başarıyla giriş yapıldı","forgotPassword":"Şifrenizi mi unuttunuz?","noAccount":"Hesabınız yok mu? Hemen kaydolun","notice":"Bildirim","unknownError":"Bilinmeyen bir hata oluştu","close":"Kapat"},"Register":{"email":"E-posta","password":"Şifre","confirmPassword":"Şifreyi Onayla","invalidEmail":"Geçerli bir e-posta adresi girin","loginSuccess":"Başarıyla giriş yapıldı","passwordLength":"Şifre uzunluğu 10 ile 50 karakter arasında olmalıdır","passwordMismatch":"Şifreler uyuşmuyor","sending":"Gönderiliyor...","sendVerification":"Doğrulama bağlantısını gönder","loggingIn":"Giriş yapılıyor...","verifiedLogin":"Doğrulama yaptım, doğrudan giriş yap","verificationSent":"Doğrulama bağlantısı gönderildi, e-postanızı kontrol edin","verificationFailed":"Doğrulama bağlantısı gönderilemedi","unknownError":"Bilinmeyen bir hata oluştu","confirm":"Onayla","error":"Hata","tip":"İpucu","sendAgain":"Almadınız mı? Tekrar gönder"},"User":{"profile":"Profil","downloaded":"İndirildi","uploadTasks":"Yükleme Görevleri"},"ProfilePage":{"user_info":"Kullanıcı Bilgileri","premium":"(Premium)","webdav_config":"WebDAV Yapılandırması {index}","webdav_url":"WebDAV URL","webdav_url_placeholder":"Lütfen WebDAV URL\'sini girin","username":"Kullanıcı Adı","username_placeholder":"Lütfen kullanıcı adınızı girin","password":"Şifre","password_placeholder":"Lütfen şifrenizi girin","test_and_save":"Bağlantıyı Test Et ve Kaydet","connection_success":"Kaydedildi","delete_config":"Yapılandırmayı Sil","add_another_webdav":"Başka Bir WebDAV Yapılandırması Ekle","add_webdav":"WebDAV Yapılandırması Ekle","config_saved":"Yapılandırma Kaydedildi","test_failed":"WebDAV Bağlantı Testi Başarısız Oldu","incomplete_config":"Lütfen WebDAV yapılandırmasını tamamlayın","remove_failed":"WebDAV yapılandırması silinemedi"},"Download":{"download":"İndir","downloading":"{title} indiriliyor","download_timeout":"İndirme bağlantısı alınırken zaman aşımına uğradı, lütfen daha sonra tekrar deneyin.","download_unavailable":"İndirme bağlantısı geçici olarak kullanılamıyor, lütfen daha sonra tekrar deneyin.","download_song":"{title} indir","upload_to_webdav":"WebDAV\'a Yükle","select_webdav_config":"WebDAV Yapılandırmasını Seçin:","upload_error":"Yükleme Başarısız Oldu","upload_success":"{title} yükleme kuyruğuna eklendi. Daha sonra kullanıcı sayfasını kontrol edin.","cancel":"İptal","loading":"Yükleniyor...","fetch_error":"Bir hata oluştu","select_format":"Format Seçin","mp3":"MP3","flac":"FLAC","select_webdav":"Lütfen bir WebDAV yapılandırması seçin","incomplete_song_info":"Eksik şarkı bilgisi"},"RechargeDialog":{"supportAuthor":"Yazarı Destekle","anyAmount":"Herhangi bir miktarda destek olun ve reklamları kaldırın","cancel":"İptal","confirmPayment":"Ödemeyi Onayla","processing":"İşleniyor...","fetchError":"Ürün listesi alınamadı","paymentInitError":"Ödeme başlatılamadı","requestFailed":"İstek başarısız oldu, lütfen tekrar deneyin"}}');

/***/ })

};
;