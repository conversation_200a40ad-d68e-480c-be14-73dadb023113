"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_zh-CN_json";
exports.ids = ["_rsc_messages_zh-CN_json"];
exports.modules = {

/***/ "(rsc)/./messages/zh-CN.json":
/*!*****************************!*\
  !*** ./messages/zh-CN.json ***!
  \*****************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"Home":{"title":"1Music.cc – 免费高品质音乐下载，支持FLAC/MP3，WebDAV上传","description":"1Music.cc 提供丰富的高品质音乐下载，支持 FLAC、MP3 格式，一键上传至 WebDAV。海量无损音乐资源，快速下载，畅享极致音质体验！","login":"登录","darkMode":"夜间模式","setLanguage":"设置语言","manageUser":"管理用户"},"Login":{"email":"邮箱","password":"密码","login":"登录","loginSuccess":"登录成功","forgotPassword":"找回密码","noAccount":"没有账号？立即注册","notice":"提示","unknownError":"发生未知错误","close":"关闭"},"Register":{"email":"邮箱","password":"密码","confirmPassword":"确认密码","invalidEmail":"请输入有效的邮箱地址","loginSuccess":"登录成功","passwordLength":"密码长度需在10到50个字符之间","passwordMismatch":"密码不匹配","sending":"发送中...","sendVerification":"发送验证链接","loggingIn":"登录中...","verifiedLogin":"我已验证，直接登录","verificationSent":"验证链接已发送，请检查邮箱","verificationFailed":"发送验证链接失败","unknownError":"发生未知错误","confirm":"确认","error":"错误","tip":"提示","sendAgain":"未收到？重新发送"},"User":{"profile":"个人资料","downloaded":"已下载","uploadTasks":"上传任务"},"ProfilePage":{"user_info":"用户信息","premium":"(会员)","webdav_config":"WebDAV 配置 {index}","webdav_url":"WebDAV URL","webdav_url_placeholder":"请输入 WebDAV URL","username":"用户名","username_placeholder":"请输入用户名","password":"密码","password_placeholder":"请输入密码","test_and_save":"测试连接并保存","connection_success":"已保存","delete_config":"删除配置","add_another_webdav":"添加另一个 WebDAV 配置","add_webdav":"添加 WebDAV 配置","config_saved":"配置已保存","test_failed":"WebDAV 连接测试失败","incomplete_config":"请完整填写 WebDav 配置","remove_failed":"删除 WebDAV 配置失败"},"Download":{"download":"下载","downloading":"正在下载 {title}","download_timeout":"获取下载链接超时，请稍后重试。","download_unavailable":"下载链接暂时不可用，请稍后重试。","download_song":"下载 {title}","upload_to_webdav":"上传到 WebDAV","select_webdav_config":"选择 WebDAV 配置：","upload_error":"上传失败","upload_success":"{title} 已添加到上传队列，稍后在用户页面查询","cancel":"取消","preparing_download":"准备下载...","fetching_audio":"获取音频文件...","downloading_files":"下载文件中...","transcoding_audio":"转换音频格式中...","download_complete":"下载完成！","download_failed":"下载失败","failed_to_download_audio":"音频文件下载失败","try_again_later":"请稍后重试","check_downloads_folder":"请检查您的下载文件夹","processing":"处理中...","loading":"加载中...","download_url_ready":"下载链接已获取","loading_transcoder":"加载转码器中...","transcoder_ready":"转码器已就绪","download_complete_preparing":"文件下载完成，准备中...","download_cancelled":"下载已取消","fetch_error":"发生了一些错误","select_format":"选择格式","mp3":"MP3","flac":"FLAC","select_webdav":"请选择一个 WebDAV 配置","incomplete_song_info":"歌曲信息不完整"},"RechargeDialog":{"supportAuthor":"支持作者","anyAmount":"支持任意金额，并免除广告","cancel":"取消","confirmPayment":"确认支付","processing":"处理中...","fetchError":"获取商品列表失败","paymentInitError":"支付初始化失败","requestFailed":"请求失败，请重试"}}');

/***/ })

};
;