/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/download/page";
exports.ids = ["app/[locale]/download/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ar.json": [
		"(rsc)/./messages/ar.json",
		"_rsc_messages_ar_json"
	],
	"./de.json": [
		"(rsc)/./messages/de.json",
		"_rsc_messages_de_json"
	],
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	],
	"./fr.json": [
		"(rsc)/./messages/fr.json",
		"_rsc_messages_fr_json"
	],
	"./hi.json": [
		"(rsc)/./messages/hi.json",
		"_rsc_messages_hi_json"
	],
	"./it.json": [
		"(rsc)/./messages/it.json",
		"_rsc_messages_it_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	],
	"./ko.json": [
		"(rsc)/./messages/ko.json",
		"_rsc_messages_ko_json"
	],
	"./nl.json": [
		"(rsc)/./messages/nl.json",
		"_rsc_messages_nl_json"
	],
	"./pt.json": [
		"(rsc)/./messages/pt.json",
		"_rsc_messages_pt_json"
	],
	"./ru.json": [
		"(rsc)/./messages/ru.json",
		"_rsc_messages_ru_json"
	],
	"./tr.json": [
		"(rsc)/./messages/tr.json",
		"_rsc_messages_tr_json"
	],
	"./zh-CN.json": [
		"(rsc)/./messages/zh-CN.json",
		"_rsc_messages_zh-CN_json"
	],
	"./zh-TW.json": [
		"(rsc)/./messages/zh-TW.json",
		"_rsc_messages_zh-TW_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.js */ \"(rsc)/./src/app/[locale]/layout.js\"));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/download/page.js */ \"(rsc)/./src/app/[locale]/download/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'download',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module3, \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\"],\n        \n      }\n      ]\n      },\n        {\n        'not-found': [module0, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module1, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module2, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/download/page\",\n        pathname: \"/[locale]/download\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js */ \"(rsc)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDJTQwbXVpJTVDJTVDbWF0ZXJpYWwtbmV4dGpzJTVDJTVDdjEzLWFwcFJvdXRlciU1QyU1Q2FwcFJvdXRlclYxMy5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2plY3QlNUMlNUN3ZWIlNUMlNUMxbXVzaWMlNUMlNUMxbXVzaWNfZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0LWludGwlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDbmF2aWdhdGlvbiU1QyU1Q3NoYXJlZCU1QyU1Q0Jhc2VMaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdCU1QyU1Q3dlYiU1QyU1QzFtdXNpYyU1QyU1QzFtdXNpY19mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQtaW50bCU1QyU1Q2Rpc3QlNUMlNUNlc20lNUMlNUNuYXZpZ2F0aW9uJTVDJTVDc2hhcmVkJTVDJTVDTGVnYWN5QmFzZUxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dC1pbnRsJTVDJTVDZGlzdCU1QyU1Q2VzbSU1QyU1Q3NoYXJlZCU1QyU1Q05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4T0FBbUo7QUFDbko7QUFDQSwwT0FBZ0w7QUFDaEw7QUFDQSxzUEFBc0w7QUFDdEw7QUFDQSxnUEFBa0wiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXEBtdWlcXFxcbWF0ZXJpYWwtbmV4dGpzXFxcXHYxMy1hcHBSb3V0ZXJcXFxcYXBwUm91dGVyVjEzLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcUHJvamVjdFxcXFx3ZWJcXFxcMW11c2ljXFxcXDFtdXNpY19mcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXG5hdmlnYXRpb25cXFxcc2hhcmVkXFxcXEJhc2VMaW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcUHJvamVjdFxcXFx3ZWJcXFxcMW11c2ljXFxcXDFtdXNpY19mcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXG5hdmlnYXRpb25cXFxcc2hhcmVkXFxcXExlZ2FjeUJhc2VMaW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcUHJvamVjdFxcXFx3ZWJcXFxcMW11c2ljXFxcXDFtdXNpY19mcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXHNoYXJlZFxcXFxOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js */ \"(ssr)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDJTQwbXVpJTVDJTVDbWF0ZXJpYWwtbmV4dGpzJTVDJTVDdjEzLWFwcFJvdXRlciU1QyU1Q2FwcFJvdXRlclYxMy5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2plY3QlNUMlNUN3ZWIlNUMlNUMxbXVzaWMlNUMlNUMxbXVzaWNfZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0LWludGwlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDbmF2aWdhdGlvbiU1QyU1Q3NoYXJlZCU1QyU1Q0Jhc2VMaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdCU1QyU1Q3dlYiU1QyU1QzFtdXNpYyU1QyU1QzFtdXNpY19mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQtaW50bCU1QyU1Q2Rpc3QlNUMlNUNlc20lNUMlNUNuYXZpZ2F0aW9uJTVDJTVDc2hhcmVkJTVDJTVDTGVnYWN5QmFzZUxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dC1pbnRsJTVDJTVDZGlzdCU1QyU1Q2VzbSU1QyU1Q3NoYXJlZCU1QyU1Q05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4T0FBbUo7QUFDbko7QUFDQSwwT0FBZ0w7QUFDaEw7QUFDQSxzUEFBc0w7QUFDdEw7QUFDQSxnUEFBa0wiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXEBtdWlcXFxcbWF0ZXJpYWwtbmV4dGpzXFxcXHYxMy1hcHBSb3V0ZXJcXFxcYXBwUm91dGVyVjEzLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcUHJvamVjdFxcXFx3ZWJcXFxcMW11c2ljXFxcXDFtdXNpY19mcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXG5hdmlnYXRpb25cXFxcc2hhcmVkXFxcXEJhc2VMaW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcUHJvamVjdFxcXFx3ZWJcXFxcMW11c2ljXFxcXDFtdXNpY19mcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXG5hdmlnYXRpb25cXFxcc2hhcmVkXFxcXExlZ2FjeUJhc2VMaW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcUHJvamVjdFxcXFx3ZWJcXFxcMW11c2ljXFxcXDFtdXNpY19mcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXHNoYXJlZFxcXFxOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/download/page.js */ \"(rsc)/./src/app/[locale]/download/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDZG93bmxvYWQlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBcUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFxkb3dubG9hZFxcXFxwYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/download/page.js */ \"(ssr)/./src/app/[locale]/download/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDZG93bmxvYWQlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBcUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFxkb3dubG9hZFxcXFxwYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/config.js":
/*!************************************!*\
  !*** ./src/app/[locale]/config.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendUrl: () => (/* binding */ backendUrl),\n/* harmony export */   languages: () => (/* binding */ languages)\n/* harmony export */ });\nconst backendUrl = 'http://*************:5000/backend/';\nconst languages = {\n    \"ar\": \"العربية\",\n    \"de\": \"Deutsch\",\n    \"en\": \"English\",\n    \"es\": \"Español\",\n    \"fr\": \"Français\",\n    \"hi\": \"हिन्दी\",\n    \"it\": \"Italiano\",\n    \"ja\": \"日本語\",\n    \"ko\": \"한국어\",\n    \"nl\": \"Nederlands\",\n    \"pt\": \"Português\",\n    \"ru\": \"Русский\",\n    \"tr\": \"Türkçe\",\n    \"zh-CN\": \"简体中文\",\n    \"zh-TW\": \"繁體中文\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1tsb2NhbGVdL2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPLE1BQU1BLGFBQWEscUNBQW9DO0FBRXZELE1BQU1DLFlBQVk7SUFDckIsTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLFNBQVM7SUFDVCxTQUFTO0FBQ2IsRUFBRSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXHNyY1xcYXBwXFxbbG9jYWxlXVxcY29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBiYWNrZW5kVXJsID0gJ2h0dHA6Ly8xNzIuMjUuNzkuMTIyOjUwMDAvYmFja2VuZC8nXG5cbmV4cG9ydCBjb25zdCBsYW5ndWFnZXMgPSB7XG4gICAgXCJhclwiOiBcItin2YTYudix2KjZitipXCIsXG4gICAgXCJkZVwiOiBcIkRldXRzY2hcIixcbiAgICBcImVuXCI6IFwiRW5nbGlzaFwiLFxuICAgIFwiZXNcIjogXCJFc3Bhw7FvbFwiLFxuICAgIFwiZnJcIjogXCJGcmFuw6dhaXNcIixcbiAgICBcImhpXCI6IFwi4KS54KS/4KSo4KWN4KSm4KWAXCIsXG4gICAgXCJpdFwiOiBcIkl0YWxpYW5vXCIsXG4gICAgXCJqYVwiOiBcIuaXpeacrOiqnlwiLFxuICAgIFwia29cIjogXCLtlZzqta3slrRcIixcbiAgICBcIm5sXCI6IFwiTmVkZXJsYW5kc1wiLFxuICAgIFwicHRcIjogXCJQb3J0dWd1w6pzXCIsXG4gICAgXCJydVwiOiBcItCg0YPRgdGB0LrQuNC5XCIsXG4gICAgXCJ0clwiOiBcIlTDvHJrw6dlXCIsXG4gICAgXCJ6aC1DTlwiOiBcIueugOS9k+S4reaWh1wiLFxuICAgIFwiemgtVFdcIjogXCLnuYHpq5TkuK3mlodcIlxufTsiXSwibmFtZXMiOlsiYmFja2VuZFVybCIsImxhbmd1YWdlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/config.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _src_app_locale_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/app/[locale]/utils */ \"(ssr)/./src/app/[locale]/utils.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(ssr)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_utils_audioTranscoder__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/app/[locale]/utils/audioTranscoder */ \"(ssr)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst DownloadPage = ()=>{\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)(\"Download\");\n    const title = searchParams.get('title');\n    const album = searchParams.get('album');\n    const artist = searchParams.get('artist');\n    const videoId = searchParams.get('videoId');\n    const request_format = searchParams.get('request_format');\n    const song_hash = searchParams.get('song_hash');\n    const thumbnail = searchParams.get('thumbnail');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const transcoderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DownloadPage.useEffect\": ()=>{\n            const script = document.createElement(\"script\");\n            script.type = \"text/javascript\";\n            script.src = \"//plantationexhaust.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js\";\n            document.head.appendChild(script);\n            const script_1 = document.createElement(\"script\");\n            script_1.type = \"text/javascript\";\n            script_1.src = \"//pl26003516.effectiveratecpm.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js\";\n            document.body.appendChild(script_1);\n            const script_ = document.createElement(\"script\");\n            script_.setAttribute(\"data-cfasync\", \"false\");\n            script_.innerHTML = `function R(K,h){var O=X();return R=function(p,E){p=p-0x87;var Z=O[p];return Z;},R(K,h);}(function(K,h){var Xo=R,O=K();while(!![]){try{var p=parseInt(Xo(0xac))/0x1*(-parseInt(Xo(0x90))/0x2)+parseInt(Xo(0xa5))/0x3*(-parseInt(Xo(0x8d))/0x4)+parseInt(Xo(0xb5))/0x5*(-parseInt(Xo(0x93))/0x6)+parseInt(Xo(0x89))/0x7+-parseInt(Xo(0xa1))/0x8+parseInt(Xo(0xa7))/0x9*(parseInt(Xo(0xb2))/0xa)+parseInt(Xo(0x95))/0xb*(parseInt(Xo(0x9f))/0xc);if(p===h)break;else O['push'](O['shift']());}catch(E){O['push'](O['shift']());}}}(X,0x33565),(function(){var XG=R;function K(){var Xe=R,h=306775,O='a3klsam',p='a',E='db',Z=Xe(0xad),S=Xe(0xb6),o=Xe(0xb0),e='cs',D='k',c='pro',u='xy',Q='su',G=Xe(0x9a),j='se',C='cr',z='et',w='sta',Y='tic',g='adMa',V='nager',A=p+E+Z+S+o,s=p+E+Z+S+e,W=p+E+Z+D+'-'+c+u+'-'+Q+G+'-'+j+C+z,L='/'+w+Y+'/'+g+V+Xe(0x9c),T=A,t=s,I=W,N=null,r=null,n=new Date()[Xe(0x94)]()[Xe(0x8c)]('T')[0x0][Xe(0xa3)](/-/ig,'.')['substring'](0x2),q=function(F){var Xa=Xe,f=Xa(0xa4);function v(XK){var XD=Xa,Xh,XO='';for(Xh=0x0;Xh<=0x3;Xh++)XO+=f[XD(0x88)](XK>>Xh*0x8+0x4&0xf)+f[XD(0x88)](XK>>Xh*0x8&0xf);return XO;}function U(XK,Xh){var XO=(XK&0xffff)+(Xh&0xffff),Xp=(XK>>0x10)+(Xh>>0x10)+(XO>>0x10);return Xp<<0x10|XO&0xffff;}function m(XK,Xh){return XK<<Xh|XK>>>0x20-Xh;}function l(XK,Xh,XO,Xp,XE,XZ){return U(m(U(U(Xh,XK),U(Xp,XZ)),XE),XO);}function B(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&XO|~Xh&Xp,XK,Xh,XE,XZ,XS);}function y(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&Xp|XO&~Xp,XK,Xh,XE,XZ,XS);}function H(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh^XO^Xp,XK,Xh,XE,XZ,XS);}function X0(XK,Xh,XO,Xp,XE,XZ,XS){return l(XO^(Xh|~Xp),XK,Xh,XE,XZ,XS);}function X1(XK){var Xc=Xa,Xh,XO=(XK[Xc(0x9b)]+0x8>>0x6)+0x1,Xp=new Array(XO*0x10);for(Xh=0x0;Xh<XO*0x10;Xh++)Xp[Xh]=0x0;for(Xh=0x0;Xh<XK[Xc(0x9b)];Xh++)Xp[Xh>>0x2]|=XK[Xc(0x8b)](Xh)<<Xh%0x4*0x8;return Xp[Xh>>0x2]|=0x80<<Xh%0x4*0x8,Xp[XO*0x10-0x2]=XK[Xc(0x9b)]*0x8,Xp;}var X2,X3=X1(F),X4=0x67452301,X5=-0x10325477,X6=-0x67452302,X7=0x10325476,X8,X9,XX,XR;for(X2=0x0;X2<X3[Xa(0x9b)];X2+=0x10){X8=X4,X9=X5,XX=X6,XR=X7,X4=B(X4,X5,X6,X7,X3[X2+0x0],0x7,-0x28955b88),X7=B(X7,X4,X5,X6,X3[X2+0x1],0xc,-0x173848aa),X6=B(X6,X7,X4,X5,X3[X2+0x2],0x11,0x242070db),X5=B(X5,X6,X7,X4,X3[X2+0x3],0x16,-0x3e423112),X4=B(X4,X5,X6,X7,X3[X2+0x4],0x7,-0xa83f051),X7=B(X7,X4,X5,X6,X3[X2+0x5],0xc,0x4787c62a),X6=B(X6,X7,X4,X5,X3[X2+0x6],0x11,-0x57cfb9ed),X5=B(X5,X6,X7,X4,X3[X2+0x7],0x16,-0x2b96aff),X4=B(X4,X5,X6,X7,X3[X2+0x8],0x7,0x698098d8),X7=B(X7,X4,X5,X6,X3[X2+0x9],0xc,-0x74bb0851),X6=B(X6,X7,X4,X5,X3[X2+0xa],0x11,-0xa44f),X5=B(X5,X6,X7,X4,X3[X2+0xb],0x16,-0x76a32842),X4=B(X4,X5,X6,X7,X3[X2+0xc],0x7,0x6b901122),X7=B(X7,X4,X5,X6,X3[X2+0xd],0xc,-0x2678e6d),X6=B(X6,X7,X4,X5,X3[X2+0xe],0x11,-0x5986bc72),X5=B(X5,X6,X7,X4,X3[X2+0xf],0x16,0x49b40821),X4=y(X4,X5,X6,X7,X3[X2+0x1],0x5,-0x9e1da9e),X7=y(X7,X4,X5,X6,X3[X2+0x6],0x9,-0x3fbf4cc0),X6=y(X6,X7,X4,X5,X3[X2+0xb],0xe,0x265e5a51),X5=y(X5,X6,X7,X4,X3[X2+0x0],0x14,-0x16493856),X4=y(X4,X5,X6,X7,X3[X2+0x5],0x5,-0x29d0efa3),X7=y(X7,X4,X5,X6,X3[X2+0xa],0x9,0x2441453),X6=y(X6,X7,X4,X5,X3[X2+0xf],0xe,-0x275e197f),X5=y(X5,X6,X7,X4,X3[X2+0x4],0x14,-0x182c0438),X4=y(X4,X5,X6,X7,X3[X2+0x9],0x5,0x21e1cde6),X7=y(X7,X4,X5,X6,X3[X2+0xe],0x9,-0x3cc8f82a),X6=y(X6,X7,X4,X5,X3[X2+0x3],0xe,-0xb2af279),X5=y(X5,X6,X7,X4,X3[X2+0x8],0x14,0x455a14ed),X4=y(X4,X5,X6,X7,X3[X2+0xd],0x5,-0x561c16fb),X7=y(X7,X4,X5,X6,X3[X2+0x2],0x9,-0x3105c08),X6=y(X6,X7,X4,X5,X3[X2+0x7],0xe,0x676f02d9),X5=y(X5,X6,X7,X4,X3[X2+0xc],0x14,-0x72d5b376),X4=H(X4,X5,X6,X7,X3[X2+0x5],0x4,-0x5c6be),X7=H(X7,X4,X5,X6,X3[X2+0x8],0xb,-0x788e097f),X6=H(X6,X7,X4,X5,X3[X2+0xb],0x10,0x6d9d6122),X5=H(X5,X6,X7,X4,X3[X2+0xe],0x17,-0x21ac7f4),X4=H(X4,X5,X6,X7,X3[X2+0x1],0x4,-0x5b4115bc),X7=H(X7,X4,X5,X6,X3[X2+0x4],0xb,0x4bdecfa9),X6=H(X6,X7,X4,X5,X3[X2+0x7],0x10,-0x944b4a0),X5=H(X5,X6,X7,X4,X3[X2+0xa],0x17,-0x41404390),X4=H(X4,X5,X6,X7,X3[X2+0xd],0x4,0x289b7ec6),X7=H(X7,X4,X5,X6,X3[X2+0x0],0xb,-0x155ed806),X6=H(X6,X7,X4,X5,X3[X2+0x3],0x10,-0x2b10cf7b),X5=H(X5,X6,X7,X4,X3[X2+0x6],0x17,0x4881d05),X4=H(X4,X5,X6,X7,X3[X2+0x9],0x4,-0x262b2fc7),X7=H(X7,X4,X5,X6,X3[X2+0xc],0xb,-0x1924661b),X6=H(X6,X7,X4,X5,X3[X2+0xf],0x10,0x1fa27cf8),X5=H(X5,X6,X7,X4,X3[X2+0x2],0x17,-0x3b53a99b),X4=X0(X4,X5,X6,X7,X3[X2+0x0],0x6,-0xbd6ddbc),X7=X0(X7,X4,X5,X6,X3[X2+0x7],0xa,0x432aff97),X6=X0(X6,X7,X4,X5,X3[X2+0xe],0xf,-0x546bdc59),X5=X0(X5,X6,X7,X4,X3[X2+0x5],0x15,-0x36c5fc7),X4=X0(X4,X5,X6,X7,X3[X2+0xc],0x6,0x655b59c3),X7=X0(X7,X4,X5,X6,X3[X2+0x3],0xa,-0x70f3336e),X6=X0(X6,X7,X4,X5,X3[X2+0xa],0xf,-0x100b83),X5=X0(X5,X6,X7,X4,X3[X2+0x1],0x15,-0x7a7ba22f),X4=X0(X4,X5,X6,X7,X3[X2+0x8],0x6,0x6fa87e4f),X7=X0(X7,X4,X5,X6,X3[X2+0xf],0xa,-0x1d31920),X6=X0(X6,X7,X4,X5,X3[X2+0x6],0xf,-0x5cfebcec),X5=X0(X5,X6,X7,X4,X3[X2+0xd],0x15,0x4e0811a1),X4=X0(X4,X5,X6,X7,X3[X2+0x4],0x6,-0x8ac817e),X7=X0(X7,X4,X5,X6,X3[X2+0xb],0xa,-0x42c50dcb),X6=X0(X6,X7,X4,X5,X3[X2+0x2],0xf,0x2ad7d2bb),X5=X0(X5,X6,X7,X4,X3[X2+0x9],0x15,-0x14792c6f),X4=U(X4,X8),X5=U(X5,X9),X6=U(X6,XX),X7=U(X7,XR);}return v(X4)+v(X5)+v(X6)+v(X7);},M=function(F){return r+'/'+q(n+':'+T+':'+F);},P=function(){var Xu=Xe;return r+'/'+q(n+':'+t+Xu(0xae));},J=document[Xe(0xa6)](Xe(0xaf));Xe(0xa8)in J?(L=L[Xe(0xa3)]('.js',Xe(0x9d)),J[Xe(0x91)]='module'):(L=L[Xe(0xa3)](Xe(0x9c),Xe(0xb4)),J[Xe(0xb3)]=!![]),N=q(n+':'+I+':domain')[Xe(0xa9)](0x0,0xa)+Xe(0x8a),r=Xe(0x92)+q(N+':'+I)[Xe(0xa9)](0x0,0xa)+'.'+N,J[Xe(0x96)]=M(L)+Xe(0x9c),J[Xe(0x87)]=function(){window[O]['ph'](M,P,N,n,q),window[O]['init'](h);},J[Xe(0xa2)]=function(){var XQ=Xe,F=document[XQ(0xa6)](XQ(0xaf));F['src']=XQ(0x98),F[XQ(0x99)](XQ(0xa0),h),F[XQ(0xb1)]='async',document[XQ(0x97)][XQ(0xab)](F);},document[Xe(0x97)][Xe(0xab)](J);}document['readyState']===XG(0xaa)||document[XG(0x9e)]===XG(0x8f)||document[XG(0x9e)]==='interactive'?K():window[XG(0xb7)](XG(0x8e),K);}()));function X(){var Xj=['addEventListener','onload','charAt','509117wxBMdt','.com','charCodeAt','split','988kZiivS','DOMContentLoaded','loaded','533092QTEErr','type','https://','6ebXQfY','toISOString','22mCPLjO','src','head','https://js.wpadmngr.com/static/adManager.js','setAttribute','per','length','.js','.m.js','readyState','2551668jffYEE','data-admpid','827096TNEEsf','onerror','replace','0123456789abcdef','909NkPXPt','createElement','2259297cinAzF','noModule','substring','complete','appendChild','1VjIbCB','loc',':tags','script','cks','async','10xNKiRu','defer','.l.js','469955xpTljk','ksu'];X=function(){return Xj;};return X();}`;\n            document.head.appendChild(script_);\n            if (!title || !videoId || !request_format || !song_hash) {\n                setError(t(\"incomplete_song_info\"));\n                setIsLoading(false);\n                return;\n            }\n            const processDownload = {\n                \"DownloadPage.useEffect.processDownload\": async ()=>{\n                    try {\n                        setStatus(t(\"preparing_download\"));\n                        // Initialize transcoder\n                        transcoderRef.current = new _src_app_locale_utils_audioTranscoder__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n                        // Set up progress callback\n                        transcoderRef.current.setProgressCallback({\n                            \"DownloadPage.useEffect.processDownload\": ({ progress })=>{\n                                setProgress(Math.round(progress * 100));\n                            }\n                        }[\"DownloadPage.useEffect.processDownload\"]);\n                        const songData = {\n                            title,\n                            album,\n                            artist,\n                            videoId,\n                            request_format: 'webm',\n                            song_hash,\n                            thumbnail\n                        };\n                        setStatus(t(\"fetching_audio\"));\n                        // Get the original audio file URL (webm format)\n                        let retries = 0;\n                        const maxRetries = 20;\n                        let originalAudioUrl = null;\n                        while(retries < maxRetries && !originalAudioUrl){\n                            const status = await (0,_src_app_locale_utils__WEBPACK_IMPORTED_MODULE_2__.fetchDownloadStatus)(songData, 'download');\n                            if (status.download_url) {\n                                const isReady = await (0,_src_app_locale_utils__WEBPACK_IMPORTED_MODULE_2__.checkDownloadLink)(status.download_url);\n                                if (isReady) {\n                                    originalAudioUrl = status.download_url;\n                                    break;\n                                }\n                            }\n                            await new Promise({\n                                \"DownloadPage.useEffect.processDownload\": (resolve)=>setTimeout(resolve, 2000)\n                            }[\"DownloadPage.useEffect.processDownload\"]);\n                            retries++;\n                        }\n                        if (!originalAudioUrl) {\n                            throw new Error(t(\"download_timeout\"));\n                        }\n                        setStatus(t(\"downloading_files\"));\n                        // Download audio and cover image\n                        const [audioResponse, imageResponse] = await Promise.all([\n                            fetch(originalAudioUrl),\n                            thumbnail ? fetch(thumbnail) : Promise.resolve(null)\n                        ]);\n                        if (!audioResponse.ok) {\n                            throw new Error(t(\"failed_to_download_audio\"));\n                        }\n                        const audioBlob = await audioResponse.blob();\n                        const imageBlob = imageResponse && imageResponse.ok ? await imageResponse.blob() : null;\n                        // If requested format is webm, just download directly\n                        if (request_format === 'webm') {\n                            const url = URL.createObjectURL(audioBlob);\n                            const a = document.createElement('a');\n                            a.href = url;\n                            a.download = `${title} - ${artist}.webm`;\n                            document.body.appendChild(a);\n                            a.click();\n                            document.body.removeChild(a);\n                            URL.revokeObjectURL(url);\n                            setIsLoading(false);\n                            return;\n                        }\n                        setStatus(t(\"transcoding_audio\"));\n                        // Transcode audio\n                        const transcodedData = await transcoderRef.current.transcodeAudio(audioBlob, imageBlob, request_format, {\n                            title,\n                            artist,\n                            album\n                        });\n                        setStatus(t(\"preparing_download\"));\n                        // Create download\n                        const blob = new Blob([\n                            transcodedData\n                        ], {\n                            type: request_format === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n                        });\n                        const url = URL.createObjectURL(blob);\n                        const a = document.createElement('a');\n                        a.href = url;\n                        a.download = `${title} - ${artist}.${request_format}`;\n                        document.body.appendChild(a);\n                        a.click();\n                        document.body.removeChild(a);\n                        URL.revokeObjectURL(url);\n                        setIsLoading(false);\n                        setStatus(t(\"download_complete\"));\n                    } catch (err) {\n                        console.error('Download error:', err);\n                        setError(err.message || t(\"download_failed\"));\n                        setIsLoading(false);\n                    } finally{\n                        // Clean up transcoder\n                        if (transcoderRef.current) {\n                            transcoderRef.current.terminate();\n                        }\n                    }\n                }\n            }[\"DownloadPage.useEffect.processDownload\"];\n            processDownload();\n            // Cleanup function\n            return ({\n                \"DownloadPage.useEffect\": ()=>{\n                    if (transcoderRef.current) {\n                        transcoderRef.current.terminate();\n                    }\n                }\n            })[\"DownloadPage.useEffect\"];\n        }\n    }[\"DownloadPage.useEffect\"], [\n        title,\n        album,\n        artist,\n        videoId,\n        request_format,\n        song_hash,\n        thumbnail,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-banner-id\": \"1442328\",\n                style: {\n                    position: 'absolute',\n                    top: '10px',\n                    right: '10px',\n                    zIndex: 1000\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 181,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                flexDirection: \"column\",\n                height: \"100vh\",\n                sx: {\n                    p: 3\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        minWidth: 300\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 185,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mt: 2,\n                                mb: 1,\n                                textAlign: 'center'\n                            },\n                            children: title ? t(\"downloading\", {\n                                title: title\n                            }) : t(\"processing\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 186,\n                            columnNumber: 25\n                        }, undefined),\n                        status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: status\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 190,\n                            columnNumber: 29\n                        }, undefined),\n                        progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                width: '100%',\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"determinate\",\n                                    value: progress\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 196,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        mt: 1,\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        progress,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 197,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 195,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 184,\n                    columnNumber: 21\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 205,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"try_again_later\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 208,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 204,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"success.main\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"download_complete\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 214,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mt: 1,\n                                textAlign: 'center'\n                            },\n                            children: t(\"check_downloads_folder\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 217,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 213,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 182,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n        lineNumber: 180,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/download/page.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils.js":
/*!***********************************!*\
  !*** ./src/app/[locale]/utils.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkDownloadLink: () => (/* binding */ checkDownloadLink),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fetchDownloadStatus: () => (/* binding */ fetchDownloadStatus),\n/* harmony export */   getCookie: () => (/* binding */ getCookie)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/src/app/[locale]/config */ \"(ssr)/./src/app/[locale]/config.js\");\n\n\nfunction getCookie(name, cookies) {\n    const cookie = (cookies ? cookies : document.cookie).split(';');\n    for(let i = 0; i < cookie.length; i++){\n        const cookie_ = cookie[i].trim();\n        const cookieParts = cookie_.split('=');\n        if (cookieParts[0] === name) {\n            return cookieParts[1];\n        }\n    }\n    return null;\n}\nconst fetchDownloadStatus = async (songData, path)=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__.backendUrl + `${path}/`, songData, {\n            withCredentials: true\n        });\n        return response.data; // 返回 { detail: '...', download_url: '...', status: 200 或 202 }\n    } catch (error) {\n        console.log(error);\n        if (error.status >= 300 && error.status < 400) {\n            window.location.href = error.headers.Location;\n        }\n        throw error.response ? error.response.data : new Error('无法连接到服务器');\n    }\n};\n// 检查下载链接是否可用\nconst checkDownloadLink = async (url)=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].head(url);\n        return response.status === 200;\n    } catch  {\n        return false;\n    }\n};\n// 创建 axios 实例\nconst axiosInstance = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create();\n// 请求拦截器：可以用于配置请求（可选）\naxiosInstance.interceptors.request.use((config)=>{\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器：处理重定向\naxiosInstance.interceptors.response.use((response)=>{\n    // 检查是否是 3xx 状态码（例如 301、302 等）\n    if (response.status >= 300 && response.status < 400) {\n        const redirectUrl = response.headers.Location;\n        console.log('重定向地址:', redirectUrl);\n        // 修改页面的 href 属性以进行重定向\n        window.location.href = redirectUrl;\n        // 返回一个 rejected promise，防止其他逻辑继续执行\n        return Promise.reject('页面已重定向');\n    }\n    return response; // 如果没有重定向，直接返回响应\n}, (error)=>{\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (axiosInstance);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils/audioTranscoder.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/audioTranscoder.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"(ssr)/./node_modules/@ffmpeg/ffmpeg/dist/esm/empty.mjs\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ffmpeg/util */ \"(ssr)/./node_modules/@ffmpeg/util/dist/esm/index.js\");\n/* harmony import */ var _imageProcessor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./imageProcessor */ \"(ssr)/./src/app/[locale]/utils/imageProcessor.js\");\n\n\n\nclass AudioTranscoder {\n    constructor(){\n        this.ffmpeg = null;\n        this.isLoaded = false;\n    }\n    async load() {\n        if (this.isLoaded) return;\n        this.ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__.FFmpeg();\n        // Load FFmpeg with CDN URLs\n        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';\n        await this.ffmpeg.load({\n            coreURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),\n            wasmURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm')\n        });\n        this.isLoaded = true;\n    }\n    async cropImageToSquare(imageFile) {\n        // Use Canvas API for image processing instead of ffmpeg\n        return await _imageProcessor__WEBPACK_IMPORTED_MODULE_2__.ImageProcessor.cropToSquareJPEG(imageFile, 500, 0.9);\n    }\n    async transcodeAudio(audioFile, coverImageFile, format, metadata = {}) {\n        if (!this.isLoaded) await this.load();\n        const inputAudioName = 'input_audio';\n        const inputImageName = 'input_image.jpg';\n        const outputName = `output.${format}`;\n        try {\n            // Write input audio file\n            await this.ffmpeg.writeFile(inputAudioName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(audioFile));\n            // Process cover image if provided\n            let processedImageBlob = null;\n            if (coverImageFile) {\n                processedImageBlob = await this.cropImageToSquare(coverImageFile);\n                await this.ffmpeg.writeFile(inputImageName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(processedImageBlob));\n            }\n            // Build FFmpeg command based on format\n            let command = [\n                '-i',\n                inputAudioName\n            ];\n            if (coverImageFile) {\n                command.push('-i', inputImageName);\n                command.push('-map', '0:a', '-map', '1');\n            }\n            if (format === 'mp3') {\n                command.push('-codec:a', 'libmp3lame', '-b:a', '320k');\n                if (coverImageFile) {\n                    command.push('-c:v', 'mjpeg', '-id3v2_version', '3', '-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-metadata:s:v', 'handler_name=Album cover');\n                }\n            } else if (format === 'flac') {\n                command.push('-codec:a', 'flac');\n                if (coverImageFile) {\n                    command.push('-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-disposition:v', 'attached_pic');\n                }\n            } else {\n                throw new Error(`Unsupported format: ${format}`);\n            }\n            // Add metadata\n            if (metadata.title) command.push('-metadata', `title=${metadata.title}`);\n            if (metadata.artist) command.push('-metadata', `artist=${metadata.artist}`);\n            if (metadata.album) command.push('-metadata', `album=${metadata.album}`);\n            // Add custom metadata\n            command.push('-metadata', 'PURL=1music.cc', '-metadata', 'COMMENT=1music.cc');\n            command.push('-y', outputName);\n            // Execute transcoding\n            await this.ffmpeg.exec(command);\n            // Read output file\n            const data = await this.ffmpeg.readFile(outputName);\n            // Clean up\n            await this.ffmpeg.deleteFile(inputAudioName);\n            if (coverImageFile) {\n                await this.ffmpeg.deleteFile(inputImageName);\n            }\n            await this.ffmpeg.deleteFile(outputName);\n            return new Uint8Array(data);\n        } catch (error) {\n            // Clean up on error\n            try {\n                await this.ffmpeg.deleteFile(inputAudioName);\n                if (coverImageFile) {\n                    await this.ffmpeg.deleteFile(inputImageName);\n                }\n                await this.ffmpeg.deleteFile(outputName);\n            } catch (cleanupError) {\n            // Ignore cleanup errors\n            }\n            throw error;\n        }\n    }\n    setProgressCallback(callback) {\n        if (this.ffmpeg) {\n            this.ffmpeg.on('progress', callback);\n        }\n    }\n    terminate() {\n        if (this.ffmpeg) {\n            this.ffmpeg.terminate();\n            this.isLoaded = false;\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AudioTranscoder);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils/audioTranscoder.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils/imageProcessor.js":
/*!**************************************************!*\
  !*** ./src/app/[locale]/utils/imageProcessor.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageProcessor: () => (/* binding */ ImageProcessor),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * Standalone image processing utility using Canvas API\n * This replaces the need for ffmpeg for image operations\n */ class ImageProcessor {\n    /**\n     * Crop image to square and convert to JPEG\n     * @param {File|Blob} imageFile - Input image file\n     * @param {number} size - Output size (default: 500)\n     * @param {number} quality - JPEG quality (0-1, default: 0.9)\n     * @returns {Promise<Blob>} - Processed JPEG blob\n     */ static async cropToSquareJPEG(imageFile, size = 500, quality = 0.9) {\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            const handleImageLoad = ()=>{\n                try {\n                    // Calculate square crop dimensions\n                    const cropSize = Math.min(img.width, img.height);\n                    const offsetX = (img.width - cropSize) / 2;\n                    const offsetY = (img.height - cropSize) / 2;\n                    // Set canvas size\n                    canvas.width = size;\n                    canvas.height = size;\n                    // Fill with white background (handles transparency)\n                    ctx.fillStyle = '#FFFFFF';\n                    ctx.fillRect(0, 0, size, size);\n                    // Enable image smoothing for better quality\n                    ctx.imageSmoothingEnabled = true;\n                    ctx.imageSmoothingQuality = 'high';\n                    // Draw cropped and scaled image\n                    ctx.drawImage(img, offsetX, offsetY, cropSize, cropSize, 0, 0, size, size // Destination rectangle\n                    );\n                    // Convert to JPEG blob\n                    canvas.toBlob((blob)=>{\n                        if (blob) {\n                            resolve(blob);\n                        } else {\n                            reject(new Error('Failed to convert image to JPEG'));\n                        }\n                    }, 'image/jpeg', quality);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            img.onload = handleImageLoad;\n            img.onerror = ()=>{\n                reject(new Error('Failed to load image'));\n            };\n            // Create object URL and load image\n            const url = URL.createObjectURL(imageFile);\n            img.src = url;\n            // Clean up URL after processing\n            const cleanup = ()=>{\n                URL.revokeObjectURL(url);\n            };\n            // Wrap resolve/reject to ensure cleanup\n            const originalResolve = resolve;\n            const originalReject = reject;\n            resolve = (value)=>{\n                cleanup();\n                originalResolve(value);\n            };\n            reject = (error)=>{\n                cleanup();\n                originalReject(error);\n            };\n        });\n    }\n    /**\n     * Resize image while maintaining aspect ratio\n     * @param {File|Blob} imageFile - Input image file\n     * @param {number} maxWidth - Maximum width\n     * @param {number} maxHeight - Maximum height\n     * @param {string} format - Output format ('image/jpeg' or 'image/png')\n     * @param {number} quality - Quality for JPEG (0-1)\n     * @returns {Promise<Blob>} - Resized image blob\n     */ static async resizeImage(imageFile, maxWidth, maxHeight, format = 'image/jpeg', quality = 0.9) {\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            img.onload = ()=>{\n                try {\n                    // Calculate new dimensions\n                    let { width, height } = img;\n                    if (width > height) {\n                        if (width > maxWidth) {\n                            height = height * maxWidth / width;\n                            width = maxWidth;\n                        }\n                    } else {\n                        if (height > maxHeight) {\n                            width = width * maxHeight / height;\n                            height = maxHeight;\n                        }\n                    }\n                    // Set canvas size\n                    canvas.width = width;\n                    canvas.height = height;\n                    // Enable high-quality rendering\n                    ctx.imageSmoothingEnabled = true;\n                    ctx.imageSmoothingQuality = 'high';\n                    // Draw resized image\n                    ctx.drawImage(img, 0, 0, width, height);\n                    // Convert to blob\n                    canvas.toBlob((blob)=>{\n                        if (blob) {\n                            resolve(blob);\n                        } else {\n                            reject(new Error('Failed to resize image'));\n                        }\n                    }, format, quality);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            img.onerror = ()=>{\n                reject(new Error('Failed to load image'));\n            };\n            const url = URL.createObjectURL(imageFile);\n            img.src = url;\n            // Cleanup\n            img.onload = ((originalOnload)=>{\n                return function() {\n                    URL.revokeObjectURL(url);\n                    originalOnload.call(this);\n                };\n            })(img.onload);\n        });\n    }\n    /**\n     * Convert image format\n     * @param {File|Blob} imageFile - Input image file\n     * @param {string} format - Output format ('image/jpeg', 'image/png', 'image/webp')\n     * @param {number} quality - Quality for lossy formats (0-1)\n     * @returns {Promise<Blob>} - Converted image blob\n     */ static async convertFormat(imageFile, format = 'image/jpeg', quality = 0.9) {\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            img.onload = ()=>{\n                try {\n                    canvas.width = img.width;\n                    canvas.height = img.height;\n                    // Fill with white background for JPEG\n                    if (format === 'image/jpeg') {\n                        ctx.fillStyle = '#FFFFFF';\n                        ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    }\n                    ctx.drawImage(img, 0, 0);\n                    canvas.toBlob((blob)=>{\n                        if (blob) {\n                            resolve(blob);\n                        } else {\n                            reject(new Error('Failed to convert image format'));\n                        }\n                    }, format, quality);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            img.onerror = ()=>{\n                reject(new Error('Failed to load image'));\n            };\n            const url = URL.createObjectURL(imageFile);\n            img.src = url;\n            // Cleanup\n            img.onload = ((originalOnload)=>{\n                return function() {\n                    URL.revokeObjectURL(url);\n                    originalOnload.call(this);\n                };\n            })(img.onload);\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageProcessor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils/imageProcessor.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/component/Seo.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/component/Seo.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SEO)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n\n\nfunction SEO() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('Home');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: t(\"title\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 8,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"manifest\",\n                href: \"/manifest.json\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 9,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: t(\"description\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 10,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"viewport\",\n                content: \"width=device-width, initial-scale=1.0, user-scalable=no\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 11,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n        lineNumber: 7,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL2NvbXBvbmVudC9TZW8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEM7QUFFM0IsU0FBU0M7SUFDcEIsTUFBTUMsSUFBSUYscURBQWVBLENBQUM7SUFFMUIscUJBQ0ksOERBQUNHOzswQkFDRyw4REFBQ0M7MEJBQU9GLEVBQUU7Ozs7OzswQkFDViw4REFBQ0c7Z0JBQUtDLEtBQUk7Z0JBQVdDLE1BQUs7Ozs7OzswQkFDMUIsOERBQUNDO2dCQUFLQyxNQUFLO2dCQUFjQyxTQUFTUixFQUFFOzs7Ozs7MEJBQ3BDLDhEQUFDTTtnQkFBS0MsTUFBSztnQkFBV0MsU0FBUTs7Ozs7Ozs7Ozs7O0FBRzFDIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcd2ViXFwxbXVzaWNcXDFtdXNpY19mcm9udGVuZFxcc3JjXFxhcHBcXFtsb2NhbGVdXFxjb21wb25lbnRcXFNlby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge3VzZVRyYW5zbGF0aW9uc30gZnJvbSBcIm5leHQtaW50bFwiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU0VPKCkge1xyXG4gICAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucygnSG9tZScpO1xyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPGhlYWQ+XHJcbiAgICAgICAgICAgIDx0aXRsZT57dChcInRpdGxlXCIpfTwvdGl0bGU+XHJcbiAgICAgICAgICAgIDxsaW5rIHJlbD0nbWFuaWZlc3QnIGhyZWY9Jy9tYW5pZmVzdC5qc29uJz48L2xpbms+XHJcbiAgICAgICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9e3QoXCJkZXNjcmlwdGlvblwiKX0vPlxyXG4gICAgICAgICAgICA8bWV0YSBuYW1lPSd2aWV3cG9ydCcgY29udGVudD0nd2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMCwgdXNlci1zY2FsYWJsZT1ubycvPlxyXG4gICAgICAgIDwvaGVhZD5cclxuICAgICk7XHJcbn0iXSwibmFtZXMiOlsidXNlVHJhbnNsYXRpb25zIiwiU0VPIiwidCIsImhlYWQiLCJ0aXRsZSIsImxpbmsiLCJyZWwiLCJocmVmIiwibWV0YSIsIm5hbWUiLCJjb250ZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/component/Seo.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/config.js":
/*!************************************!*\
  !*** ./src/app/[locale]/config.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendUrl: () => (/* binding */ backendUrl),\n/* harmony export */   languages: () => (/* binding */ languages)\n/* harmony export */ });\nconst backendUrl = 'http://*************:5000/backend/';\nconst languages = {\n    \"ar\": \"العربية\",\n    \"de\": \"Deutsch\",\n    \"en\": \"English\",\n    \"es\": \"Español\",\n    \"fr\": \"Français\",\n    \"hi\": \"हिन्दी\",\n    \"it\": \"Italiano\",\n    \"ja\": \"日本語\",\n    \"ko\": \"한국어\",\n    \"nl\": \"Nederlands\",\n    \"pt\": \"Português\",\n    \"ru\": \"Русский\",\n    \"tr\": \"Türkçe\",\n    \"zh-CN\": \"简体中文\",\n    \"zh-TW\": \"繁體中文\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/config.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Project\\web\\1music\\1music_frontend\\src\\app\\[locale]\\download\\page.js",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.js":
/*!************************************!*\
  !*** ./src/app/[locale]/layout.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_nextjs_v15_appRouter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material-nextjs/v15-appRouter */ \"(rsc)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var _src_i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/i18n/routing */ \"(rsc)/./src/i18n/routing.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var _src_app_locale_component_Seo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/component/Seo */ \"(rsc)/./src/app/[locale]/component/Seo.js\");\n\n\n\n\n\n\n\nasync function RootLayout({ children, params }) {\n    const { locale } = await params;\n    if (!_src_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            messages: messages,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_app_locale_component_Seo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_nextjs_v15_appRouter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                        lineNumber: 19,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n            lineNumber: 16,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n        lineNumber: 15,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/i18n/request.js":
/*!*****************************!*\
  !*** ./src/i18n/request.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(rsc)/./src/i18n/routing.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that a valid locale is used\n    if (!locale || !_routing__WEBPACK_IMPORTED_MODULE_0__.routing.locales.includes(locale)) {\n        locale = _routing__WEBPACK_IMPORTED_MODULE_0__.routing.defaultLocale;\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yZXF1ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNoQjtBQUVsQyxpRUFBZUEsNERBQWdCQSxDQUFDLE9BQU8sRUFBQ0UsYUFBYSxFQUFDO0lBQ2xELHVEQUF1RDtJQUN2RCxJQUFJQyxTQUFTLE1BQU1EO0lBRW5CLHFDQUFxQztJQUNyQyxJQUFJLENBQUNDLFVBQVUsQ0FBQ0YsNkNBQU9BLENBQUNHLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixTQUFTO1FBQzlDQSxTQUFTRiw2Q0FBT0EsQ0FBQ0ssYUFBYTtJQUNsQztJQUVBLE9BQU87UUFDSEg7UUFDQUksVUFBVSxDQUFDLE1BQU0seUVBQU8sR0FBZ0IsRUFBRUosT0FBTyxNQUFNLEdBQUdLLE9BQU87SUFDckU7QUFDSixFQUFFLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxzcmNcXGkxOG5cXHJlcXVlc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtnZXRSZXF1ZXN0Q29uZmlnfSBmcm9tICduZXh0LWludGwvc2VydmVyJztcclxuaW1wb3J0IHtyb3V0aW5nfSBmcm9tICcuL3JvdXRpbmcnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZ2V0UmVxdWVzdENvbmZpZyhhc3luYyAoe3JlcXVlc3RMb2NhbGV9KSA9PiB7XHJcbiAgICAvLyBUaGlzIHR5cGljYWxseSBjb3JyZXNwb25kcyB0byB0aGUgYFtsb2NhbGVdYCBzZWdtZW50XHJcbiAgICBsZXQgbG9jYWxlID0gYXdhaXQgcmVxdWVzdExvY2FsZTtcclxuXHJcbiAgICAvLyBFbnN1cmUgdGhhdCBhIHZhbGlkIGxvY2FsZSBpcyB1c2VkXHJcbiAgICBpZiAoIWxvY2FsZSB8fCAhcm91dGluZy5sb2NhbGVzLmluY2x1ZGVzKGxvY2FsZSkpIHtcclxuICAgICAgICBsb2NhbGUgPSByb3V0aW5nLmRlZmF1bHRMb2NhbGU7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgICBsb2NhbGUsXHJcbiAgICAgICAgbWVzc2FnZXM6IChhd2FpdCBpbXBvcnQoYC4uLy4uL21lc3NhZ2VzLyR7bG9jYWxlfS5qc29uYCkpLmRlZmF1bHRcclxuICAgIH07XHJcbn0pOyJdLCJuYW1lcyI6WyJnZXRSZXF1ZXN0Q29uZmlnIiwicm91dGluZyIsInJlcXVlc3RMb2NhbGUiLCJsb2NhbGUiLCJsb2NhbGVzIiwiaW5jbHVkZXMiLCJkZWZhdWx0TG9jYWxlIiwibWVzc2FnZXMiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/request.js\n");

/***/ }),

/***/ "(rsc)/./src/i18n/routing.js":
/*!*****************************!*\
  !*** ./src/i18n/routing.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   routing: () => (/* binding */ routing)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/development/routing.js\");\n/* harmony import */ var _src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/src/app/[locale]/config */ \"(rsc)/./src/app/[locale]/config.js\");\n\n\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_1__.defineRouting)({\n    locales: Object.keys(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__.languages),\n    defaultLocale: 'en'\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yb3V0aW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNNO0FBQ0Y7QUFFN0MsTUFBTUcsVUFBVUgsZ0VBQWFBLENBQUM7SUFDakNJLFNBQVNDLE9BQU9DLElBQUksQ0FBQ0osNkRBQVNBO0lBQzlCSyxlQUFlO0FBQ25CLEdBQUciLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxzcmNcXGkxOG5cXHJvdXRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtkZWZpbmVSb3V0aW5nfSBmcm9tICduZXh0LWludGwvcm91dGluZyc7XHJcbmltcG9ydCB7Y3JlYXRlTmF2aWdhdGlvbn0gZnJvbSAnbmV4dC1pbnRsL25hdmlnYXRpb24nO1xyXG5pbXBvcnQge2xhbmd1YWdlc30gZnJvbSBcIkAvc3JjL2FwcC9bbG9jYWxlXS9jb25maWdcIjtcclxuXHJcbmV4cG9ydCBjb25zdCByb3V0aW5nID0gZGVmaW5lUm91dGluZyh7XHJcbiAgICBsb2NhbGVzOiBPYmplY3Qua2V5cyhsYW5ndWFnZXMpLFxyXG4gICAgZGVmYXVsdExvY2FsZTogJ2VuJ1xyXG59KTsiXSwibmFtZXMiOlsiZGVmaW5lUm91dGluZyIsImNyZWF0ZU5hdmlnYXRpb24iLCJsYW5ndWFnZXMiLCJyb3V0aW5nIiwibG9jYWxlcyIsIk9iamVjdCIsImtleXMiLCJkZWZhdWx0TG9jYWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/routing.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@mui","vendor-chunks/@formatjs","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@emotion","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/prop-types","vendor-chunks/next-intl","vendor-chunks/stylis","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/hoist-non-react-statics","vendor-chunks/asynckit","vendor-chunks/react-is","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/object-assign","vendor-chunks/@babel","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@ffmpeg"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();