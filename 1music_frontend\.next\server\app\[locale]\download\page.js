/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/download/page";
exports.ids = ["app/[locale]/download/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ar.json": [
		"(rsc)/./messages/ar.json",
		"_rsc_messages_ar_json"
	],
	"./de.json": [
		"(rsc)/./messages/de.json",
		"_rsc_messages_de_json"
	],
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	],
	"./fr.json": [
		"(rsc)/./messages/fr.json",
		"_rsc_messages_fr_json"
	],
	"./hi.json": [
		"(rsc)/./messages/hi.json",
		"_rsc_messages_hi_json"
	],
	"./it.json": [
		"(rsc)/./messages/it.json",
		"_rsc_messages_it_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	],
	"./ko.json": [
		"(rsc)/./messages/ko.json",
		"_rsc_messages_ko_json"
	],
	"./nl.json": [
		"(rsc)/./messages/nl.json",
		"_rsc_messages_nl_json"
	],
	"./pt.json": [
		"(rsc)/./messages/pt.json",
		"_rsc_messages_pt_json"
	],
	"./ru.json": [
		"(rsc)/./messages/ru.json",
		"_rsc_messages_ru_json"
	],
	"./tr.json": [
		"(rsc)/./messages/tr.json",
		"_rsc_messages_tr_json"
	],
	"./zh-CN.json": [
		"(rsc)/./messages/zh-CN.json",
		"_rsc_messages_zh-CN_json"
	],
	"./zh-TW.json": [
		"(rsc)/./messages/zh-TW.json",
		"_rsc_messages_zh-TW_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.js */ \"(rsc)/./src/app/[locale]/layout.js\"));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/download/page.js */ \"(rsc)/./src/app/[locale]/download/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'download',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module3, \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\"],\n        \n      }\n      ]\n      },\n        {\n        'not-found': [module0, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module1, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module2, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/download/page\",\n        pathname: \"/[locale]/download\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js */ \"(rsc)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js */ \"(ssr)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/download/page.js */ \"(rsc)/./src/app/[locale]/download/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDZG93bmxvYWQlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBcUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFxkb3dubG9hZFxcXFxwYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/download/page.js */ \"(ssr)/./src/app/[locale]/download/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDZG93bmxvYWQlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBcUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFxkb3dubG9hZFxcXFxwYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/config.js":
/*!************************************!*\
  !*** ./src/app/[locale]/config.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendUrl: () => (/* binding */ backendUrl),\n/* harmony export */   languages: () => (/* binding */ languages)\n/* harmony export */ });\nconst backendUrl = 'http://*************:5000/backend/';\nconst languages = {\n    \"ar\": \"العربية\",\n    \"de\": \"Deutsch\",\n    \"en\": \"English\",\n    \"es\": \"Español\",\n    \"fr\": \"Français\",\n    \"hi\": \"हिन्दी\",\n    \"it\": \"Italiano\",\n    \"ja\": \"日本語\",\n    \"ko\": \"한국어\",\n    \"nl\": \"Nederlands\",\n    \"pt\": \"Português\",\n    \"ru\": \"Русский\",\n    \"tr\": \"Türkçe\",\n    \"zh-CN\": \"简体中文\",\n    \"zh-TW\": \"繁體中文\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1tsb2NhbGVdL2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPLE1BQU1BLGFBQWEscUNBQW9DO0FBRXZELE1BQU1DLFlBQVk7SUFDckIsTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLFNBQVM7SUFDVCxTQUFTO0FBQ2IsRUFBRSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXHNyY1xcYXBwXFxbbG9jYWxlXVxcY29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBiYWNrZW5kVXJsID0gJ2h0dHA6Ly8xNzIuMjUuNzkuMTIyOjUwMDAvYmFja2VuZC8nXG5cbmV4cG9ydCBjb25zdCBsYW5ndWFnZXMgPSB7XG4gICAgXCJhclwiOiBcItin2YTYudix2KjZitipXCIsXG4gICAgXCJkZVwiOiBcIkRldXRzY2hcIixcbiAgICBcImVuXCI6IFwiRW5nbGlzaFwiLFxuICAgIFwiZXNcIjogXCJFc3Bhw7FvbFwiLFxuICAgIFwiZnJcIjogXCJGcmFuw6dhaXNcIixcbiAgICBcImhpXCI6IFwi4KS54KS/4KSo4KWN4KSm4KWAXCIsXG4gICAgXCJpdFwiOiBcIkl0YWxpYW5vXCIsXG4gICAgXCJqYVwiOiBcIuaXpeacrOiqnlwiLFxuICAgIFwia29cIjogXCLtlZzqta3slrRcIixcbiAgICBcIm5sXCI6IFwiTmVkZXJsYW5kc1wiLFxuICAgIFwicHRcIjogXCJQb3J0dWd1w6pzXCIsXG4gICAgXCJydVwiOiBcItCg0YPRgdGB0LrQuNC5XCIsXG4gICAgXCJ0clwiOiBcIlTDvHJrw6dlXCIsXG4gICAgXCJ6aC1DTlwiOiBcIueugOS9k+S4reaWh1wiLFxuICAgIFwiemgtVFdcIjogXCLnuYHpq5TkuK3mlodcIlxufTsiXSwibmFtZXMiOlsiYmFja2VuZFVybCIsImxhbmd1YWdlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/config.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(ssr)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/utils/downloadManager */ \"(ssr)/./src/app/[locale]/utils/downloadManager.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst DownloadPage = ()=>{\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"Download\");\n    const title = searchParams.get('title');\n    const album = searchParams.get('album');\n    const artist = searchParams.get('artist');\n    const videoId = searchParams.get('videoId');\n    const request_format = searchParams.get('request_format');\n    const song_hash = searchParams.get('song_hash');\n    const thumbnail = searchParams.get('thumbnail');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const downloadManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DownloadPage.useEffect\": ()=>{\n            const script = document.createElement(\"script\");\n            script.type = \"text/javascript\";\n            script.src = \"//plantationexhaust.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js\";\n            document.head.appendChild(script);\n            const script_1 = document.createElement(\"script\");\n            script_1.type = \"text/javascript\";\n            script_1.src = \"//pl26003516.effectiveratecpm.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js\";\n            document.body.appendChild(script_1);\n            const script_ = document.createElement(\"script\");\n            script_.setAttribute(\"data-cfasync\", \"false\");\n            script_.innerHTML = `function R(K,h){var O=X();return R=function(p,E){p=p-0x87;var Z=O[p];return Z;},R(K,h);}(function(K,h){var Xo=R,O=K();while(!![]){try{var p=parseInt(Xo(0xac))/0x1*(-parseInt(Xo(0x90))/0x2)+parseInt(Xo(0xa5))/0x3*(-parseInt(Xo(0x8d))/0x4)+parseInt(Xo(0xb5))/0x5*(-parseInt(Xo(0x93))/0x6)+parseInt(Xo(0x89))/0x7+-parseInt(Xo(0xa1))/0x8+parseInt(Xo(0xa7))/0x9*(parseInt(Xo(0xb2))/0xa)+parseInt(Xo(0x95))/0xb*(parseInt(Xo(0x9f))/0xc);if(p===h)break;else O['push'](O['shift']());}catch(E){O['push'](O['shift']());}}}(X,0x33565),(function(){var XG=R;function K(){var Xe=R,h=306775,O='a3klsam',p='a',E='db',Z=Xe(0xad),S=Xe(0xb6),o=Xe(0xb0),e='cs',D='k',c='pro',u='xy',Q='su',G=Xe(0x9a),j='se',C='cr',z='et',w='sta',Y='tic',g='adMa',V='nager',A=p+E+Z+S+o,s=p+E+Z+S+e,W=p+E+Z+D+'-'+c+u+'-'+Q+G+'-'+j+C+z,L='/'+w+Y+'/'+g+V+Xe(0x9c),T=A,t=s,I=W,N=null,r=null,n=new Date()[Xe(0x94)]()[Xe(0x8c)]('T')[0x0][Xe(0xa3)](/-/ig,'.')['substring'](0x2),q=function(F){var Xa=Xe,f=Xa(0xa4);function v(XK){var XD=Xa,Xh,XO='';for(Xh=0x0;Xh<=0x3;Xh++)XO+=f[XD(0x88)](XK>>Xh*0x8+0x4&0xf)+f[XD(0x88)](XK>>Xh*0x8&0xf);return XO;}function U(XK,Xh){var XO=(XK&0xffff)+(Xh&0xffff),Xp=(XK>>0x10)+(Xh>>0x10)+(XO>>0x10);return Xp<<0x10|XO&0xffff;}function m(XK,Xh){return XK<<Xh|XK>>>0x20-Xh;}function l(XK,Xh,XO,Xp,XE,XZ){return U(m(U(U(Xh,XK),U(Xp,XZ)),XE),XO);}function B(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&XO|~Xh&Xp,XK,Xh,XE,XZ,XS);}function y(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&Xp|XO&~Xp,XK,Xh,XE,XZ,XS);}function H(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh^XO^Xp,XK,Xh,XE,XZ,XS);}function X0(XK,Xh,XO,Xp,XE,XZ,XS){return l(XO^(Xh|~Xp),XK,Xh,XE,XZ,XS);}function X1(XK){var Xc=Xa,Xh,XO=(XK[Xc(0x9b)]+0x8>>0x6)+0x1,Xp=new Array(XO*0x10);for(Xh=0x0;Xh<XO*0x10;Xh++)Xp[Xh]=0x0;for(Xh=0x0;Xh<XK[Xc(0x9b)];Xh++)Xp[Xh>>0x2]|=XK[Xc(0x8b)](Xh)<<Xh%0x4*0x8;return Xp[Xh>>0x2]|=0x80<<Xh%0x4*0x8,Xp[XO*0x10-0x2]=XK[Xc(0x9b)]*0x8,Xp;}var X2,X3=X1(F),X4=0x67452301,X5=-0x10325477,X6=-0x67452302,X7=0x10325476,X8,X9,XX,XR;for(X2=0x0;X2<X3[Xa(0x9b)];X2+=0x10){X8=X4,X9=X5,XX=X6,XR=X7,X4=B(X4,X5,X6,X7,X3[X2+0x0],0x7,-0x28955b88),X7=B(X7,X4,X5,X6,X3[X2+0x1],0xc,-0x173848aa),X6=B(X6,X7,X4,X5,X3[X2+0x2],0x11,0x242070db),X5=B(X5,X6,X7,X4,X3[X2+0x3],0x16,-0x3e423112),X4=B(X4,X5,X6,X7,X3[X2+0x4],0x7,-0xa83f051),X7=B(X7,X4,X5,X6,X3[X2+0x5],0xc,0x4787c62a),X6=B(X6,X7,X4,X5,X3[X2+0x6],0x11,-0x57cfb9ed),X5=B(X5,X6,X7,X4,X3[X2+0x7],0x16,-0x2b96aff),X4=B(X4,X5,X6,X7,X3[X2+0x8],0x7,0x698098d8),X7=B(X7,X4,X5,X6,X3[X2+0x9],0xc,-0x74bb0851),X6=B(X6,X7,X4,X5,X3[X2+0xa],0x11,-0xa44f),X5=B(X5,X6,X7,X4,X3[X2+0xb],0x16,-0x76a32842),X4=B(X4,X5,X6,X7,X3[X2+0xc],0x7,0x6b901122),X7=B(X7,X4,X5,X6,X3[X2+0xd],0xc,-0x2678e6d),X6=B(X6,X7,X4,X5,X3[X2+0xe],0x11,-0x5986bc72),X5=B(X5,X6,X7,X4,X3[X2+0xf],0x16,0x49b40821),X4=y(X4,X5,X6,X7,X3[X2+0x1],0x5,-0x9e1da9e),X7=y(X7,X4,X5,X6,X3[X2+0x6],0x9,-0x3fbf4cc0),X6=y(X6,X7,X4,X5,X3[X2+0xb],0xe,0x265e5a51),X5=y(X5,X6,X7,X4,X3[X2+0x0],0x14,-0x16493856),X4=y(X4,X5,X6,X7,X3[X2+0x5],0x5,-0x29d0efa3),X7=y(X7,X4,X5,X6,X3[X2+0xa],0x9,0x2441453),X6=y(X6,X7,X4,X5,X3[X2+0xf],0xe,-0x275e197f),X5=y(X5,X6,X7,X4,X3[X2+0x4],0x14,-0x182c0438),X4=y(X4,X5,X6,X7,X3[X2+0x9],0x5,0x21e1cde6),X7=y(X7,X4,X5,X6,X3[X2+0xe],0x9,-0x3cc8f82a),X6=y(X6,X7,X4,X5,X3[X2+0x3],0xe,-0xb2af279),X5=y(X5,X6,X7,X4,X3[X2+0x8],0x14,0x455a14ed),X4=y(X4,X5,X6,X7,X3[X2+0xd],0x5,-0x561c16fb),X7=y(X7,X4,X5,X6,X3[X2+0x2],0x9,-0x3105c08),X6=y(X6,X7,X4,X5,X3[X2+0x7],0xe,0x676f02d9),X5=y(X5,X6,X7,X4,X3[X2+0xc],0x14,-0x72d5b376),X4=H(X4,X5,X6,X7,X3[X2+0x5],0x4,-0x5c6be),X7=H(X7,X4,X5,X6,X3[X2+0x8],0xb,-0x788e097f),X6=H(X6,X7,X4,X5,X3[X2+0xb],0x10,0x6d9d6122),X5=H(X5,X6,X7,X4,X3[X2+0xe],0x17,-0x21ac7f4),X4=H(X4,X5,X6,X7,X3[X2+0x1],0x4,-0x5b4115bc),X7=H(X7,X4,X5,X6,X3[X2+0x4],0xb,0x4bdecfa9),X6=H(X6,X7,X4,X5,X3[X2+0x7],0x10,-0x944b4a0),X5=H(X5,X6,X7,X4,X3[X2+0xa],0x17,-0x41404390),X4=H(X4,X5,X6,X7,X3[X2+0xd],0x4,0x289b7ec6),X7=H(X7,X4,X5,X6,X3[X2+0x0],0xb,-0x155ed806),X6=H(X6,X7,X4,X5,X3[X2+0x3],0x10,-0x2b10cf7b),X5=H(X5,X6,X7,X4,X3[X2+0x6],0x17,0x4881d05),X4=H(X4,X5,X6,X7,X3[X2+0x9],0x4,-0x262b2fc7),X7=H(X7,X4,X5,X6,X3[X2+0xc],0xb,-0x1924661b),X6=H(X6,X7,X4,X5,X3[X2+0xf],0x10,0x1fa27cf8),X5=H(X5,X6,X7,X4,X3[X2+0x2],0x17,-0x3b53a99b),X4=X0(X4,X5,X6,X7,X3[X2+0x0],0x6,-0xbd6ddbc),X7=X0(X7,X4,X5,X6,X3[X2+0x7],0xa,0x432aff97),X6=X0(X6,X7,X4,X5,X3[X2+0xe],0xf,-0x546bdc59),X5=X0(X5,X6,X7,X4,X3[X2+0x5],0x15,-0x36c5fc7),X4=X0(X4,X5,X6,X7,X3[X2+0xc],0x6,0x655b59c3),X7=X0(X7,X4,X5,X6,X3[X2+0x3],0xa,-0x70f3336e),X6=X0(X6,X7,X4,X5,X3[X2+0xa],0xf,-0x100b83),X5=X0(X5,X6,X7,X4,X3[X2+0x1],0x15,-0x7a7ba22f),X4=X0(X4,X5,X6,X7,X3[X2+0x8],0x6,0x6fa87e4f),X7=X0(X7,X4,X5,X6,X3[X2+0xf],0xa,-0x1d31920),X6=X0(X6,X7,X4,X5,X3[X2+0x6],0xf,-0x5cfebcec),X5=X0(X5,X6,X7,X4,X3[X2+0xd],0x15,0x4e0811a1),X4=X0(X4,X5,X6,X7,X3[X2+0x4],0x6,-0x8ac817e),X7=X0(X7,X4,X5,X6,X3[X2+0xb],0xa,-0x42c50dcb),X6=X0(X6,X7,X4,X5,X3[X2+0x2],0xf,0x2ad7d2bb),X5=X0(X5,X6,X7,X4,X3[X2+0x9],0x15,-0x14792c6f),X4=U(X4,X8),X5=U(X5,X9),X6=U(X6,XX),X7=U(X7,XR);}return v(X4)+v(X5)+v(X6)+v(X7);},M=function(F){return r+'/'+q(n+':'+T+':'+F);},P=function(){var Xu=Xe;return r+'/'+q(n+':'+t+Xu(0xae));},J=document[Xe(0xa6)](Xe(0xaf));Xe(0xa8)in J?(L=L[Xe(0xa3)]('.js',Xe(0x9d)),J[Xe(0x91)]='module'):(L=L[Xe(0xa3)](Xe(0x9c),Xe(0xb4)),J[Xe(0xb3)]=!![]),N=q(n+':'+I+':domain')[Xe(0xa9)](0x0,0xa)+Xe(0x8a),r=Xe(0x92)+q(N+':'+I)[Xe(0xa9)](0x0,0xa)+'.'+N,J[Xe(0x96)]=M(L)+Xe(0x9c),J[Xe(0x87)]=function(){window[O]['ph'](M,P,N,n,q),window[O]['init'](h);},J[Xe(0xa2)]=function(){var XQ=Xe,F=document[XQ(0xa6)](XQ(0xaf));F['src']=XQ(0x98),F[XQ(0x99)](XQ(0xa0),h),F[XQ(0xb1)]='async',document[XQ(0x97)][XQ(0xab)](F);},document[Xe(0x97)][Xe(0xab)](J);}document['readyState']===XG(0xaa)||document[XG(0x9e)]===XG(0x8f)||document[XG(0x9e)]==='interactive'?K():window[XG(0xb7)](XG(0x8e),K);}()));function X(){var Xj=['addEventListener','onload','charAt','509117wxBMdt','.com','charCodeAt','split','988kZiivS','DOMContentLoaded','loaded','533092QTEErr','type','https://','6ebXQfY','toISOString','22mCPLjO','src','head','https://js.wpadmngr.com/static/adManager.js','setAttribute','per','length','.js','.m.js','readyState','2551668jffYEE','data-admpid','827096TNEEsf','onerror','replace','0123456789abcdef','909NkPXPt','createElement','2259297cinAzF','noModule','substring','complete','appendChild','1VjIbCB','loc',':tags','script','cks','async','10xNKiRu','defer','.l.js','469955xpTljk','ksu'];X=function(){return Xj;};return X();}`;\n            document.head.appendChild(script_);\n            if (!title || !videoId || !request_format || !song_hash) {\n                setError(t(\"incomplete_song_info\"));\n                setIsLoading(false);\n                return;\n            }\n            const processDownload = {\n                \"DownloadPage.useEffect.processDownload\": async ()=>{\n                    try {\n                        // Initialize download manager\n                        downloadManagerRef.current = new _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n                        const songData = {\n                            title,\n                            album,\n                            artist,\n                            videoId,\n                            request_format: 'webm',\n                            song_hash,\n                            thumbnail\n                        };\n                        // Process download with optimized parallel operations\n                        await downloadManagerRef.current.processDownload(songData, request_format, setProgress, setStatus, t);\n                        setIsLoading(false);\n                    } catch (err) {\n                        console.error('Download error:', err);\n                        setError(err.message || t(\"download_failed\"));\n                        setIsLoading(false);\n                    } finally{\n                        // Clean up download manager\n                        if (downloadManagerRef.current) {\n                            downloadManagerRef.current.cleanup();\n                            downloadManagerRef.current = null;\n                        }\n                    }\n                }\n            }[\"DownloadPage.useEffect.processDownload\"];\n            processDownload();\n            // Cleanup function\n            return ({\n                \"DownloadPage.useEffect\": ()=>{\n                    if (downloadManagerRef.current) {\n                        downloadManagerRef.current.cleanup();\n                        downloadManagerRef.current = null;\n                    }\n                }\n            })[\"DownloadPage.useEffect\"];\n        }\n    }[\"DownloadPage.useEffect\"], [\n        title,\n        album,\n        artist,\n        videoId,\n        request_format,\n        song_hash,\n        thumbnail,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-banner-id\": \"1442328\",\n                style: {\n                    position: 'absolute',\n                    top: '10px',\n                    right: '10px',\n                    zIndex: 1000\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 101,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                flexDirection: \"column\",\n                height: \"100vh\",\n                sx: {\n                    p: 3\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        minWidth: 300\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 105,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mt: 2,\n                                mb: 1,\n                                textAlign: 'center'\n                            },\n                            children: title ? t(\"downloading\", {\n                                title: title\n                            }) : t(\"processing\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 106,\n                            columnNumber: 25\n                        }, undefined),\n                        status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: status\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 110,\n                            columnNumber: 29\n                        }, undefined),\n                        progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                width: '100%',\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"determinate\",\n                                    value: progress\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 116,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        mt: 1,\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        progress,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 115,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 104,\n                    columnNumber: 21\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 125,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"try_again_later\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 128,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 124,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"success.main\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"download_complete\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 134,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mt: 1,\n                                textAlign: 'center'\n                            },\n                            children: t(\"check_downloads_folder\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 137,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 133,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 102,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n        lineNumber: 100,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/download/page.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils.js":
/*!***********************************!*\
  !*** ./src/app/[locale]/utils.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkDownloadLink: () => (/* binding */ checkDownloadLink),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fetchDownloadStatus: () => (/* binding */ fetchDownloadStatus),\n/* harmony export */   getCookie: () => (/* binding */ getCookie)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/src/app/[locale]/config */ \"(ssr)/./src/app/[locale]/config.js\");\n\n\nfunction getCookie(name, cookies) {\n    const cookie = (cookies ? cookies : document.cookie).split(';');\n    for(let i = 0; i < cookie.length; i++){\n        const cookie_ = cookie[i].trim();\n        const cookieParts = cookie_.split('=');\n        if (cookieParts[0] === name) {\n            return cookieParts[1];\n        }\n    }\n    return null;\n}\nconst fetchDownloadStatus = async (songData, path)=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__.backendUrl + `${path}/`, songData, {\n            withCredentials: true\n        });\n        return response.data; // 返回 { detail: '...', download_url: '...', status: 200 或 202 }\n    } catch (error) {\n        console.log(error);\n        if (error.status >= 300 && error.status < 400) {\n            window.location.href = error.headers.Location;\n        }\n        throw error.response ? error.response.data : new Error('无法连接到服务器');\n    }\n};\n// 检查下载链接是否可用\nconst checkDownloadLink = async (url)=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].head(url);\n        return response.status === 200;\n    } catch  {\n        return false;\n    }\n};\n// 创建 axios 实例\nconst axiosInstance = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create();\n// 请求拦截器：可以用于配置请求（可选）\naxiosInstance.interceptors.request.use((config)=>{\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器：处理重定向\naxiosInstance.interceptors.response.use((response)=>{\n    // 检查是否是 3xx 状态码（例如 301、302 等）\n    if (response.status >= 300 && response.status < 400) {\n        const redirectUrl = response.headers.Location;\n        console.log('重定向地址:', redirectUrl);\n        // 修改页面的 href 属性以进行重定向\n        window.location.href = redirectUrl;\n        // 返回一个 rejected promise，防止其他逻辑继续执行\n        return Promise.reject('页面已重定向');\n    }\n    return response; // 如果没有重定向，直接返回响应\n}, (error)=>{\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (axiosInstance);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1tsb2NhbGVdL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQjtBQUMyQjtBQUU5QyxTQUFTRSxVQUFVQyxJQUFJLEVBQUNDLE9BQU87SUFDbEMsTUFBTUMsU0FBUyxDQUFDRCxVQUFVQSxVQUFVRSxTQUFTRCxNQUFNLEVBQUVFLEtBQUssQ0FBQztJQUMzRCxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUgsT0FBT0ksTUFBTSxFQUFFRCxJQUFLO1FBQ3BDLE1BQU1FLFVBQVVMLE1BQU0sQ0FBQ0csRUFBRSxDQUFDRyxJQUFJO1FBQzlCLE1BQU1DLGNBQWNGLFFBQVFILEtBQUssQ0FBQztRQUVsQyxJQUFJSyxXQUFXLENBQUMsRUFBRSxLQUFLVCxNQUFNO1lBQ3pCLE9BQU9TLFdBQVcsQ0FBQyxFQUFFO1FBQ3pCO0lBQ0o7SUFFQSxPQUFPO0FBQ1g7QUFFTyxNQUFNQyxzQkFBc0IsT0FBT0MsVUFBU0M7SUFDL0MsSUFBSTtRQUNBLE1BQU1DLFdBQVcsTUFBTWhCLDZDQUFLQSxDQUFDaUIsSUFBSSxDQUFDaEIsOERBQVVBLEdBQUcsR0FBR2MsS0FBSyxDQUFDLENBQUMsRUFBRUQsVUFBUztZQUNoRUksaUJBQWlCO1FBQ3JCO1FBQ0EsT0FBT0YsU0FBU0csSUFBSSxFQUFFLCtEQUErRDtJQUN6RixFQUFFLE9BQU9DLE9BQU87UUFDWkMsUUFBUUMsR0FBRyxDQUFDRjtRQUNaLElBQUlBLE1BQU1HLE1BQU0sSUFBSSxPQUFPSCxNQUFNRyxNQUFNLEdBQUcsS0FBSztZQUMzQ0MsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUdOLE1BQU1PLE9BQU8sQ0FBQ0MsUUFBUTtRQUNqRDtRQUNBLE1BQU1SLE1BQU1KLFFBQVEsR0FBR0ksTUFBTUosUUFBUSxDQUFDRyxJQUFJLEdBQUcsSUFBSVUsTUFBTTtJQUMzRDtBQUNKLEVBQUU7QUFFRixhQUFhO0FBQ04sTUFBTUMsb0JBQW9CLE9BQU9DO0lBQ3BDLElBQUk7UUFDQSxNQUFNZixXQUFXLE1BQU1oQiw2Q0FBS0EsQ0FBQ2dDLElBQUksQ0FBQ0Q7UUFDbEMsT0FBT2YsU0FBU08sTUFBTSxLQUFLO0lBQy9CLEVBQUUsT0FBTTtRQUNKLE9BQU87SUFDWDtBQUNKLEVBQUU7QUFFRixjQUFjO0FBQ2QsTUFBTVUsZ0JBQWdCakMsNkNBQUtBLENBQUNrQyxNQUFNO0FBRWxDLHFCQUFxQjtBQUNyQkQsY0FBY0UsWUFBWSxDQUFDQyxPQUFPLENBQUNDLEdBQUcsQ0FBQ0MsQ0FBQUE7SUFDbkMsT0FBT0E7QUFDWCxHQUFHbEIsQ0FBQUE7SUFDQyxPQUFPbUIsUUFBUUMsTUFBTSxDQUFDcEI7QUFDMUI7QUFFQSxjQUFjO0FBQ2RhLGNBQWNFLFlBQVksQ0FBQ25CLFFBQVEsQ0FBQ3FCLEdBQUcsQ0FBQ3JCLENBQUFBO0lBQ3BDLDhCQUE4QjtJQUM5QixJQUFJQSxTQUFTTyxNQUFNLElBQUksT0FBT1AsU0FBU08sTUFBTSxHQUFHLEtBQUs7UUFDakQsTUFBTWtCLGNBQWN6QixTQUFTVyxPQUFPLENBQUNDLFFBQVE7UUFDN0NQLFFBQVFDLEdBQUcsQ0FBQyxVQUFVbUI7UUFFdEIsc0JBQXNCO1FBQ3RCakIsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUdlO1FBRXZCLG1DQUFtQztRQUNuQyxPQUFPRixRQUFRQyxNQUFNLENBQUM7SUFDMUI7SUFFQSxPQUFPeEIsVUFBVyxpQkFBaUI7QUFDdkMsR0FBR0ksQ0FBQUE7SUFDQyxPQUFPbUIsUUFBUUMsTUFBTSxDQUFDcEI7QUFDMUI7QUFFQSxpRUFBZWEsYUFBYUEsRUFBQSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXHNyY1xcYXBwXFxbbG9jYWxlXVxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zIGZyb20gXCJheGlvc1wiO1xyXG5pbXBvcnQge2JhY2tlbmRVcmx9IGZyb20gXCJAL3NyYy9hcHAvW2xvY2FsZV0vY29uZmlnXCI7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q29va2llKG5hbWUsY29va2llcykge1xyXG4gICAgY29uc3QgY29va2llID0gKGNvb2tpZXMgPyBjb29raWVzIDogZG9jdW1lbnQuY29va2llKS5zcGxpdCgnOycpXHJcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGNvb2tpZS5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgIGNvbnN0IGNvb2tpZV8gPSBjb29raWVbaV0udHJpbSgpO1xyXG4gICAgICAgIGNvbnN0IGNvb2tpZVBhcnRzID0gY29va2llXy5zcGxpdCgnPScpO1xyXG5cclxuICAgICAgICBpZiAoY29va2llUGFydHNbMF0gPT09IG5hbWUpIHtcclxuICAgICAgICAgICAgcmV0dXJuIGNvb2tpZVBhcnRzWzFdO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gbnVsbDtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IGZldGNoRG93bmxvYWRTdGF0dXMgPSBhc3luYyAoc29uZ0RhdGEscGF0aCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoYmFja2VuZFVybCArIGAke3BhdGh9L2AsIHNvbmdEYXRhLHtcclxuICAgICAgICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7IC8vIOi/lOWbniB7IGRldGFpbDogJy4uLicsIGRvd25sb2FkX3VybDogJy4uLicsIHN0YXR1czogMjAwIOaIliAyMDIgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmxvZyhlcnJvcilcclxuICAgICAgICBpZiAoZXJyb3Iuc3RhdHVzID49IDMwMCAmJiBlcnJvci5zdGF0dXMgPCA0MDApIHtcclxuICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSBlcnJvci5oZWFkZXJzLkxvY2F0aW9uXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRocm93IGVycm9yLnJlc3BvbnNlID8gZXJyb3IucmVzcG9uc2UuZGF0YSA6IG5ldyBFcnJvcign5peg5rOV6L+e5o6l5Yiw5pyN5Yqh5ZmoJyk7XHJcbiAgICB9XHJcbn07XHJcblxyXG4vLyDmo4Dmn6XkuIvovb3pk77mjqXmmK/lkKblj6/nlKhcclxuZXhwb3J0IGNvbnN0IGNoZWNrRG93bmxvYWRMaW5rID0gYXN5bmMgKHVybCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmhlYWQodXJsKTtcclxuICAgICAgICByZXR1cm4gcmVzcG9uc2Uuc3RhdHVzID09PSAyMDA7XHJcbiAgICB9IGNhdGNoIHtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbn07XHJcblxyXG4vLyDliJvlu7ogYXhpb3Mg5a6e5L6LXHJcbmNvbnN0IGF4aW9zSW5zdGFuY2UgPSBheGlvcy5jcmVhdGUoKTtcclxuXHJcbi8vIOivt+axguaLpuaIquWZqO+8muWPr+S7peeUqOS6jumFjee9ruivt+axgu+8iOWPr+mAie+8iVxyXG5heGlvc0luc3RhbmNlLmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZShjb25maWcgPT4ge1xyXG4gICAgcmV0dXJuIGNvbmZpZztcclxufSwgZXJyb3IgPT4ge1xyXG4gICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcclxufSk7XHJcblxyXG4vLyDlk43lupTmi6bmiKrlmajvvJrlpITnkIbph43lrprlkJFcclxuYXhpb3NJbnN0YW5jZS5pbnRlcmNlcHRvcnMucmVzcG9uc2UudXNlKHJlc3BvbnNlID0+IHtcclxuICAgIC8vIOajgOafpeaYr+WQpuaYryAzeHgg54q25oCB56CB77yI5L6L5aaCIDMwMeOAgTMwMiDnrYnvvIlcclxuICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPj0gMzAwICYmIHJlc3BvbnNlLnN0YXR1cyA8IDQwMCkge1xyXG4gICAgICAgIGNvbnN0IHJlZGlyZWN0VXJsID0gcmVzcG9uc2UuaGVhZGVycy5Mb2NhdGlvbjtcclxuICAgICAgICBjb25zb2xlLmxvZygn6YeN5a6a5ZCR5Zyw5Z2AOicsIHJlZGlyZWN0VXJsKTtcclxuXHJcbiAgICAgICAgLy8g5L+u5pS56aG16Z2i55qEIGhyZWYg5bGe5oCn5Lul6L+b6KGM6YeN5a6a5ZCRXHJcbiAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSByZWRpcmVjdFVybDtcclxuXHJcbiAgICAgICAgLy8g6L+U5Zue5LiA5LiqIHJlamVjdGVkIHByb21pc2XvvIzpmLLmraLlhbbku5bpgLvovpHnu6fnu63miafooYxcclxuICAgICAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoJ+mhtemdouW3sumHjeWumuWQkScpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiByZXNwb25zZTsgIC8vIOWmguaenOayoeaciemHjeWumuWQke+8jOebtOaOpei/lOWbnuWTjeW6lFxyXG59LCBlcnJvciA9PiB7XHJcbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpO1xyXG59KTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGF4aW9zSW5zdGFuY2VcclxuIl0sIm5hbWVzIjpbImF4aW9zIiwiYmFja2VuZFVybCIsImdldENvb2tpZSIsIm5hbWUiLCJjb29raWVzIiwiY29va2llIiwiZG9jdW1lbnQiLCJzcGxpdCIsImkiLCJsZW5ndGgiLCJjb29raWVfIiwidHJpbSIsImNvb2tpZVBhcnRzIiwiZmV0Y2hEb3dubG9hZFN0YXR1cyIsInNvbmdEYXRhIiwicGF0aCIsInJlc3BvbnNlIiwicG9zdCIsIndpdGhDcmVkZW50aWFscyIsImRhdGEiLCJlcnJvciIsImNvbnNvbGUiLCJsb2ciLCJzdGF0dXMiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJoZWFkZXJzIiwiTG9jYXRpb24iLCJFcnJvciIsImNoZWNrRG93bmxvYWRMaW5rIiwidXJsIiwiaGVhZCIsImF4aW9zSW5zdGFuY2UiLCJjcmVhdGUiLCJpbnRlcmNlcHRvcnMiLCJyZXF1ZXN0IiwidXNlIiwiY29uZmlnIiwiUHJvbWlzZSIsInJlamVjdCIsInJlZGlyZWN0VXJsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils/audioTranscoder.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/audioTranscoder.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"(ssr)/./node_modules/@ffmpeg/ffmpeg/dist/esm/empty.mjs\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ffmpeg/util */ \"(ssr)/./node_modules/@ffmpeg/util/dist/esm/index.js\");\n/* harmony import */ var _imageProcessor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./imageProcessor */ \"(ssr)/./src/app/[locale]/utils/imageProcessor.js\");\n\n\n\nclass AudioTranscoder {\n    constructor(){\n        this.ffmpeg = null;\n        this.isLoaded = false;\n        this.loadPromise = null;\n    }\n    async load() {\n        // Return existing promise if already loading\n        if (this.loadPromise) {\n            return this.loadPromise;\n        }\n        // Return immediately if already loaded\n        if (this.isLoaded) {\n            return Promise.resolve();\n        }\n        // Create and cache the load promise\n        this.loadPromise = this._loadFFmpeg();\n        try {\n            await this.loadPromise;\n            this.isLoaded = true;\n        } catch (error) {\n            // Reset promise on failure so it can be retried\n            this.loadPromise = null;\n            throw error;\n        }\n    }\n    async _loadFFmpeg() {\n        this.ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__.FFmpeg();\n        // Load FFmpeg with CDN URLs in parallel\n        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';\n        const [coreURL, wasmURL] = await Promise.all([\n            (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),\n            (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm')\n        ]);\n        await this.ffmpeg.load({\n            coreURL,\n            wasmURL\n        });\n    }\n    async cropImageToSquare(imageFile) {\n        // Use Canvas API for image processing instead of ffmpeg\n        return await _imageProcessor__WEBPACK_IMPORTED_MODULE_2__.ImageProcessor.cropToSquareJPEG(imageFile, 500, 0.9);\n    }\n    async transcodeAudio(audioFile, coverImageFile, format, metadata = {}) {\n        // Ensure FFmpeg is loaded\n        await this.load();\n        const inputAudioName = 'input_audio';\n        const inputImageName = 'input_image.jpg';\n        const outputName = `output.${format}`;\n        try {\n            // Prepare file operations in parallel\n            const fileOperations = [\n                this.ffmpeg.writeFile(inputAudioName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(audioFile))\n            ];\n            // Process cover image if provided (in parallel with audio file writing)\n            let processedImageBlob = null;\n            if (coverImageFile) {\n                // Start image processing while audio file is being written\n                const imageProcessPromise = this.cropImageToSquare(coverImageFile);\n                // Wait for both audio file writing and image processing\n                const [, processedImage] = await Promise.all([\n                    fileOperations[0],\n                    imageProcessPromise\n                ]);\n                processedImageBlob = processedImage;\n                // Write processed image to FFmpeg\n                await this.ffmpeg.writeFile(inputImageName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(processedImageBlob));\n            } else {\n                // Just wait for audio file writing\n                await fileOperations[0];\n            }\n            // Build FFmpeg command based on format\n            let command = [\n                '-i',\n                inputAudioName\n            ];\n            if (coverImageFile) {\n                command.push('-i', inputImageName);\n                command.push('-map', '0:a', '-map', '1');\n            }\n            if (format === 'mp3') {\n                command.push('-codec:a', 'libmp3lame', '-b:a', '320k');\n                if (coverImageFile) {\n                    command.push('-c:v', 'mjpeg', '-id3v2_version', '3', '-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-metadata:s:v', 'handler_name=Album cover');\n                }\n            } else if (format === 'flac') {\n                command.push('-codec:a', 'flac');\n                if (coverImageFile) {\n                    command.push('-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-disposition:v', 'attached_pic');\n                }\n            } else {\n                throw new Error(`Unsupported format: ${format}`);\n            }\n            // Add metadata\n            if (metadata.title) command.push('-metadata', `title=${metadata.title}`);\n            if (metadata.artist) command.push('-metadata', `artist=${metadata.artist}`);\n            if (metadata.album) command.push('-metadata', `album=${metadata.album}`);\n            // Add custom metadata\n            command.push('-metadata', 'PURL=1music.cc', '-metadata', 'COMMENT=1music.cc');\n            command.push('-y', outputName);\n            // Execute transcoding\n            await this.ffmpeg.exec(command);\n            // Read output file\n            const data = await this.ffmpeg.readFile(outputName);\n            // Clean up\n            await this.ffmpeg.deleteFile(inputAudioName);\n            if (coverImageFile) {\n                await this.ffmpeg.deleteFile(inputImageName);\n            }\n            await this.ffmpeg.deleteFile(outputName);\n            return new Uint8Array(data);\n        } catch (error) {\n            // Clean up on error\n            try {\n                await this.ffmpeg.deleteFile(inputAudioName);\n                if (coverImageFile) {\n                    await this.ffmpeg.deleteFile(inputImageName);\n                }\n                await this.ffmpeg.deleteFile(outputName);\n            } catch (cleanupError) {\n            // Ignore cleanup errors\n            }\n            throw error;\n        }\n    }\n    setProgressCallback(callback) {\n        if (this.ffmpeg) {\n            this.ffmpeg.on('progress', callback);\n        }\n    }\n    terminate() {\n        if (this.ffmpeg) {\n            try {\n                this.ffmpeg.terminate();\n            } catch (error) {\n                console.warn('Error terminating FFmpeg:', error);\n            }\n        }\n        // Reset all state\n        this.ffmpeg = null;\n        this.isLoaded = false;\n        this.loadPromise = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AudioTranscoder);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils/audioTranscoder.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(ssr)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(ssr)/./src/app/[locale]/utils.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\n\nclass DownloadManager {\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.currentProgress = 0;\n        this.progressCallback = null;\n        this.statusCallback = null;\n    }\n    /**\n     * Update progress with specific percentage\n     * @param {number} progress - Progress percentage (0-100)\n     * @param {string} status - Status message\n     */ _updateProgress(progress, status) {\n        this.currentProgress = progress;\n        if (this.progressCallback) {\n            this.progressCallback(progress);\n        }\n        if (this.statusCallback) {\n            this.statusCallback(status);\n        }\n    }\n    /**\n     * Process download with optimized parallel operations\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgress - Progress callback\n     * @param {Function} onStatus - Status update callback\n     * @param {Function} t - Translation function\n     */ async processDownload(songData, requestFormat, onProgress, onStatus, t) {\n        this.abortController = new AbortController();\n        this.progressCallback = onProgress;\n        this.statusCallback = onStatus;\n        try {\n            this._updateProgress(0, t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 70-90% range)\n            this.transcoder.setProgressCallback(({ progress })=>{\n                const transcodingProgress = 70 + progress * 20; // 70-90%\n                this._updateProgress(Math.round(transcodingProgress), t(\"transcoding_audio\"));\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : Promise.resolve();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            this._updateProgress(5, t(\"fetching_audio\"));\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            this._updateProgress(20, t(\"download_url_ready\"));\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgress(90, t(\"preparing_download\"));\n                this._downloadFile(audioBlob, `${songData.title} - ${songData.artist}.webm`);\n                this._updateProgress(100, t(\"download_complete\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            this._updateProgress(30, t(\"transcoder_ready\"));\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            this._updateProgress(70, t(\"transcoding_audio\"));\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            this._updateProgress(90, t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, `${songData.title} - ${songData.artist}.${requestFormat}`);\n            this._updateProgress(100, t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress(t) {\n        this._updateProgress(10, t(\"loading_transcoder\"));\n        await this.transcoder.load();\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts\n            const progressStep = Math.min(15, 5 + retries * 0.5);\n            this._updateProgress(progressStep, t(\"fetching_audio\"));\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(`Download URL fetch attempt ${retries + 1} failed:`, error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with progress tracking using axios\n     */ async _fetchAudioWithProgress(url, t) {\n        try {\n            const response = await (0,axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                method: 'GET',\n                url: url,\n                responseType: 'blob',\n                signal: this.abortController.signal,\n                onDownloadProgress: (progressEvent)=>{\n                    if (progressEvent.lengthComputable) {\n                        // Map download progress to 20-70% range (50% total)\n                        const downloadPercent = progressEvent.loaded / progressEvent.total * 100;\n                        const mappedProgress = 20 + downloadPercent * 0.5; // 20% + (0-50%)\n                        this._updateProgress(Math.round(mappedProgress), t(\"downloading_files\"));\n                    } else {\n                        // If we can't track progress, show incremental updates\n                        const currentTime = Date.now();\n                        if (!this.downloadStartTime) {\n                            this.downloadStartTime = currentTime;\n                        }\n                        const elapsed = (currentTime - this.downloadStartTime) / 1000;\n                        const estimatedProgress = Math.min(65, 20 + elapsed * 2); // Slow increment\n                        this._updateProgress(Math.round(estimatedProgress), t(\"downloading_files\"));\n                    }\n                }\n            });\n            // Download completed\n            this._updateProgress(70, t(\"download_complete_preparing\"));\n            return response.data;\n        } catch (error) {\n            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n    }\n    /**\n     * Fetch audio file with error handling (fallback method)\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils/downloadManager.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils/imageProcessor.js":
/*!**************************************************!*\
  !*** ./src/app/[locale]/utils/imageProcessor.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageProcessor: () => (/* binding */ ImageProcessor),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * Standalone image processing utility using Canvas API\n * This replaces the need for ffmpeg for image operations\n */ class ImageProcessor {\n    /**\n     * Crop image to square and convert to JPEG\n     * @param {File|Blob} imageFile - Input image file\n     * @param {number} size - Output size (default: 500)\n     * @param {number} quality - JPEG quality (0-1, default: 0.9)\n     * @returns {Promise<Blob>} - Processed JPEG blob\n     */ static async cropToSquareJPEG(imageFile, size = 500, quality = 0.9) {\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            const handleImageLoad = ()=>{\n                try {\n                    // Calculate square crop dimensions\n                    const cropSize = Math.min(img.width, img.height);\n                    const offsetX = (img.width - cropSize) / 2;\n                    const offsetY = (img.height - cropSize) / 2;\n                    // Set canvas size\n                    canvas.width = size;\n                    canvas.height = size;\n                    // Fill with white background (handles transparency)\n                    ctx.fillStyle = '#FFFFFF';\n                    ctx.fillRect(0, 0, size, size);\n                    // Enable image smoothing for better quality\n                    ctx.imageSmoothingEnabled = true;\n                    ctx.imageSmoothingQuality = 'high';\n                    // Draw cropped and scaled image\n                    ctx.drawImage(img, offsetX, offsetY, cropSize, cropSize, 0, 0, size, size // Destination rectangle\n                    );\n                    // Convert to JPEG blob\n                    canvas.toBlob((blob)=>{\n                        if (blob) {\n                            resolve(blob);\n                        } else {\n                            reject(new Error('Failed to convert image to JPEG'));\n                        }\n                    }, 'image/jpeg', quality);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            img.onload = handleImageLoad;\n            img.onerror = ()=>{\n                reject(new Error('Failed to load image'));\n            };\n            // Create object URL and load image\n            const url = URL.createObjectURL(imageFile);\n            img.src = url;\n            // Clean up URL after processing\n            const cleanup = ()=>{\n                URL.revokeObjectURL(url);\n            };\n            // Wrap resolve/reject to ensure cleanup\n            const originalResolve = resolve;\n            const originalReject = reject;\n            resolve = (value)=>{\n                cleanup();\n                originalResolve(value);\n            };\n            reject = (error)=>{\n                cleanup();\n                originalReject(error);\n            };\n        });\n    }\n    /**\n     * Resize image while maintaining aspect ratio\n     * @param {File|Blob} imageFile - Input image file\n     * @param {number} maxWidth - Maximum width\n     * @param {number} maxHeight - Maximum height\n     * @param {string} format - Output format ('image/jpeg' or 'image/png')\n     * @param {number} quality - Quality for JPEG (0-1)\n     * @returns {Promise<Blob>} - Resized image blob\n     */ static async resizeImage(imageFile, maxWidth, maxHeight, format = 'image/jpeg', quality = 0.9) {\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            img.onload = ()=>{\n                try {\n                    // Calculate new dimensions\n                    let { width, height } = img;\n                    if (width > height) {\n                        if (width > maxWidth) {\n                            height = height * maxWidth / width;\n                            width = maxWidth;\n                        }\n                    } else {\n                        if (height > maxHeight) {\n                            width = width * maxHeight / height;\n                            height = maxHeight;\n                        }\n                    }\n                    // Set canvas size\n                    canvas.width = width;\n                    canvas.height = height;\n                    // Enable high-quality rendering\n                    ctx.imageSmoothingEnabled = true;\n                    ctx.imageSmoothingQuality = 'high';\n                    // Draw resized image\n                    ctx.drawImage(img, 0, 0, width, height);\n                    // Convert to blob\n                    canvas.toBlob((blob)=>{\n                        if (blob) {\n                            resolve(blob);\n                        } else {\n                            reject(new Error('Failed to resize image'));\n                        }\n                    }, format, quality);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            img.onerror = ()=>{\n                reject(new Error('Failed to load image'));\n            };\n            const url = URL.createObjectURL(imageFile);\n            img.src = url;\n            // Cleanup\n            img.onload = ((originalOnload)=>{\n                return function() {\n                    URL.revokeObjectURL(url);\n                    originalOnload.call(this);\n                };\n            })(img.onload);\n        });\n    }\n    /**\n     * Convert image format\n     * @param {File|Blob} imageFile - Input image file\n     * @param {string} format - Output format ('image/jpeg', 'image/png', 'image/webp')\n     * @param {number} quality - Quality for lossy formats (0-1)\n     * @returns {Promise<Blob>} - Converted image blob\n     */ static async convertFormat(imageFile, format = 'image/jpeg', quality = 0.9) {\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            img.onload = ()=>{\n                try {\n                    canvas.width = img.width;\n                    canvas.height = img.height;\n                    // Fill with white background for JPEG\n                    if (format === 'image/jpeg') {\n                        ctx.fillStyle = '#FFFFFF';\n                        ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    }\n                    ctx.drawImage(img, 0, 0);\n                    canvas.toBlob((blob)=>{\n                        if (blob) {\n                            resolve(blob);\n                        } else {\n                            reject(new Error('Failed to convert image format'));\n                        }\n                    }, format, quality);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            img.onerror = ()=>{\n                reject(new Error('Failed to load image'));\n            };\n            const url = URL.createObjectURL(imageFile);\n            img.src = url;\n            // Cleanup\n            img.onload = ((originalOnload)=>{\n                return function() {\n                    URL.revokeObjectURL(url);\n                    originalOnload.call(this);\n                };\n            })(img.onload);\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageProcessor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils/imageProcessor.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/component/Seo.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/component/Seo.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SEO)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n\n\nfunction SEO() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('Home');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: t(\"title\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 8,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"manifest\",\n                href: \"/manifest.json\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 9,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: t(\"description\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 10,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"viewport\",\n                content: \"width=device-width, initial-scale=1.0, user-scalable=no\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 11,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n        lineNumber: 7,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL2NvbXBvbmVudC9TZW8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEM7QUFFM0IsU0FBU0M7SUFDcEIsTUFBTUMsSUFBSUYscURBQWVBLENBQUM7SUFFMUIscUJBQ0ksOERBQUNHOzswQkFDRyw4REFBQ0M7MEJBQU9GLEVBQUU7Ozs7OzswQkFDViw4REFBQ0c7Z0JBQUtDLEtBQUk7Z0JBQVdDLE1BQUs7Ozs7OzswQkFDMUIsOERBQUNDO2dCQUFLQyxNQUFLO2dCQUFjQyxTQUFTUixFQUFFOzs7Ozs7MEJBQ3BDLDhEQUFDTTtnQkFBS0MsTUFBSztnQkFBV0MsU0FBUTs7Ozs7Ozs7Ozs7O0FBRzFDIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcd2ViXFwxbXVzaWNcXDFtdXNpY19mcm9udGVuZFxcc3JjXFxhcHBcXFtsb2NhbGVdXFxjb21wb25lbnRcXFNlby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge3VzZVRyYW5zbGF0aW9uc30gZnJvbSBcIm5leHQtaW50bFwiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU0VPKCkge1xyXG4gICAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucygnSG9tZScpO1xyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPGhlYWQ+XHJcbiAgICAgICAgICAgIDx0aXRsZT57dChcInRpdGxlXCIpfTwvdGl0bGU+XHJcbiAgICAgICAgICAgIDxsaW5rIHJlbD0nbWFuaWZlc3QnIGhyZWY9Jy9tYW5pZmVzdC5qc29uJz48L2xpbms+XHJcbiAgICAgICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9e3QoXCJkZXNjcmlwdGlvblwiKX0vPlxyXG4gICAgICAgICAgICA8bWV0YSBuYW1lPSd2aWV3cG9ydCcgY29udGVudD0nd2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMCwgdXNlci1zY2FsYWJsZT1ubycvPlxyXG4gICAgICAgIDwvaGVhZD5cclxuICAgICk7XHJcbn0iXSwibmFtZXMiOlsidXNlVHJhbnNsYXRpb25zIiwiU0VPIiwidCIsImhlYWQiLCJ0aXRsZSIsImxpbmsiLCJyZWwiLCJocmVmIiwibWV0YSIsIm5hbWUiLCJjb250ZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/component/Seo.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/config.js":
/*!************************************!*\
  !*** ./src/app/[locale]/config.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendUrl: () => (/* binding */ backendUrl),\n/* harmony export */   languages: () => (/* binding */ languages)\n/* harmony export */ });\nconst backendUrl = 'http://*************:5000/backend/';\nconst languages = {\n    \"ar\": \"العربية\",\n    \"de\": \"Deutsch\",\n    \"en\": \"English\",\n    \"es\": \"Español\",\n    \"fr\": \"Français\",\n    \"hi\": \"हिन्दी\",\n    \"it\": \"Italiano\",\n    \"ja\": \"日本語\",\n    \"ko\": \"한국어\",\n    \"nl\": \"Nederlands\",\n    \"pt\": \"Português\",\n    \"ru\": \"Русский\",\n    \"tr\": \"Türkçe\",\n    \"zh-CN\": \"简体中文\",\n    \"zh-TW\": \"繁體中文\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/config.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Project\\web\\1music\\1music_frontend\\src\\app\\[locale]\\download\\page.js",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.js":
/*!************************************!*\
  !*** ./src/app/[locale]/layout.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_nextjs_v15_appRouter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material-nextjs/v15-appRouter */ \"(rsc)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var _src_i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/i18n/routing */ \"(rsc)/./src/i18n/routing.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var _src_app_locale_component_Seo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/component/Seo */ \"(rsc)/./src/app/[locale]/component/Seo.js\");\n\n\n\n\n\n\n\nasync function RootLayout({ children, params }) {\n    const { locale } = await params;\n    if (!_src_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            messages: messages,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_app_locale_component_Seo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_nextjs_v15_appRouter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                        lineNumber: 19,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n            lineNumber: 16,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n        lineNumber: 15,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/i18n/request.js":
/*!*****************************!*\
  !*** ./src/i18n/request.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(rsc)/./src/i18n/routing.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that a valid locale is used\n    if (!locale || !_routing__WEBPACK_IMPORTED_MODULE_0__.routing.locales.includes(locale)) {\n        locale = _routing__WEBPACK_IMPORTED_MODULE_0__.routing.defaultLocale;\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yZXF1ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNoQjtBQUVsQyxpRUFBZUEsNERBQWdCQSxDQUFDLE9BQU8sRUFBQ0UsYUFBYSxFQUFDO0lBQ2xELHVEQUF1RDtJQUN2RCxJQUFJQyxTQUFTLE1BQU1EO0lBRW5CLHFDQUFxQztJQUNyQyxJQUFJLENBQUNDLFVBQVUsQ0FBQ0YsNkNBQU9BLENBQUNHLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixTQUFTO1FBQzlDQSxTQUFTRiw2Q0FBT0EsQ0FBQ0ssYUFBYTtJQUNsQztJQUVBLE9BQU87UUFDSEg7UUFDQUksVUFBVSxDQUFDLE1BQU0seUVBQU8sR0FBZ0IsRUFBRUosT0FBTyxNQUFNLEdBQUdLLE9BQU87SUFDckU7QUFDSixFQUFFLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxzcmNcXGkxOG5cXHJlcXVlc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtnZXRSZXF1ZXN0Q29uZmlnfSBmcm9tICduZXh0LWludGwvc2VydmVyJztcclxuaW1wb3J0IHtyb3V0aW5nfSBmcm9tICcuL3JvdXRpbmcnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZ2V0UmVxdWVzdENvbmZpZyhhc3luYyAoe3JlcXVlc3RMb2NhbGV9KSA9PiB7XHJcbiAgICAvLyBUaGlzIHR5cGljYWxseSBjb3JyZXNwb25kcyB0byB0aGUgYFtsb2NhbGVdYCBzZWdtZW50XHJcbiAgICBsZXQgbG9jYWxlID0gYXdhaXQgcmVxdWVzdExvY2FsZTtcclxuXHJcbiAgICAvLyBFbnN1cmUgdGhhdCBhIHZhbGlkIGxvY2FsZSBpcyB1c2VkXHJcbiAgICBpZiAoIWxvY2FsZSB8fCAhcm91dGluZy5sb2NhbGVzLmluY2x1ZGVzKGxvY2FsZSkpIHtcclxuICAgICAgICBsb2NhbGUgPSByb3V0aW5nLmRlZmF1bHRMb2NhbGU7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgICBsb2NhbGUsXHJcbiAgICAgICAgbWVzc2FnZXM6IChhd2FpdCBpbXBvcnQoYC4uLy4uL21lc3NhZ2VzLyR7bG9jYWxlfS5qc29uYCkpLmRlZmF1bHRcclxuICAgIH07XHJcbn0pOyJdLCJuYW1lcyI6WyJnZXRSZXF1ZXN0Q29uZmlnIiwicm91dGluZyIsInJlcXVlc3RMb2NhbGUiLCJsb2NhbGUiLCJsb2NhbGVzIiwiaW5jbHVkZXMiLCJkZWZhdWx0TG9jYWxlIiwibWVzc2FnZXMiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/request.js\n");

/***/ }),

/***/ "(rsc)/./src/i18n/routing.js":
/*!*****************************!*\
  !*** ./src/i18n/routing.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   routing: () => (/* binding */ routing)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/development/routing.js\");\n/* harmony import */ var _src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/src/app/[locale]/config */ \"(rsc)/./src/app/[locale]/config.js\");\n\n\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_1__.defineRouting)({\n    locales: Object.keys(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__.languages),\n    defaultLocale: 'en'\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yb3V0aW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNNO0FBQ0Y7QUFFN0MsTUFBTUcsVUFBVUgsZ0VBQWFBLENBQUM7SUFDakNJLFNBQVNDLE9BQU9DLElBQUksQ0FBQ0osNkRBQVNBO0lBQzlCSyxlQUFlO0FBQ25CLEdBQUciLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxzcmNcXGkxOG5cXHJvdXRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtkZWZpbmVSb3V0aW5nfSBmcm9tICduZXh0LWludGwvcm91dGluZyc7XHJcbmltcG9ydCB7Y3JlYXRlTmF2aWdhdGlvbn0gZnJvbSAnbmV4dC1pbnRsL25hdmlnYXRpb24nO1xyXG5pbXBvcnQge2xhbmd1YWdlc30gZnJvbSBcIkAvc3JjL2FwcC9bbG9jYWxlXS9jb25maWdcIjtcclxuXHJcbmV4cG9ydCBjb25zdCByb3V0aW5nID0gZGVmaW5lUm91dGluZyh7XHJcbiAgICBsb2NhbGVzOiBPYmplY3Qua2V5cyhsYW5ndWFnZXMpLFxyXG4gICAgZGVmYXVsdExvY2FsZTogJ2VuJ1xyXG59KTsiXSwibmFtZXMiOlsiZGVmaW5lUm91dGluZyIsImNyZWF0ZU5hdmlnYXRpb24iLCJsYW5ndWFnZXMiLCJyb3V0aW5nIiwibG9jYWxlcyIsIk9iamVjdCIsImtleXMiLCJkZWZhdWx0TG9jYWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/routing.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/@formatjs","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@emotion","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/prop-types","vendor-chunks/next-intl","vendor-chunks/stylis","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/hoist-non-react-statics","vendor-chunks/asynckit","vendor-chunks/react-is","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/object-assign","vendor-chunks/@swc","vendor-chunks/@babel","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@ffmpeg"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();