/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/download/page";
exports.ids = ["app/[locale]/download/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ar.json": [
		"(rsc)/./messages/ar.json",
		"_rsc_messages_ar_json"
	],
	"./de.json": [
		"(rsc)/./messages/de.json",
		"_rsc_messages_de_json"
	],
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	],
	"./fr.json": [
		"(rsc)/./messages/fr.json",
		"_rsc_messages_fr_json"
	],
	"./hi.json": [
		"(rsc)/./messages/hi.json",
		"_rsc_messages_hi_json"
	],
	"./it.json": [
		"(rsc)/./messages/it.json",
		"_rsc_messages_it_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	],
	"./ko.json": [
		"(rsc)/./messages/ko.json",
		"_rsc_messages_ko_json"
	],
	"./nl.json": [
		"(rsc)/./messages/nl.json",
		"_rsc_messages_nl_json"
	],
	"./pt.json": [
		"(rsc)/./messages/pt.json",
		"_rsc_messages_pt_json"
	],
	"./ru.json": [
		"(rsc)/./messages/ru.json",
		"_rsc_messages_ru_json"
	],
	"./tr.json": [
		"(rsc)/./messages/tr.json",
		"_rsc_messages_tr_json"
	],
	"./zh-CN.json": [
		"(rsc)/./messages/zh-CN.json",
		"_rsc_messages_zh-CN_json"
	],
	"./zh-TW.json": [
		"(rsc)/./messages/zh-TW.json",
		"_rsc_messages_zh-TW_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.js */ \"(rsc)/./src/app/[locale]/layout.js\"));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/download/page.js */ \"(rsc)/./src/app/[locale]/download/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'download',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module3, \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\"],\n        \n      }\n      ]\n      },\n        {\n        'not-found': [module0, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module1, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module2, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/download/page\",\n        pathname: \"/[locale]/download\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js */ \"(rsc)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js */ \"(ssr)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2plY3QlNUMlNUN3ZWIlNUMlNUMxbXVzaWMlNUMlNUMxbXVzaWNfZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdCU1QyU1Q3dlYiU1QyU1QzFtdXNpYyU1QyU1QzFtdXNpY19mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDaHR0cC1hY2Nlc3MtZmFsbGJhY2slNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdCU1QyU1Q3dlYiU1QyU1QzFtdXNpYyU1QyU1QzFtdXNpY19mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2plY3QlNUMlNUN3ZWIlNUMlNUMxbXVzaWMlNUMlNUMxbXVzaWNfZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNsaWIlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBNEk7QUFDNUk7QUFDQSwwT0FBK0k7QUFDL0k7QUFDQSwwT0FBK0k7QUFDL0k7QUFDQSxvUkFBcUs7QUFDcks7QUFDQSx3T0FBOEk7QUFDOUk7QUFDQSxzUUFBNko7QUFDN0o7QUFDQSxzT0FBNkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdFxcXFx3ZWJcXFxcMW11c2ljXFxcXDFtdXNpY19mcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0XFxcXHdlYlxcXFwxbXVzaWNcXFxcMW11c2ljX2Zyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdFxcXFx3ZWJcXFxcMW11c2ljXFxcXDFtdXNpY19mcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0XFxcXHdlYlxcXFwxbXVzaWNcXFxcMW11c2ljX2Zyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcbGliXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/download/page.js */ \"(rsc)/./src/app/[locale]/download/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDZG93bmxvYWQlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBcUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFxkb3dubG9hZFxcXFxwYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/download/page.js */ \"(ssr)/./src/app/[locale]/download/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDZG93bmxvYWQlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBcUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RcXFxcd2ViXFxcXDFtdXNpY1xcXFwxbXVzaWNfZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFxkb3dubG9hZFxcXFxwYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cdownload%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/config.js":
/*!************************************!*\
  !*** ./src/app/[locale]/config.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendUrl: () => (/* binding */ backendUrl),\n/* harmony export */   languages: () => (/* binding */ languages)\n/* harmony export */ });\nconst backendUrl = 'http://*************:5000/backend/';\nconst languages = {\n    \"ar\": \"العربية\",\n    \"de\": \"Deutsch\",\n    \"en\": \"English\",\n    \"es\": \"Español\",\n    \"fr\": \"Français\",\n    \"hi\": \"हिन्दी\",\n    \"it\": \"Italiano\",\n    \"ja\": \"日本語\",\n    \"ko\": \"한국어\",\n    \"nl\": \"Nederlands\",\n    \"pt\": \"Português\",\n    \"ru\": \"Русский\",\n    \"tr\": \"Türkçe\",\n    \"zh-CN\": \"简体中文\",\n    \"zh-TW\": \"繁體中文\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1tsb2NhbGVdL2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPLE1BQU1BLGFBQWEscUNBQW9DO0FBRXZELE1BQU1DLFlBQVk7SUFDckIsTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLFNBQVM7SUFDVCxTQUFTO0FBQ2IsRUFBRSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXHNyY1xcYXBwXFxbbG9jYWxlXVxcY29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBiYWNrZW5kVXJsID0gJ2h0dHA6Ly8xNzIuMjUuNzkuMTIyOjUwMDAvYmFja2VuZC8nXG5cbmV4cG9ydCBjb25zdCBsYW5ndWFnZXMgPSB7XG4gICAgXCJhclwiOiBcItin2YTYudix2KjZitipXCIsXG4gICAgXCJkZVwiOiBcIkRldXRzY2hcIixcbiAgICBcImVuXCI6IFwiRW5nbGlzaFwiLFxuICAgIFwiZXNcIjogXCJFc3Bhw7FvbFwiLFxuICAgIFwiZnJcIjogXCJGcmFuw6dhaXNcIixcbiAgICBcImhpXCI6IFwi4KS54KS/4KSo4KWN4KSm4KWAXCIsXG4gICAgXCJpdFwiOiBcIkl0YWxpYW5vXCIsXG4gICAgXCJqYVwiOiBcIuaXpeacrOiqnlwiLFxuICAgIFwia29cIjogXCLtlZzqta3slrRcIixcbiAgICBcIm5sXCI6IFwiTmVkZXJsYW5kc1wiLFxuICAgIFwicHRcIjogXCJQb3J0dWd1w6pzXCIsXG4gICAgXCJydVwiOiBcItCg0YPRgdGB0LrQuNC5XCIsXG4gICAgXCJ0clwiOiBcIlTDvHJrw6dlXCIsXG4gICAgXCJ6aC1DTlwiOiBcIueugOS9k+S4reaWh1wiLFxuICAgIFwiemgtVFdcIjogXCLnuYHpq5TkuK3mlodcIlxufTsiXSwibmFtZXMiOlsiYmFja2VuZFVybCIsImxhbmd1YWdlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/config.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(ssr)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/utils/downloadManager */ \"(ssr)/./src/app/[locale]/utils/downloadManager.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst DownloadPage = ()=>{\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"Download\");\n    const title = searchParams.get('title');\n    const album = searchParams.get('album');\n    const artist = searchParams.get('artist');\n    const videoId = searchParams.get('videoId');\n    const request_format = searchParams.get('request_format');\n    const song_hash = searchParams.get('song_hash');\n    const thumbnail = searchParams.get('thumbnail');\n    // Simple state management for parallel progress\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Progress state for parallel operations\n    const progressState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        urlFetch: 0,\n        ffmpegLoad: 0,\n        audioDownload: 0,\n        transcoding: 0 // 转码进度 (0-20%)\n    });\n    const downloadManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Calculate total progress from all components\n    const calculateTotalProgress = ()=>{\n        const { urlFetch, ffmpegLoad, audioDownload, transcoding } = progressState.current;\n        return Math.min(100, urlFetch + ffmpegLoad + audioDownload + transcoding);\n    };\n    // Update specific progress component\n    const updateProgressComponent = (component, componentProgress)=>{\n        progressState.current[component] = componentProgress;\n        const totalProgress = calculateTotalProgress();\n        setProgress(totalProgress);\n        console.log(`Progress Update - ${component}: ${componentProgress}%, Total: ${totalProgress}%`);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DownloadPage.useEffect\": ()=>{\n            const script = document.createElement(\"script\");\n            script.type = \"text/javascript\";\n            script.src = \"//plantationexhaust.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js\";\n            document.head.appendChild(script);\n            const script_1 = document.createElement(\"script\");\n            script_1.type = \"text/javascript\";\n            script_1.src = \"//pl26003516.effectiveratecpm.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js\";\n            document.body.appendChild(script_1);\n            if (!title || !videoId || !request_format || !song_hash) {\n                setError(t(\"incomplete_song_info\"));\n                setIsLoading(false);\n                return;\n            }\n            const processDownload = {\n                \"DownloadPage.useEffect.processDownload\": async ()=>{\n                    try {\n                        // Reset progress state\n                        progressState.current = {\n                            urlFetch: 0,\n                            ffmpegLoad: 0,\n                            audioDownload: 0,\n                            transcoding: 0\n                        };\n                        setProgress(0);\n                        setError(null);\n                        // Initialize download manager\n                        downloadManagerRef.current = new _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n                        const songData = {\n                            title,\n                            album,\n                            artist,\n                            videoId,\n                            request_format: 'webm',\n                            song_hash,\n                            thumbnail\n                        };\n                        // Process download with component-based progress\n                        await downloadManagerRef.current.processDownloadWithComponents(songData, request_format, updateProgressComponent, t);\n                        setIsLoading(false);\n                    } catch (err) {\n                        console.error('Download error:', err);\n                        setError(err.message || t(\"download_failed\"));\n                        setIsLoading(false);\n                    } finally{\n                        // Clean up download manager\n                        if (downloadManagerRef.current) {\n                            downloadManagerRef.current.cleanup();\n                            downloadManagerRef.current = null;\n                        }\n                    }\n                }\n            }[\"DownloadPage.useEffect.processDownload\"];\n            processDownload();\n            // Cleanup function\n            return ({\n                \"DownloadPage.useEffect\": ()=>{\n                    if (downloadManagerRef.current) {\n                        downloadManagerRef.current.cleanup();\n                        downloadManagerRef.current = null;\n                    }\n                }\n            })[\"DownloadPage.useEffect\"];\n        }\n    }[\"DownloadPage.useEffect\"], [\n        title,\n        album,\n        artist,\n        videoId,\n        request_format,\n        song_hash,\n        thumbnail,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            flexDirection: \"column\",\n            height: \"100vh\",\n            sx: {\n                p: 3\n            },\n            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                sx: {\n                    minWidth: 300\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 60\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                        lineNumber: 131,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        sx: {\n                            mt: 2,\n                            mb: 3,\n                            textAlign: 'center'\n                        },\n                        children: title ? t(\"downloading\", {\n                            title: title\n                        }) : t(\"processing\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                        lineNumber: 132,\n                        columnNumber: 25\n                    }, undefined),\n                    progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            width: '100%',\n                            mt: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"determinate\",\n                            value: progress\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 137,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                        lineNumber: 136,\n                        columnNumber: 29\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 130,\n                columnNumber: 21\n            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        color: \"error\",\n                        sx: {\n                            mb: 2,\n                            textAlign: 'center'\n                        },\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                        lineNumber: 143,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        sx: {\n                            textAlign: 'center'\n                        },\n                        children: t(\"try_again_later\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                        lineNumber: 146,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 142,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    variant: \"h6\",\n                    color: \"success.main\",\n                    sx: {\n                        textAlign: 'center'\n                    },\n                    children: t(\"download_complete\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 152,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 151,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n            lineNumber: 128,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n        lineNumber: 127,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/download/page.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils.js":
/*!***********************************!*\
  !*** ./src/app/[locale]/utils.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkDownloadLink: () => (/* binding */ checkDownloadLink),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fetchDownloadStatus: () => (/* binding */ fetchDownloadStatus),\n/* harmony export */   getCookie: () => (/* binding */ getCookie)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/src/app/[locale]/config */ \"(ssr)/./src/app/[locale]/config.js\");\n\n\nfunction getCookie(name, cookies) {\n    const cookie = (cookies ? cookies : document.cookie).split(';');\n    for(let i = 0; i < cookie.length; i++){\n        const cookie_ = cookie[i].trim();\n        const cookieParts = cookie_.split('=');\n        if (cookieParts[0] === name) {\n            return cookieParts[1];\n        }\n    }\n    return null;\n}\nconst fetchDownloadStatus = async (songData, path)=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__.backendUrl + `${path}/`, songData, {\n            withCredentials: true\n        });\n        return response.data; // 返回 { detail: '...', download_url: '...', status: 200 或 202 }\n    } catch (error) {\n        console.log(error);\n        if (error.status >= 300 && error.status < 400) {\n            window.location.href = error.headers.Location;\n        }\n        throw error.response ? error.response.data : new Error('无法连接到服务器');\n    }\n};\n// 检查下载链接是否可用\nconst checkDownloadLink = async (url)=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].head(url);\n        return response.status === 200;\n    } catch  {\n        return false;\n    }\n};\n// 创建 axios 实例\nconst axiosInstance = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create();\n// 请求拦截器：可以用于配置请求（可选）\naxiosInstance.interceptors.request.use((config)=>{\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器：处理重定向\naxiosInstance.interceptors.response.use((response)=>{\n    // 检查是否是 3xx 状态码（例如 301、302 等）\n    if (response.status >= 300 && response.status < 400) {\n        const redirectUrl = response.headers.Location;\n        console.log('重定向地址:', redirectUrl);\n        // 修改页面的 href 属性以进行重定向\n        window.location.href = redirectUrl;\n        // 返回一个 rejected promise，防止其他逻辑继续执行\n        return Promise.reject('页面已重定向');\n    }\n    return response; // 如果没有重定向，直接返回响应\n}, (error)=>{\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (axiosInstance);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils/audioTranscoder.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/audioTranscoder.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"(ssr)/./node_modules/@ffmpeg/ffmpeg/dist/esm/empty.mjs\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ffmpeg/util */ \"(ssr)/./node_modules/@ffmpeg/util/dist/esm/index.js\");\n/* harmony import */ var _imageProcessor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./imageProcessor */ \"(ssr)/./src/app/[locale]/utils/imageProcessor.js\");\n\n\n\nclass AudioTranscoder {\n    constructor(){\n        this.ffmpeg = null;\n        this.isLoaded = false;\n        this.loadPromise = null;\n    }\n    async load() {\n        // Return existing promise if already loading\n        if (this.loadPromise) {\n            return this.loadPromise;\n        }\n        // Return immediately if already loaded\n        if (this.isLoaded) {\n            return Promise.resolve();\n        }\n        // Create and cache the load promise\n        this.loadPromise = this._loadFFmpeg();\n        try {\n            await this.loadPromise;\n            this.isLoaded = true;\n        } catch (error) {\n            // Reset promise on failure so it can be retried\n            this.loadPromise = null;\n            throw error;\n        }\n    }\n    async _loadFFmpeg() {\n        this.ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__.FFmpeg();\n        // Load FFmpeg with CDN URLs in parallel\n        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';\n        const [coreURL, wasmURL] = await Promise.all([\n            (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),\n            (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm')\n        ]);\n        await this.ffmpeg.load({\n            coreURL,\n            wasmURL\n        });\n    }\n    async cropImageToSquare(imageFile) {\n        // Use Canvas API for image processing instead of ffmpeg\n        return await _imageProcessor__WEBPACK_IMPORTED_MODULE_2__.ImageProcessor.cropToSquareJPEG(imageFile, 500, 0.9);\n    }\n    async transcodeAudio(audioFile, coverImageFile, format, metadata = {}) {\n        // Ensure FFmpeg is loaded\n        await this.load();\n        const inputAudioName = 'input_audio';\n        const inputImageName = 'input_image.jpg';\n        const outputName = `output.${format}`;\n        try {\n            // Prepare file operations in parallel\n            const fileOperations = [\n                this.ffmpeg.writeFile(inputAudioName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(audioFile))\n            ];\n            // Process cover image if provided (in parallel with audio file writing)\n            let processedImageBlob = null;\n            if (coverImageFile) {\n                // Start image processing while audio file is being written\n                const imageProcessPromise = this.cropImageToSquare(coverImageFile);\n                // Wait for both audio file writing and image processing\n                const [, processedImage] = await Promise.all([\n                    fileOperations[0],\n                    imageProcessPromise\n                ]);\n                processedImageBlob = processedImage;\n                // Write processed image to FFmpeg\n                await this.ffmpeg.writeFile(inputImageName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(processedImageBlob));\n            } else {\n                // Just wait for audio file writing\n                await fileOperations[0];\n            }\n            // Build FFmpeg command based on format\n            let command = [\n                '-i',\n                inputAudioName\n            ];\n            if (coverImageFile) {\n                command.push('-i', inputImageName);\n                command.push('-map', '0:a', '-map', '1');\n            }\n            if (format === 'mp3') {\n                command.push('-codec:a', 'libmp3lame', '-b:a', '320k');\n                if (coverImageFile) {\n                    command.push('-c:v', 'mjpeg', '-id3v2_version', '3', '-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-metadata:s:v', 'handler_name=Album cover');\n                }\n            } else if (format === 'flac') {\n                command.push('-codec:a', 'flac');\n                if (coverImageFile) {\n                    command.push('-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-disposition:v', 'attached_pic');\n                }\n            } else {\n                throw new Error(`Unsupported format: ${format}`);\n            }\n            // Add metadata\n            if (metadata.title) command.push('-metadata', `title=${metadata.title}`);\n            if (metadata.artist) command.push('-metadata', `artist=${metadata.artist}`);\n            if (metadata.album) command.push('-metadata', `album=${metadata.album}`);\n            // Add custom metadata\n            command.push('-metadata', 'PURL=1music.cc', '-metadata', 'COMMENT=1music.cc');\n            command.push('-y', outputName);\n            // Execute transcoding\n            await this.ffmpeg.exec(command);\n            // Read output file\n            const data = await this.ffmpeg.readFile(outputName);\n            // Clean up\n            await this.ffmpeg.deleteFile(inputAudioName);\n            if (coverImageFile) {\n                await this.ffmpeg.deleteFile(inputImageName);\n            }\n            await this.ffmpeg.deleteFile(outputName);\n            return new Uint8Array(data);\n        } catch (error) {\n            // Clean up on error\n            try {\n                await this.ffmpeg.deleteFile(inputAudioName);\n                if (coverImageFile) {\n                    await this.ffmpeg.deleteFile(inputImageName);\n                }\n                await this.ffmpeg.deleteFile(outputName);\n            } catch (cleanupError) {\n            // Ignore cleanup errors\n            }\n            throw error;\n        }\n    }\n    setProgressCallback(callback) {\n        if (this.ffmpeg) {\n            this.ffmpeg.on('progress', callback);\n        }\n    }\n    terminate() {\n        if (this.ffmpeg) {\n            try {\n                this.ffmpeg.terminate();\n            } catch (error) {\n                console.warn('Error terminating FFmpeg:', error);\n            }\n        }\n        // Reset all state\n        this.ffmpeg = null;\n        this.isLoaded = false;\n        this.loadPromise = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AudioTranscoder);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils/audioTranscoder.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(ssr)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(ssr)/./src/app/[locale]/utils.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\n\nclass DownloadManager {\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.progressComponentCallback = null;\n    }\n    /**\n     * Update specific progress component\n     * @param {string} component - Progress component name\n     * @param {number} progress - Progress value for this component\n     */ _updateProgressComponent(component, progress) {\n        if (this.progressComponentCallback) {\n            this.progressComponentCallback(component, progress);\n        }\n    }\n    /**\n     * Process download with component-based progress tracking\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgressComponent - Component progress callback\n     * @param {Function} t - Translation function\n     */ async processDownloadWithComponents(songData, requestFormat, onProgressComponent, t1) {\n        this.abortController = new AbortController();\n        this.progressComponentCallback = onProgressComponent;\n        try {\n            // Reset progress state\n            this.progressState = {\n                urlFetch: 0,\n                ffmpegLoad: 0,\n                audioDownload: 0,\n                transcoding: 0\n            };\n            this._updateProgressComponent('urlFetch', 0);\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 0-20% of transcoding component)\n            this.transcoder.setProgressCallback(({ progress })=>{\n                const transcodingProgress = progress * 20; // 0-20%\n                this._updateProgressComponent('transcoding', transcodingProgress);\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress() : this._skipFFmpegLoad();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgressComponent('transcoding', 20);\n                this._downloadFile(audioBlob, `${songData.title} - ${songData.artist}.webm`);\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            // Start transcoding\n            this._updateProgressComponent('transcoding', 0);\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            // Transcoding complete\n            this._updateProgressComponent('transcoding', 20);\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, `${songData.title} - ${songData.artist}.${requestFormat}`);\n            this._updateProgressComponent('transcoding', 20);\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t1(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress() {\n        this._updateProgressComponent('ffmpegLoad', 0);\n        await this.transcoder.load();\n        this._updateProgressComponent('ffmpegLoad', 10);\n        return true;\n    }\n    /**\n     * Skip FFmpeg loading for webm format\n     */ async _skipFFmpegLoad() {\n        this._updateProgressComponent('ffmpegLoad', 10);\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts (0-20% range)\n            const progressStep = Math.min(20, (retries + 1) * (20 / maxRetries));\n            this._updateProgressComponent('urlFetch', progressStep);\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        this._updateProgressComponent('urlFetch', 20);\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(`Download URL fetch attempt ${retries + 1} failed:`, error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with progress tracking using axios\n     */ async _fetchAudioWithProgress(url) {\n        try {\n            this._updateProgressComponent('audioDownload', 0);\n            const response = await (0,axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                method: 'GET',\n                url: url,\n                responseType: 'blob',\n                signal: this.abortController.signal,\n                onDownloadProgress: (progressEvent)=>{\n                    if (progressEvent.lengthComputable) {\n                        // Map download progress to 0-50% range for audioDownload component\n                        const downloadPercent = progressEvent.loaded / progressEvent.total * 100;\n                        const mappedProgress = downloadPercent / 100 * 50; // 0-50%\n                        this._updateProgressComponent('audioDownload', mappedProgress);\n                    } else {\n                        // If we can't track progress, show incremental updates\n                        const currentTime = Date.now();\n                        if (!this.downloadStartTime) {\n                            this.downloadStartTime = currentTime;\n                        }\n                        const elapsed = (currentTime - this.downloadStartTime) / 1000;\n                        const estimatedProgress = Math.min(45, elapsed * 2); // Slow increment up to 45%\n                        this._updateProgressComponent('audioDownload', estimatedProgress);\n                    }\n                }\n            });\n            // Download completed\n            this._updateProgressComponent('audioDownload', 50);\n            return response.data;\n        } catch (error) {\n            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n    }\n    /**\n     * Fetch audio file with error handling (fallback method)\n     */ async _fetchAudio(url, t1) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t1(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils/downloadManager.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils/imageProcessor.js":
/*!**************************************************!*\
  !*** ./src/app/[locale]/utils/imageProcessor.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageProcessor: () => (/* binding */ ImageProcessor),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * Standalone image processing utility using Canvas API\n * This replaces the need for ffmpeg for image operations\n */ class ImageProcessor {\n    /**\n     * Crop image to square and convert to JPEG\n     * @param {File|Blob} imageFile - Input image file\n     * @param {number} size - Output size (default: 500)\n     * @param {number} quality - JPEG quality (0-1, default: 0.9)\n     * @returns {Promise<Blob>} - Processed JPEG blob\n     */ static async cropToSquareJPEG(imageFile, size = 500, quality = 0.9) {\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            const handleImageLoad = ()=>{\n                try {\n                    // Calculate square crop dimensions\n                    const cropSize = Math.min(img.width, img.height);\n                    const offsetX = (img.width - cropSize) / 2;\n                    const offsetY = (img.height - cropSize) / 2;\n                    // Set canvas size\n                    canvas.width = size;\n                    canvas.height = size;\n                    // Fill with white background (handles transparency)\n                    ctx.fillStyle = '#FFFFFF';\n                    ctx.fillRect(0, 0, size, size);\n                    // Enable image smoothing for better quality\n                    ctx.imageSmoothingEnabled = true;\n                    ctx.imageSmoothingQuality = 'high';\n                    // Draw cropped and scaled image\n                    ctx.drawImage(img, offsetX, offsetY, cropSize, cropSize, 0, 0, size, size // Destination rectangle\n                    );\n                    // Convert to JPEG blob\n                    canvas.toBlob((blob)=>{\n                        if (blob) {\n                            resolve(blob);\n                        } else {\n                            reject(new Error('Failed to convert image to JPEG'));\n                        }\n                    }, 'image/jpeg', quality);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            img.onload = handleImageLoad;\n            img.onerror = ()=>{\n                reject(new Error('Failed to load image'));\n            };\n            // Create object URL and load image\n            const url = URL.createObjectURL(imageFile);\n            img.src = url;\n            // Clean up URL after processing\n            const cleanup = ()=>{\n                URL.revokeObjectURL(url);\n            };\n            // Wrap resolve/reject to ensure cleanup\n            const originalResolve = resolve;\n            const originalReject = reject;\n            resolve = (value)=>{\n                cleanup();\n                originalResolve(value);\n            };\n            reject = (error)=>{\n                cleanup();\n                originalReject(error);\n            };\n        });\n    }\n    /**\n     * Resize image while maintaining aspect ratio\n     * @param {File|Blob} imageFile - Input image file\n     * @param {number} maxWidth - Maximum width\n     * @param {number} maxHeight - Maximum height\n     * @param {string} format - Output format ('image/jpeg' or 'image/png')\n     * @param {number} quality - Quality for JPEG (0-1)\n     * @returns {Promise<Blob>} - Resized image blob\n     */ static async resizeImage(imageFile, maxWidth, maxHeight, format = 'image/jpeg', quality = 0.9) {\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            img.onload = ()=>{\n                try {\n                    // Calculate new dimensions\n                    let { width, height } = img;\n                    if (width > height) {\n                        if (width > maxWidth) {\n                            height = height * maxWidth / width;\n                            width = maxWidth;\n                        }\n                    } else {\n                        if (height > maxHeight) {\n                            width = width * maxHeight / height;\n                            height = maxHeight;\n                        }\n                    }\n                    // Set canvas size\n                    canvas.width = width;\n                    canvas.height = height;\n                    // Enable high-quality rendering\n                    ctx.imageSmoothingEnabled = true;\n                    ctx.imageSmoothingQuality = 'high';\n                    // Draw resized image\n                    ctx.drawImage(img, 0, 0, width, height);\n                    // Convert to blob\n                    canvas.toBlob((blob)=>{\n                        if (blob) {\n                            resolve(blob);\n                        } else {\n                            reject(new Error('Failed to resize image'));\n                        }\n                    }, format, quality);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            img.onerror = ()=>{\n                reject(new Error('Failed to load image'));\n            };\n            const url = URL.createObjectURL(imageFile);\n            img.src = url;\n            // Cleanup\n            img.onload = ((originalOnload)=>{\n                return function() {\n                    URL.revokeObjectURL(url);\n                    originalOnload.call(this);\n                };\n            })(img.onload);\n        });\n    }\n    /**\n     * Convert image format\n     * @param {File|Blob} imageFile - Input image file\n     * @param {string} format - Output format ('image/jpeg', 'image/png', 'image/webp')\n     * @param {number} quality - Quality for lossy formats (0-1)\n     * @returns {Promise<Blob>} - Converted image blob\n     */ static async convertFormat(imageFile, format = 'image/jpeg', quality = 0.9) {\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            img.onload = ()=>{\n                try {\n                    canvas.width = img.width;\n                    canvas.height = img.height;\n                    // Fill with white background for JPEG\n                    if (format === 'image/jpeg') {\n                        ctx.fillStyle = '#FFFFFF';\n                        ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    }\n                    ctx.drawImage(img, 0, 0);\n                    canvas.toBlob((blob)=>{\n                        if (blob) {\n                            resolve(blob);\n                        } else {\n                            reject(new Error('Failed to convert image format'));\n                        }\n                    }, format, quality);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            img.onerror = ()=>{\n                reject(new Error('Failed to load image'));\n            };\n            const url = URL.createObjectURL(imageFile);\n            img.src = url;\n            // Cleanup\n            img.onload = ((originalOnload)=>{\n                return function() {\n                    URL.revokeObjectURL(url);\n                    originalOnload.call(this);\n                };\n            })(img.onload);\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageProcessor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils/imageProcessor.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/component/Seo.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/component/Seo.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SEO)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n\n\nfunction SEO() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('Home');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: t(\"title\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 8,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"manifest\",\n                href: \"/manifest.json\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 9,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: t(\"description\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 10,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"viewport\",\n                content: \"width=device-width, initial-scale=1.0, user-scalable=no\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 11,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n        lineNumber: 7,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL2NvbXBvbmVudC9TZW8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEM7QUFFM0IsU0FBU0M7SUFDcEIsTUFBTUMsSUFBSUYscURBQWVBLENBQUM7SUFFMUIscUJBQ0ksOERBQUNHOzswQkFDRyw4REFBQ0M7MEJBQU9GLEVBQUU7Ozs7OzswQkFDViw4REFBQ0c7Z0JBQUtDLEtBQUk7Z0JBQVdDLE1BQUs7Ozs7OzswQkFDMUIsOERBQUNDO2dCQUFLQyxNQUFLO2dCQUFjQyxTQUFTUixFQUFFOzs7Ozs7MEJBQ3BDLDhEQUFDTTtnQkFBS0MsTUFBSztnQkFBV0MsU0FBUTs7Ozs7Ozs7Ozs7O0FBRzFDIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcd2ViXFwxbXVzaWNcXDFtdXNpY19mcm9udGVuZFxcc3JjXFxhcHBcXFtsb2NhbGVdXFxjb21wb25lbnRcXFNlby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge3VzZVRyYW5zbGF0aW9uc30gZnJvbSBcIm5leHQtaW50bFwiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU0VPKCkge1xyXG4gICAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucygnSG9tZScpO1xyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPGhlYWQ+XHJcbiAgICAgICAgICAgIDx0aXRsZT57dChcInRpdGxlXCIpfTwvdGl0bGU+XHJcbiAgICAgICAgICAgIDxsaW5rIHJlbD0nbWFuaWZlc3QnIGhyZWY9Jy9tYW5pZmVzdC5qc29uJz48L2xpbms+XHJcbiAgICAgICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9e3QoXCJkZXNjcmlwdGlvblwiKX0vPlxyXG4gICAgICAgICAgICA8bWV0YSBuYW1lPSd2aWV3cG9ydCcgY29udGVudD0nd2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMCwgdXNlci1zY2FsYWJsZT1ubycvPlxyXG4gICAgICAgIDwvaGVhZD5cclxuICAgICk7XHJcbn0iXSwibmFtZXMiOlsidXNlVHJhbnNsYXRpb25zIiwiU0VPIiwidCIsImhlYWQiLCJ0aXRsZSIsImxpbmsiLCJyZWwiLCJocmVmIiwibWV0YSIsIm5hbWUiLCJjb250ZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/component/Seo.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/config.js":
/*!************************************!*\
  !*** ./src/app/[locale]/config.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendUrl: () => (/* binding */ backendUrl),\n/* harmony export */   languages: () => (/* binding */ languages)\n/* harmony export */ });\nconst backendUrl = 'http://*************:5000/backend/';\nconst languages = {\n    \"ar\": \"العربية\",\n    \"de\": \"Deutsch\",\n    \"en\": \"English\",\n    \"es\": \"Español\",\n    \"fr\": \"Français\",\n    \"hi\": \"हिन्दी\",\n    \"it\": \"Italiano\",\n    \"ja\": \"日本語\",\n    \"ko\": \"한국어\",\n    \"nl\": \"Nederlands\",\n    \"pt\": \"Português\",\n    \"ru\": \"Русский\",\n    \"tr\": \"Türkçe\",\n    \"zh-CN\": \"简体中文\",\n    \"zh-TW\": \"繁體中文\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/config.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Project\\web\\1music\\1music_frontend\\src\\app\\[locale]\\download\\page.js",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.js":
/*!************************************!*\
  !*** ./src/app/[locale]/layout.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_nextjs_v15_appRouter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material-nextjs/v15-appRouter */ \"(rsc)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var _src_i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/i18n/routing */ \"(rsc)/./src/i18n/routing.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var _src_app_locale_component_Seo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/component/Seo */ \"(rsc)/./src/app/[locale]/component/Seo.js\");\n\n\n\n\n\n\n\nasync function RootLayout({ children, params }) {\n    const { locale } = await params;\n    if (!_src_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            messages: messages,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_app_locale_component_Seo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_nextjs_v15_appRouter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                        lineNumber: 19,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n            lineNumber: 16,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n        lineNumber: 15,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/i18n/request.js":
/*!*****************************!*\
  !*** ./src/i18n/request.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(rsc)/./src/i18n/routing.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that a valid locale is used\n    if (!locale || !_routing__WEBPACK_IMPORTED_MODULE_0__.routing.locales.includes(locale)) {\n        locale = _routing__WEBPACK_IMPORTED_MODULE_0__.routing.defaultLocale;\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yZXF1ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNoQjtBQUVsQyxpRUFBZUEsNERBQWdCQSxDQUFDLE9BQU8sRUFBQ0UsYUFBYSxFQUFDO0lBQ2xELHVEQUF1RDtJQUN2RCxJQUFJQyxTQUFTLE1BQU1EO0lBRW5CLHFDQUFxQztJQUNyQyxJQUFJLENBQUNDLFVBQVUsQ0FBQ0YsNkNBQU9BLENBQUNHLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixTQUFTO1FBQzlDQSxTQUFTRiw2Q0FBT0EsQ0FBQ0ssYUFBYTtJQUNsQztJQUVBLE9BQU87UUFDSEg7UUFDQUksVUFBVSxDQUFDLE1BQU0seUVBQU8sR0FBZ0IsRUFBRUosT0FBTyxNQUFNLEdBQUdLLE9BQU87SUFDckU7QUFDSixFQUFFLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxzcmNcXGkxOG5cXHJlcXVlc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtnZXRSZXF1ZXN0Q29uZmlnfSBmcm9tICduZXh0LWludGwvc2VydmVyJztcclxuaW1wb3J0IHtyb3V0aW5nfSBmcm9tICcuL3JvdXRpbmcnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZ2V0UmVxdWVzdENvbmZpZyhhc3luYyAoe3JlcXVlc3RMb2NhbGV9KSA9PiB7XHJcbiAgICAvLyBUaGlzIHR5cGljYWxseSBjb3JyZXNwb25kcyB0byB0aGUgYFtsb2NhbGVdYCBzZWdtZW50XHJcbiAgICBsZXQgbG9jYWxlID0gYXdhaXQgcmVxdWVzdExvY2FsZTtcclxuXHJcbiAgICAvLyBFbnN1cmUgdGhhdCBhIHZhbGlkIGxvY2FsZSBpcyB1c2VkXHJcbiAgICBpZiAoIWxvY2FsZSB8fCAhcm91dGluZy5sb2NhbGVzLmluY2x1ZGVzKGxvY2FsZSkpIHtcclxuICAgICAgICBsb2NhbGUgPSByb3V0aW5nLmRlZmF1bHRMb2NhbGU7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgICBsb2NhbGUsXHJcbiAgICAgICAgbWVzc2FnZXM6IChhd2FpdCBpbXBvcnQoYC4uLy4uL21lc3NhZ2VzLyR7bG9jYWxlfS5qc29uYCkpLmRlZmF1bHRcclxuICAgIH07XHJcbn0pOyJdLCJuYW1lcyI6WyJnZXRSZXF1ZXN0Q29uZmlnIiwicm91dGluZyIsInJlcXVlc3RMb2NhbGUiLCJsb2NhbGUiLCJsb2NhbGVzIiwiaW5jbHVkZXMiLCJkZWZhdWx0TG9jYWxlIiwibWVzc2FnZXMiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/request.js\n");

/***/ }),

/***/ "(rsc)/./src/i18n/routing.js":
/*!*****************************!*\
  !*** ./src/i18n/routing.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   routing: () => (/* binding */ routing)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/development/routing.js\");\n/* harmony import */ var _src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/src/app/[locale]/config */ \"(rsc)/./src/app/[locale]/config.js\");\n\n\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_1__.defineRouting)({\n    locales: Object.keys(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__.languages),\n    defaultLocale: 'en'\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yb3V0aW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNNO0FBQ0Y7QUFFN0MsTUFBTUcsVUFBVUgsZ0VBQWFBLENBQUM7SUFDakNJLFNBQVNDLE9BQU9DLElBQUksQ0FBQ0osNkRBQVNBO0lBQzlCSyxlQUFlO0FBQ25CLEdBQUciLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxzcmNcXGkxOG5cXHJvdXRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtkZWZpbmVSb3V0aW5nfSBmcm9tICduZXh0LWludGwvcm91dGluZyc7XHJcbmltcG9ydCB7Y3JlYXRlTmF2aWdhdGlvbn0gZnJvbSAnbmV4dC1pbnRsL25hdmlnYXRpb24nO1xyXG5pbXBvcnQge2xhbmd1YWdlc30gZnJvbSBcIkAvc3JjL2FwcC9bbG9jYWxlXS9jb25maWdcIjtcclxuXHJcbmV4cG9ydCBjb25zdCByb3V0aW5nID0gZGVmaW5lUm91dGluZyh7XHJcbiAgICBsb2NhbGVzOiBPYmplY3Qua2V5cyhsYW5ndWFnZXMpLFxyXG4gICAgZGVmYXVsdExvY2FsZTogJ2VuJ1xyXG59KTsiXSwibmFtZXMiOlsiZGVmaW5lUm91dGluZyIsImNyZWF0ZU5hdmlnYXRpb24iLCJsYW5ndWFnZXMiLCJyb3V0aW5nIiwibG9jYWxlcyIsIk9iamVjdCIsImtleXMiLCJkZWZhdWx0TG9jYWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/routing.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/@formatjs","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@emotion","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/prop-types","vendor-chunks/next-intl","vendor-chunks/stylis","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/hoist-non-react-statics","vendor-chunks/asynckit","vendor-chunks/react-is","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/object-assign","vendor-chunks/@swc","vendor-chunks/@babel","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@ffmpeg"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fdownload%2Fpage&page=%2F%5Blocale%5D%2Fdownload%2Fpage&appPaths=%2F%5Blocale%5D%2Fdownload%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fdownload%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();