"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ffmpeg";
exports.ids = ["vendor-chunks/@ffmpeg"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ffmpeg/ffmpeg/dist/esm/empty.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@ffmpeg/ffmpeg/dist/esm/empty.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FFmpeg: () => (/* binding */ FFmpeg)\n/* harmony export */ });\n// File to be imported in node enviroments\nclass FFmpeg {\n    constructor() {\n        throw new Error(\"ffmpeg.wasm does not support nodejs\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGZmbXBlZy9mZm1wZWcvZGlzdC9lc20vZW1wdHkubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxub2RlX21vZHVsZXNcXEBmZm1wZWdcXGZmbXBlZ1xcZGlzdFxcZXNtXFxlbXB0eS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSB0byBiZSBpbXBvcnRlZCBpbiBub2RlIGVudmlyb21lbnRzXG5leHBvcnQgY2xhc3MgRkZtcGVnIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiZmZtcGVnLndhc20gZG9lcyBub3Qgc3VwcG9ydCBub2RlanNcIik7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ffmpeg/ffmpeg/dist/esm/empty.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ffmpeg/util/dist/esm/const.js":
/*!*****************************************************!*\
  !*** ./node_modules/@ffmpeg/util/dist/esm/const.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeaderContentLength: () => (/* binding */ HeaderContentLength)\n/* harmony export */ });\nconst HeaderContentLength = \"Content-Length\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGZmbXBlZy91dGlsL2Rpc3QvZXNtL2NvbnN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGZmbXBlZ1xcdXRpbFxcZGlzdFxcZXNtXFxjb25zdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgSGVhZGVyQ29udGVudExlbmd0aCA9IFwiQ29udGVudC1MZW5ndGhcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ffmpeg/util/dist/esm/const.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ffmpeg/util/dist/esm/errors.js":
/*!******************************************************!*\
  !*** ./node_modules/@ffmpeg/util/dist/esm/errors.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_INCOMPLETED_DOWNLOAD: () => (/* binding */ ERROR_INCOMPLETED_DOWNLOAD),\n/* harmony export */   ERROR_RESPONSE_BODY_READER: () => (/* binding */ ERROR_RESPONSE_BODY_READER)\n/* harmony export */ });\nconst ERROR_RESPONSE_BODY_READER = new Error(\"failed to get response body reader\");\nconst ERROR_INCOMPLETED_DOWNLOAD = new Error(\"failed to complete download\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGZmbXBlZy91dGlsL2Rpc3QvZXNtL2Vycm9ycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxub2RlX21vZHVsZXNcXEBmZm1wZWdcXHV0aWxcXGRpc3RcXGVzbVxcZXJyb3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBFUlJPUl9SRVNQT05TRV9CT0RZX1JFQURFUiA9IG5ldyBFcnJvcihcImZhaWxlZCB0byBnZXQgcmVzcG9uc2UgYm9keSByZWFkZXJcIik7XG5leHBvcnQgY29uc3QgRVJST1JfSU5DT01QTEVURURfRE9XTkxPQUQgPSBuZXcgRXJyb3IoXCJmYWlsZWQgdG8gY29tcGxldGUgZG93bmxvYWRcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ffmpeg/util/dist/esm/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ffmpeg/util/dist/esm/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@ffmpeg/util/dist/esm/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   downloadWithProgress: () => (/* binding */ downloadWithProgress),\n/* harmony export */   fetchFile: () => (/* binding */ fetchFile),\n/* harmony export */   importScript: () => (/* binding */ importScript),\n/* harmony export */   toBlobURL: () => (/* binding */ toBlobURL)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./errors.js */ \"(ssr)/./node_modules/@ffmpeg/util/dist/esm/errors.js\");\n/* harmony import */ var _const_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./const.js */ \"(ssr)/./node_modules/@ffmpeg/util/dist/esm/const.js\");\n\n\nconst readFromBlobOrFile = (blob) => new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = () => {\n        const { result } = fileReader;\n        if (result instanceof ArrayBuffer) {\n            resolve(new Uint8Array(result));\n        }\n        else {\n            resolve(new Uint8Array());\n        }\n    };\n    fileReader.onerror = (event) => {\n        reject(Error(`File could not be read! Code=${event?.target?.error?.code || -1}`));\n    };\n    fileReader.readAsArrayBuffer(blob);\n});\n/**\n * An util function to fetch data from url string, base64, URL, File or Blob format.\n *\n * Examples:\n * ```ts\n * // URL\n * await fetchFile(\"http://localhost:3000/video.mp4\");\n * // base64\n * await fetchFile(\"data:<type>;base64,wL2dvYWwgbW9yZ...\");\n * // URL\n * await fetchFile(new URL(\"video.mp4\", import.meta.url));\n * // File\n * fileInput.addEventListener('change', (e) => {\n *   await fetchFile(e.target.files[0]);\n * });\n * // Blob\n * const blob = new Blob(...);\n * await fetchFile(blob);\n * ```\n */\nconst fetchFile = async (file) => {\n    let data;\n    if (typeof file === \"string\") {\n        /* From base64 format */\n        if (/data:_data\\/([a-zA-Z]*);base64,([^\"]*)/.test(file)) {\n            data = atob(file.split(\",\")[1])\n                .split(\"\")\n                .map((c) => c.charCodeAt(0));\n            /* From remote server/URL */\n        }\n        else {\n            data = await (await fetch(file)).arrayBuffer();\n        }\n    }\n    else if (file instanceof URL) {\n        data = await (await fetch(file)).arrayBuffer();\n    }\n    else if (file instanceof File || file instanceof Blob) {\n        data = await readFromBlobOrFile(file);\n    }\n    else {\n        return new Uint8Array();\n    }\n    return new Uint8Array(data);\n};\n/**\n * importScript dynamically import a script, useful when you\n * want to use different versions of ffmpeg.wasm based on environment.\n *\n * Example:\n *\n * ```ts\n * await importScript(\"http://localhost:3000/ffmpeg.js\");\n * ```\n */\nconst importScript = async (url) => new Promise((resolve) => {\n    const script = document.createElement(\"script\");\n    const eventHandler = () => {\n        script.removeEventListener(\"load\", eventHandler);\n        resolve();\n    };\n    script.src = url;\n    script.type = \"text/javascript\";\n    script.addEventListener(\"load\", eventHandler);\n    document.getElementsByTagName(\"head\")[0].appendChild(script);\n});\n/**\n * Download content of a URL with progress.\n *\n * Progress only works when Content-Length is provided by the server.\n *\n */\nconst downloadWithProgress = async (url, cb) => {\n    const resp = await fetch(url);\n    let buf;\n    try {\n        // Set total to -1 to indicate that there is not Content-Type Header.\n        const total = parseInt(resp.headers.get(_const_js__WEBPACK_IMPORTED_MODULE_1__.HeaderContentLength) || \"-1\");\n        const reader = resp.body?.getReader();\n        if (!reader)\n            throw _errors_js__WEBPACK_IMPORTED_MODULE_0__.ERROR_RESPONSE_BODY_READER;\n        const chunks = [];\n        let received = 0;\n        for (;;) {\n            const { done, value } = await reader.read();\n            const delta = value ? value.length : 0;\n            if (done) {\n                if (total != -1 && total !== received)\n                    throw _errors_js__WEBPACK_IMPORTED_MODULE_0__.ERROR_INCOMPLETED_DOWNLOAD;\n                cb && cb({ url, total, received, delta, done });\n                break;\n            }\n            chunks.push(value);\n            received += delta;\n            cb && cb({ url, total, received, delta, done });\n        }\n        const data = new Uint8Array(received);\n        let position = 0;\n        for (const chunk of chunks) {\n            data.set(chunk, position);\n            position += chunk.length;\n        }\n        buf = data.buffer;\n    }\n    catch (e) {\n        console.log(`failed to send download progress event: `, e);\n        // Fetch arrayBuffer directly when it is not possible to get progress.\n        buf = await resp.arrayBuffer();\n        cb &&\n            cb({\n                url,\n                total: buf.byteLength,\n                received: buf.byteLength,\n                delta: 0,\n                done: true,\n            });\n    }\n    return buf;\n};\n/**\n * toBlobURL fetches data from an URL and return a blob URL.\n *\n * Example:\n *\n * ```ts\n * await toBlobURL(\"http://localhost:3000/ffmpeg.js\", \"text/javascript\");\n * ```\n */\nconst toBlobURL = async (url, mimeType, progress = false, cb) => {\n    const buf = progress\n        ? await downloadWithProgress(url, cb)\n        : await (await fetch(url)).arrayBuffer();\n    const blob = new Blob([buf], { type: mimeType });\n    return URL.createObjectURL(blob);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ffmpeg/util/dist/esm/index.js\n");

/***/ })

};
;