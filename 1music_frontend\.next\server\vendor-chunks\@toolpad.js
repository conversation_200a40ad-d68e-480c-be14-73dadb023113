"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@toolpad";
exports.ids = ["vendor-chunks/@toolpad"];
exports.modules = {

/***/ "(ssr)/./node_modules/@toolpad/core/esm/AppProvider/LocalizationProvider.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@toolpad/core/esm/AppProvider/LocalizationProvider.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LocalizationContext: () => (/* binding */ LocalizationContext),\n/* harmony export */   LocalizationProvider: () => (/* binding */ LocalizationProvider),\n/* harmony export */   useLocaleText: () => (/* binding */ useLocaleText)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _locales_en_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../locales/en.js */ \"(ssr)/./node_modules/@toolpad/core/esm/locales/en.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ LocalizationContext,LocalizationProvider,useLocaleText auto */ \n\n\n\n\nconst LocalizationContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({});\nconst LocalizationProvider = function LocalizationProvider(props) {\n    const { localeText: propsLocaleText, children } = props;\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // @ts-ignore\n    const themeLocaleText = theme?.components?.MuiLocalizationProvider?.defaultProps?.localeText;\n    const defaultLocaleText = _locales_en_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].components.MuiLocalizationProvider.defaultProps.localeText;\n    /* The order of overrides is:\n   * 1. The `localeText` prop of the `AppProvider` supersedes\n   * 2. The localeText provided as an argument to the `createTheme` function, which supersedes\n   * 3. The default locale text\n   */ const localeText = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"LocalizationProvider.useMemo[localeText]\": ()=>({\n                ...defaultLocaleText,\n                ...themeLocaleText,\n                ...propsLocaleText\n            })\n    }[\"LocalizationProvider.useMemo[localeText]\"], [\n        defaultLocaleText,\n        themeLocaleText,\n        propsLocaleText\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LocalizationContext.Provider, {\n        value: localeText,\n        children: children\n    });\n};\n true ? LocalizationProvider.propTypes = {\n    // ┌────────────────────────────── Warning ──────────────────────────────┐\n    // │ These PropTypes are generated from the TypeScript type definitions. │\n    // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n    // └─────────────────────────────────────────────────────────────────────┘\n    /**\n   * @ignore\n   */ children: prop_types__WEBPACK_IMPORTED_MODULE_4__.node,\n    /**\n   * Locale for components texts\n   */ localeText: prop_types__WEBPACK_IMPORTED_MODULE_4__.object\n} : 0;\n\n/**\n *\n * Demos:\n *\n * - [Sign-in Page](https://mui.com/toolpad/core/react-sign-in-page/)\n *\n * API:\n *\n * - [LocalizationProvider API](https://mui.com/toolpad/core/api/localization-provider)\n */ function useLocaleText() {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useContext(LocalizationContext);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/esm/AppProvider/LocalizationProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/esm/locales/en.js":
/*!******************************************************!*\
  !*** ./node_modules/@toolpad/core/esm/locales/en.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _getLocalization_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getLocalization.js */ \"(ssr)/./node_modules/@toolpad/core/esm/locales/getLocalization.js\");\n\nconst en = {\n  // Account\n  accountSignInLabel: 'Sign In',\n  accountSignOutLabel: 'Sign Out',\n  // AccountPreview\n  accountPreviewTitle: 'Account',\n  accountPreviewIconButtonLabel: 'Current User',\n  // SignInPage\n  signInTitle: 'Sign In',\n  signInSubtitle: 'Welcome user, please sign in to continue',\n  signInRememberMe: 'Remember Me',\n  oauthSignInTitle: 'Sign in with OAuth',\n  passkeySignInTitle: 'Sign in with Passkey',\n  magicLinkSignInTitle: 'Sign in with Magic Link',\n  // Common authentication labels\n  email: 'Email',\n  password: 'Password',\n  username: 'Username',\n  passkey: 'Passkey',\n  // Common action labels\n  save: 'Save',\n  cancel: 'Cancel',\n  ok: 'Ok',\n  or: 'Or',\n  to: 'To',\n  with: 'With',\n  close: 'Close',\n  delete: 'Delete',\n  alert: 'Alert',\n  confirm: 'Confirm',\n  loading: 'Loading...'\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_getLocalization_js__WEBPACK_IMPORTED_MODULE_0__.getLocalization)(en));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRvb2xwYWQvY29yZS9lc20vbG9jYWxlcy9lbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1RDtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsb0VBQWUsSUFBSSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHRvb2xwYWRcXGNvcmVcXGVzbVxcbG9jYWxlc1xcZW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0TG9jYWxpemF0aW9uIH0gZnJvbSBcIi4vZ2V0TG9jYWxpemF0aW9uLmpzXCI7XG5jb25zdCBlbiA9IHtcbiAgLy8gQWNjb3VudFxuICBhY2NvdW50U2lnbkluTGFiZWw6ICdTaWduIEluJyxcbiAgYWNjb3VudFNpZ25PdXRMYWJlbDogJ1NpZ24gT3V0JyxcbiAgLy8gQWNjb3VudFByZXZpZXdcbiAgYWNjb3VudFByZXZpZXdUaXRsZTogJ0FjY291bnQnLFxuICBhY2NvdW50UHJldmlld0ljb25CdXR0b25MYWJlbDogJ0N1cnJlbnQgVXNlcicsXG4gIC8vIFNpZ25JblBhZ2VcbiAgc2lnbkluVGl0bGU6ICdTaWduIEluJyxcbiAgc2lnbkluU3VidGl0bGU6ICdXZWxjb21lIHVzZXIsIHBsZWFzZSBzaWduIGluIHRvIGNvbnRpbnVlJyxcbiAgc2lnbkluUmVtZW1iZXJNZTogJ1JlbWVtYmVyIE1lJyxcbiAgb2F1dGhTaWduSW5UaXRsZTogJ1NpZ24gaW4gd2l0aCBPQXV0aCcsXG4gIHBhc3NrZXlTaWduSW5UaXRsZTogJ1NpZ24gaW4gd2l0aCBQYXNza2V5JyxcbiAgbWFnaWNMaW5rU2lnbkluVGl0bGU6ICdTaWduIGluIHdpdGggTWFnaWMgTGluaycsXG4gIC8vIENvbW1vbiBhdXRoZW50aWNhdGlvbiBsYWJlbHNcbiAgZW1haWw6ICdFbWFpbCcsXG4gIHBhc3N3b3JkOiAnUGFzc3dvcmQnLFxuICB1c2VybmFtZTogJ1VzZXJuYW1lJyxcbiAgcGFzc2tleTogJ1Bhc3NrZXknLFxuICAvLyBDb21tb24gYWN0aW9uIGxhYmVsc1xuICBzYXZlOiAnU2F2ZScsXG4gIGNhbmNlbDogJ0NhbmNlbCcsXG4gIG9rOiAnT2snLFxuICBvcjogJ09yJyxcbiAgdG86ICdUbycsXG4gIHdpdGg6ICdXaXRoJyxcbiAgY2xvc2U6ICdDbG9zZScsXG4gIGRlbGV0ZTogJ0RlbGV0ZScsXG4gIGFsZXJ0OiAnQWxlcnQnLFxuICBjb25maXJtOiAnQ29uZmlybScsXG4gIGxvYWRpbmc6ICdMb2FkaW5nLi4uJ1xufTtcbmV4cG9ydCBkZWZhdWx0IGdldExvY2FsaXphdGlvbihlbik7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/esm/locales/en.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/esm/locales/getLocalization.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@toolpad/core/esm/locales/getLocalization.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocalization: () => (/* binding */ getLocalization)\n/* harmony export */ });\nconst getLocalization = translations => {\n  return {\n    components: {\n      MuiLocalizationProvider: {\n        defaultProps: {\n          localeText: {\n            ...translations\n          }\n        }\n      }\n    }\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRvb2xwYWQvY29yZS9lc20vbG9jYWxlcy9nZXRMb2NhbGl6YXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcd2ViXFwxbXVzaWNcXDFtdXNpY19mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAdG9vbHBhZFxcY29yZVxcZXNtXFxsb2NhbGVzXFxnZXRMb2NhbGl6YXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGdldExvY2FsaXphdGlvbiA9IHRyYW5zbGF0aW9ucyA9PiB7XG4gIHJldHVybiB7XG4gICAgY29tcG9uZW50czoge1xuICAgICAgTXVpTG9jYWxpemF0aW9uUHJvdmlkZXI6IHtcbiAgICAgICAgZGVmYXVsdFByb3BzOiB7XG4gICAgICAgICAgbG9jYWxlVGV4dDoge1xuICAgICAgICAgICAgLi4udHJhbnNsYXRpb25zXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9O1xufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/esm/locales/getLocalization.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/esm/useNotifications/NotificationsContext.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@toolpad/core/esm/useNotifications/NotificationsContext.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationsContext: () => (/* binding */ NotificationsContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ NotificationsContext auto */ \n/**\n * @ignore - internal component.\n */ const NotificationsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRvb2xwYWQvY29yZS9lc20vdXNlTm90aWZpY2F0aW9ucy9Ob3RpZmljYXRpb25zQ29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7OzswRUFFK0I7QUFFL0I7O0NBRUMsR0FFTSxNQUFNQyx1QkFBdUIsV0FBVyxHQUFFRCxnREFBbUIsQ0FBQyxNQUFNIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcd2ViXFwxbXVzaWNcXDFtdXNpY19mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAdG9vbHBhZFxcY29yZVxcZXNtXFx1c2VOb3RpZmljYXRpb25zXFxOb3RpZmljYXRpb25zQ29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcblxuLyoqXG4gKiBAaWdub3JlIC0gaW50ZXJuYWwgY29tcG9uZW50LlxuICovXG5cbmV4cG9ydCBjb25zdCBOb3RpZmljYXRpb25zQ29udGV4dCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVDb250ZXh0KG51bGwpOyJdLCJuYW1lcyI6WyJSZWFjdCIsIk5vdGlmaWNhdGlvbnNDb250ZXh0IiwiY3JlYXRlQ29udGV4dCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/esm/useNotifications/NotificationsContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/esm/useNotifications/NotificationsProvider.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@toolpad/core/esm/useNotifications/NotificationsProvider.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationsProvider: () => (/* binding */ NotificationsProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,IconButton,Snackbar,SnackbarContent!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,IconButton,Snackbar,SnackbarContent!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,IconButton,Snackbar,SnackbarContent!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Snackbar/Snackbar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,IconButton,Snackbar,SnackbarContent!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Badge/Badge.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,IconButton,Snackbar,SnackbarContent!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,IconButton,Snackbar,SnackbarContent!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/SnackbarContent/SnackbarContent.js\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(ssr)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _toolpad_utils_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @toolpad/utils/react */ \"(ssr)/./node_modules/@toolpad/utils/esm/react.js\");\n/* harmony import */ var _mui_utils_useSlotProps__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/utils/useSlotProps */ \"(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js\");\n/* harmony import */ var _NotificationsContext_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NotificationsContext.js */ \"(ssr)/./node_modules/@toolpad/core/esm/useNotifications/NotificationsContext.js\");\n/* harmony import */ var _AppProvider_LocalizationProvider_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../AppProvider/LocalizationProvider.js */ \"(ssr)/./node_modules/@toolpad/core/esm/AppProvider/LocalizationProvider.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ NotificationsProvider auto */ var _CloseIcon;\n\n\n\n\n\n\n\n\nconst RootPropsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst defaultLocaleText = {\n    close: 'Close'\n};\nfunction Notification({ notificationKey, open, message, options, badge }) {\n    const globalLocaleText = (0,_AppProvider_LocalizationProvider_js__WEBPACK_IMPORTED_MODULE_2__.useLocaleText)();\n    const localeText = {\n        ...defaultLocaleText,\n        ...globalLocaleText\n    };\n    const { close } = (0,_toolpad_utils_react__WEBPACK_IMPORTED_MODULE_3__.useNonNullableContext)(_NotificationsContext_js__WEBPACK_IMPORTED_MODULE_4__.NotificationsContext);\n    const { severity, actionText, onAction, autoHideDuration } = options;\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Notification.useCallback[handleClose]\": (event, reason)=>{\n            if (reason === 'clickaway') {\n                return;\n            }\n            close(notificationKey);\n        }\n    }[\"Notification.useCallback[handleClose]\"], [\n        notificationKey,\n        close\n    ]);\n    const action = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            onAction ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                color: \"inherit\",\n                size: \"small\",\n                onClick: onAction,\n                children: actionText ?? 'Action'\n            }) : null,\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                size: \"small\",\n                \"aria-label\": localeText?.close,\n                title: localeText?.close,\n                color: \"inherit\",\n                onClick: handleClose,\n                children: _CloseIcon || (_CloseIcon = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    fontSize: \"small\"\n                }))\n            })\n        ]\n    });\n    const props = react__WEBPACK_IMPORTED_MODULE_0__.useContext(RootPropsContext);\n    const SnackbarComponent = props?.slots?.snackbar ?? _barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n    const snackbarSlotProps = (0,_mui_utils_useSlotProps__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({\n        elementType: SnackbarComponent,\n        ownerState: props,\n        externalSlotProps: props?.slotProps?.snackbar,\n        additionalProps: {\n            open,\n            autoHideDuration,\n            onClose: handleClose,\n            action\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SnackbarComponent, {\n        ...snackbarSlotProps,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            badgeContent: badge,\n            color: \"primary\",\n            sx: {\n                width: '100%'\n            },\n            children: severity ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                severity: severity,\n                sx: {\n                    width: '100%'\n                },\n                action: action,\n                children: message\n            }) : /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_barrel_optimize_names_Alert_Badge_Button_IconButton_Snackbar_SnackbarContent_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                message: message,\n                action: action\n            })\n        })\n    }, notificationKey);\n}\nfunction Notifications({ state }) {\n    const currentNotification = state.queue[0] ?? null;\n    return currentNotification ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Notification, {\n        ...currentNotification,\n        badge: state.queue.length > 1 ? String(state.queue.length) : null\n    }) : null;\n}\nlet nextId = 0;\nconst generateId = ()=>{\n    const id = nextId;\n    nextId += 1;\n    return id;\n};\n/**\n * Provider for Notifications. The subtree of this component can use the `useNotifications` hook to\n * access the notifications API. The notifications are shown in the same order they are requested.\n *\n * Demos:\n *\n * - [Sign-in Page](https://mui.com/toolpad/core/react-sign-in-page/)\n * - [useNotifications](https://mui.com/toolpad/core/react-use-notifications/)\n *\n * API:\n *\n * - [NotificationsProvider API](https://mui.com/toolpad/core/api/notifications-provider)\n */ function NotificationsProvider(props) {\n    const { children } = props;\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        queue: []\n    });\n    const show = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"NotificationsProvider.useCallback[show]\": (message, options = {})=>{\n            const notificationKey = options.key ?? `::toolpad-internal::notification::${generateId()}`;\n            setState({\n                \"NotificationsProvider.useCallback[show]\": (prev)=>{\n                    if (prev.queue.some({\n                        \"NotificationsProvider.useCallback[show]\": (n)=>n.notificationKey === notificationKey\n                    }[\"NotificationsProvider.useCallback[show]\"])) {\n                        // deduplicate by key\n                        return prev;\n                    }\n                    return {\n                        ...prev,\n                        queue: [\n                            ...prev.queue,\n                            {\n                                message,\n                                options,\n                                notificationKey,\n                                open: true\n                            }\n                        ]\n                    };\n                }\n            }[\"NotificationsProvider.useCallback[show]\"]);\n            return notificationKey;\n        }\n    }[\"NotificationsProvider.useCallback[show]\"], []);\n    const close = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"NotificationsProvider.useCallback[close]\": (key)=>{\n            setState({\n                \"NotificationsProvider.useCallback[close]\": (prev)=>({\n                        ...prev,\n                        queue: prev.queue.filter({\n                            \"NotificationsProvider.useCallback[close]\": (n)=>n.notificationKey !== key\n                        }[\"NotificationsProvider.useCallback[close]\"])\n                    })\n            }[\"NotificationsProvider.useCallback[close]\"]);\n        }\n    }[\"NotificationsProvider.useCallback[close]\"], []);\n    const contextValue = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"NotificationsProvider.useMemo[contextValue]\": ()=>({\n                show,\n                close\n            })\n    }[\"NotificationsProvider.useMemo[contextValue]\"], [\n        show,\n        close\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RootPropsContext.Provider, {\n        value: props,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_NotificationsContext_js__WEBPACK_IMPORTED_MODULE_4__.NotificationsContext.Provider, {\n            value: contextValue,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Notifications, {\n                    state: state\n                })\n            ]\n        })\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/esm/useNotifications/NotificationsProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/esm/useNotifications/useNotifications.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@toolpad/core/esm/useNotifications/useNotifications.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _NotificationsContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NotificationsContext.js */ \"(ssr)/./node_modules/@toolpad/core/esm/useNotifications/NotificationsContext.js\");\n\n\nconst serverNotifications = {\n  show: () => {\n    throw new Error('Not supported on server side');\n  },\n  close: () => {\n    throw new Error('Not supported on server side');\n  }\n};\nfunction useNotifications() {\n  const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_NotificationsContext_js__WEBPACK_IMPORTED_MODULE_1__.NotificationsContext);\n  if (context) {\n    return context;\n  }\n  return serverNotifications;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRvb2xwYWQvY29yZS9lc20vdXNlTm90aWZpY2F0aW9ucy91c2VOb3RpZmljYXRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQjtBQUNrQztBQUNqRTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLGtCQUFrQiw2Q0FBZ0IsQ0FBQywwRUFBb0I7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHRvb2xwYWRcXGNvcmVcXGVzbVxcdXNlTm90aWZpY2F0aW9uc1xcdXNlTm90aWZpY2F0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBOb3RpZmljYXRpb25zQ29udGV4dCB9IGZyb20gXCIuL05vdGlmaWNhdGlvbnNDb250ZXh0LmpzXCI7XG5jb25zdCBzZXJ2ZXJOb3RpZmljYXRpb25zID0ge1xuICBzaG93OiAoKSA9PiB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdOb3Qgc3VwcG9ydGVkIG9uIHNlcnZlciBzaWRlJyk7XG4gIH0sXG4gIGNsb3NlOiAoKSA9PiB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdOb3Qgc3VwcG9ydGVkIG9uIHNlcnZlciBzaWRlJyk7XG4gIH1cbn07XG5leHBvcnQgZnVuY3Rpb24gdXNlTm90aWZpY2F0aW9ucygpIHtcbiAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoTm90aWZpY2F0aW9uc0NvbnRleHQpO1xuICBpZiAoY29udGV4dCkge1xuICAgIHJldHVybiBjb250ZXh0O1xuICB9XG4gIHJldHVybiBzZXJ2ZXJOb3RpZmljYXRpb25zO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/esm/useNotifications/useNotifications.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/appendOwnerState/appendOwnerState.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/@toolpad/core/node_modules/@mui/utils/esm/appendOwnerState/appendOwnerState.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _isHostComponent_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../isHostComponent/index.js */ \"(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/isHostComponent/isHostComponent.js\");\n\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nfunction appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || (0,_isHostComponent_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(elementType)) {\n    return otherProps;\n  }\n  return {\n    ...otherProps,\n    ownerState: {\n      ...otherProps.ownerState,\n      ...ownerState\n    }\n  };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (appendOwnerState);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/appendOwnerState/appendOwnerState.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/extractEventHandlers/extractEventHandlers.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/@toolpad/core/node_modules/@mui/utils/esm/extractEventHandlers/extractEventHandlers.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * Extracts event handlers from a given object.\n * A prop is considered an event handler if it is a function and its name starts with `on`.\n *\n * @param object An object to extract event handlers from.\n * @param excludeKeys An array of keys to exclude from the returned object.\n */\nfunction extractEventHandlers(object, excludeKeys = []) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => prop.match(/^on[A-Z]/) && typeof object[prop] === 'function' && !excludeKeys.includes(prop)).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (extractEventHandlers);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRvb2xwYWQvY29yZS9ub2RlX21vZHVsZXMvQG11aS91dGlscy9lc20vZXh0cmFjdEV2ZW50SGFuZGxlcnMvZXh0cmFjdEV2ZW50SGFuZGxlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLGlFQUFlLG9CQUFvQiIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHRvb2xwYWRcXGNvcmVcXG5vZGVfbW9kdWxlc1xcQG11aVxcdXRpbHNcXGVzbVxcZXh0cmFjdEV2ZW50SGFuZGxlcnNcXGV4dHJhY3RFdmVudEhhbmRsZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0cmFjdHMgZXZlbnQgaGFuZGxlcnMgZnJvbSBhIGdpdmVuIG9iamVjdC5cbiAqIEEgcHJvcCBpcyBjb25zaWRlcmVkIGFuIGV2ZW50IGhhbmRsZXIgaWYgaXQgaXMgYSBmdW5jdGlvbiBhbmQgaXRzIG5hbWUgc3RhcnRzIHdpdGggYG9uYC5cbiAqXG4gKiBAcGFyYW0gb2JqZWN0IEFuIG9iamVjdCB0byBleHRyYWN0IGV2ZW50IGhhbmRsZXJzIGZyb20uXG4gKiBAcGFyYW0gZXhjbHVkZUtleXMgQW4gYXJyYXkgb2Yga2V5cyB0byBleGNsdWRlIGZyb20gdGhlIHJldHVybmVkIG9iamVjdC5cbiAqL1xuZnVuY3Rpb24gZXh0cmFjdEV2ZW50SGFuZGxlcnMob2JqZWN0LCBleGNsdWRlS2V5cyA9IFtdKSB7XG4gIGlmIChvYmplY3QgPT09IHVuZGVmaW5lZCkge1xuICAgIHJldHVybiB7fTtcbiAgfVxuICBjb25zdCByZXN1bHQgPSB7fTtcbiAgT2JqZWN0LmtleXMob2JqZWN0KS5maWx0ZXIocHJvcCA9PiBwcm9wLm1hdGNoKC9eb25bQS1aXS8pICYmIHR5cGVvZiBvYmplY3RbcHJvcF0gPT09ICdmdW5jdGlvbicgJiYgIWV4Y2x1ZGVLZXlzLmluY2x1ZGVzKHByb3ApKS5mb3JFYWNoKHByb3AgPT4ge1xuICAgIHJlc3VsdFtwcm9wXSA9IG9iamVjdFtwcm9wXTtcbiAgfSk7XG4gIHJldHVybiByZXN1bHQ7XG59XG5leHBvcnQgZGVmYXVsdCBleHRyYWN0RXZlbnRIYW5kbGVyczsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/extractEventHandlers/extractEventHandlers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/isHostComponent/isHostComponent.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@toolpad/core/node_modules/@mui/utils/esm/isHostComponent/isHostComponent.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * Determines if a given element is a DOM element name (i.e. not a React component).\n */\nfunction isHostComponent(element) {\n  return typeof element === 'string';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isHostComponent);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRvb2xwYWQvY29yZS9ub2RlX21vZHVsZXMvQG11aS91dGlscy9lc20vaXNIb3N0Q29tcG9uZW50L2lzSG9zdENvbXBvbmVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsZUFBZSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHRvb2xwYWRcXGNvcmVcXG5vZGVfbW9kdWxlc1xcQG11aVxcdXRpbHNcXGVzbVxcaXNIb3N0Q29tcG9uZW50XFxpc0hvc3RDb21wb25lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEZXRlcm1pbmVzIGlmIGEgZ2l2ZW4gZWxlbWVudCBpcyBhIERPTSBlbGVtZW50IG5hbWUgKGkuZS4gbm90IGEgUmVhY3QgY29tcG9uZW50KS5cbiAqL1xuZnVuY3Rpb24gaXNIb3N0Q29tcG9uZW50KGVsZW1lbnQpIHtcbiAgcmV0dXJuIHR5cGVvZiBlbGVtZW50ID09PSAnc3RyaW5nJztcbn1cbmV4cG9ydCBkZWZhdWx0IGlzSG9zdENvbXBvbmVudDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/isHostComponent/isHostComponent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/mergeSlotProps/mergeSlotProps.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/@toolpad/core/node_modules/@mui/utils/esm/mergeSlotProps/mergeSlotProps.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _extractEventHandlers_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../extractEventHandlers/index.js */ \"(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/extractEventHandlers/extractEventHandlers.js\");\n/* harmony import */ var _omitEventHandlers_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../omitEventHandlers/index.js */ \"(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/omitEventHandlers/omitEventHandlers.js\");\n\n\n\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = (0,clsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n    const mergedStyle = {\n      ...additionalProps?.style,\n      ...externalForwardedProps?.style,\n      ...externalSlotProps?.style\n    };\n    const props = {\n      ...additionalProps,\n      ...externalForwardedProps,\n      ...externalSlotProps\n    };\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = (0,_extractEventHandlers_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    ...externalForwardedProps,\n    ...externalSlotProps\n  });\n  const componentsPropsWithoutEventHandlers = (0,_omitEventHandlers_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(externalSlotProps);\n  const otherPropsWithoutEventHandlers = (0,_omitEventHandlers_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = (0,clsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(internalSlotProps?.className, additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n  const mergedStyle = {\n    ...internalSlotProps?.style,\n    ...additionalProps?.style,\n    ...externalForwardedProps?.style,\n    ...externalSlotProps?.style\n  };\n  const props = {\n    ...internalSlotProps,\n    ...additionalProps,\n    ...otherPropsWithoutEventHandlers,\n    ...componentsPropsWithoutEventHandlers\n  };\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (mergeSlotProps);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/mergeSlotProps/mergeSlotProps.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/omitEventHandlers/omitEventHandlers.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/@toolpad/core/node_modules/@mui/utils/esm/omitEventHandlers/omitEventHandlers.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * Removes event handlers from the given object.\n * A field is considered an event handler if it is a function with a name beginning with `on`.\n *\n * @param object Object to remove event handlers from.\n * @returns Object with event handlers removed.\n */\nfunction omitEventHandlers(object) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => !(prop.match(/^on[A-Z]/) && typeof object[prop] === 'function')).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (omitEventHandlers);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRvb2xwYWQvY29yZS9ub2RlX21vZHVsZXMvQG11aS91dGlscy9lc20vb21pdEV2ZW50SGFuZGxlcnMvb21pdEV2ZW50SGFuZGxlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLGlFQUFlLGlCQUFpQiIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHRvb2xwYWRcXGNvcmVcXG5vZGVfbW9kdWxlc1xcQG11aVxcdXRpbHNcXGVzbVxcb21pdEV2ZW50SGFuZGxlcnNcXG9taXRFdmVudEhhbmRsZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmVtb3ZlcyBldmVudCBoYW5kbGVycyBmcm9tIHRoZSBnaXZlbiBvYmplY3QuXG4gKiBBIGZpZWxkIGlzIGNvbnNpZGVyZWQgYW4gZXZlbnQgaGFuZGxlciBpZiBpdCBpcyBhIGZ1bmN0aW9uIHdpdGggYSBuYW1lIGJlZ2lubmluZyB3aXRoIGBvbmAuXG4gKlxuICogQHBhcmFtIG9iamVjdCBPYmplY3QgdG8gcmVtb3ZlIGV2ZW50IGhhbmRsZXJzIGZyb20uXG4gKiBAcmV0dXJucyBPYmplY3Qgd2l0aCBldmVudCBoYW5kbGVycyByZW1vdmVkLlxuICovXG5mdW5jdGlvbiBvbWl0RXZlbnRIYW5kbGVycyhvYmplY3QpIHtcbiAgaWYgKG9iamVjdCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgcmV0dXJuIHt9O1xuICB9XG4gIGNvbnN0IHJlc3VsdCA9IHt9O1xuICBPYmplY3Qua2V5cyhvYmplY3QpLmZpbHRlcihwcm9wID0+ICEocHJvcC5tYXRjaCgvXm9uW0EtWl0vKSAmJiB0eXBlb2Ygb2JqZWN0W3Byb3BdID09PSAnZnVuY3Rpb24nKSkuZm9yRWFjaChwcm9wID0+IHtcbiAgICByZXN1bHRbcHJvcF0gPSBvYmplY3RbcHJvcF07XG4gIH0pO1xuICByZXR1cm4gcmVzdWx0O1xufVxuZXhwb3J0IGRlZmF1bHQgb21pdEV2ZW50SGFuZGxlcnM7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/omitEventHandlers/omitEventHandlers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/resolveComponentProps/resolveComponentProps.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/@toolpad/core/node_modules/@mui/utils/esm/resolveComponentProps/resolveComponentProps.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nfunction resolveComponentProps(componentProps, ownerState, slotState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState, slotState);\n  }\n  return componentProps;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (resolveComponentProps);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRvb2xwYWQvY29yZS9ub2RlX21vZHVsZXMvQG11aS91dGlscy9lc20vcmVzb2x2ZUNvbXBvbmVudFByb3BzL3Jlc29sdmVDb21wb25lbnRQcm9wcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBZSxxQkFBcUIiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxub2RlX21vZHVsZXNcXEB0b29scGFkXFxjb3JlXFxub2RlX21vZHVsZXNcXEBtdWlcXHV0aWxzXFxlc21cXHJlc29sdmVDb21wb25lbnRQcm9wc1xccmVzb2x2ZUNvbXBvbmVudFByb3BzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSWYgYGNvbXBvbmVudFByb3BzYCBpcyBhIGZ1bmN0aW9uLCBjYWxscyBpdCB3aXRoIHRoZSBwcm92aWRlZCBgb3duZXJTdGF0ZWAuXG4gKiBPdGhlcndpc2UsIGp1c3QgcmV0dXJucyBgY29tcG9uZW50UHJvcHNgLlxuICovXG5mdW5jdGlvbiByZXNvbHZlQ29tcG9uZW50UHJvcHMoY29tcG9uZW50UHJvcHMsIG93bmVyU3RhdGUsIHNsb3RTdGF0ZSkge1xuICBpZiAodHlwZW9mIGNvbXBvbmVudFByb3BzID09PSAnZnVuY3Rpb24nKSB7XG4gICAgcmV0dXJuIGNvbXBvbmVudFByb3BzKG93bmVyU3RhdGUsIHNsb3RTdGF0ZSk7XG4gIH1cbiAgcmV0dXJuIGNvbXBvbmVudFByb3BzO1xufVxuZXhwb3J0IGRlZmF1bHQgcmVzb2x2ZUNvbXBvbmVudFByb3BzOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/resolveComponentProps/resolveComponentProps.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/setRef/setRef.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@toolpad/core/node_modules/@mui/utils/esm/setRef/setRef.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ setRef)\n/* harmony export */ });\n/**\n * TODO v5: consider making it private\n *\n * passes {value} to {ref}\n *\n * WARNING: Be sure to only call this inside a callback that is passed as a ref.\n * Otherwise, make sure to cleanup the previous {ref} if it changes. See\n * https://github.com/mui/material-ui/issues/13539\n *\n * Useful if you want to expose the ref of an inner component to the public API\n * while still using it inside the component.\n * @param ref A ref callback or ref object. If anything falsy, this is a no-op.\n */\nfunction setRef(ref, value) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref) {\n    ref.current = value;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRvb2xwYWQvY29yZS9ub2RlX21vZHVsZXMvQG11aS91dGlscy9lc20vc2V0UmVmL3NldFJlZi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPLElBQUk7QUFDdEI7QUFDQTtBQUNBLGlEQUFpRCxLQUFLO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNlO0FBQ2Y7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxub2RlX21vZHVsZXNcXEB0b29scGFkXFxjb3JlXFxub2RlX21vZHVsZXNcXEBtdWlcXHV0aWxzXFxlc21cXHNldFJlZlxcc2V0UmVmLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVE9ETyB2NTogY29uc2lkZXIgbWFraW5nIGl0IHByaXZhdGVcbiAqXG4gKiBwYXNzZXMge3ZhbHVlfSB0byB7cmVmfVxuICpcbiAqIFdBUk5JTkc6IEJlIHN1cmUgdG8gb25seSBjYWxsIHRoaXMgaW5zaWRlIGEgY2FsbGJhY2sgdGhhdCBpcyBwYXNzZWQgYXMgYSByZWYuXG4gKiBPdGhlcndpc2UsIG1ha2Ugc3VyZSB0byBjbGVhbnVwIHRoZSBwcmV2aW91cyB7cmVmfSBpZiBpdCBjaGFuZ2VzLiBTZWVcbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9tdWkvbWF0ZXJpYWwtdWkvaXNzdWVzLzEzNTM5XG4gKlxuICogVXNlZnVsIGlmIHlvdSB3YW50IHRvIGV4cG9zZSB0aGUgcmVmIG9mIGFuIGlubmVyIGNvbXBvbmVudCB0byB0aGUgcHVibGljIEFQSVxuICogd2hpbGUgc3RpbGwgdXNpbmcgaXQgaW5zaWRlIHRoZSBjb21wb25lbnQuXG4gKiBAcGFyYW0gcmVmIEEgcmVmIGNhbGxiYWNrIG9yIHJlZiBvYmplY3QuIElmIGFueXRoaW5nIGZhbHN5LCB0aGlzIGlzIGEgbm8tb3AuXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHNldFJlZihyZWYsIHZhbHVlKSB7XG4gIGlmICh0eXBlb2YgcmVmID09PSAnZnVuY3Rpb24nKSB7XG4gICAgcmVmKHZhbHVlKTtcbiAgfSBlbHNlIGlmIChyZWYpIHtcbiAgICByZWYuY3VycmVudCA9IHZhbHVlO1xuICB9XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/setRef/setRef.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/useForkRef/useForkRef.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@toolpad/core/node_modules/@mui/utils/esm/useForkRef/useForkRef.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useForkRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _setRef_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../setRef/index.js */ \"(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/setRef/setRef.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * Takes an array of refs and returns a new ref which will apply any modification to all of the refs.\n * This is useful when you want to have the ref used in multiple places.\n *\n * ```tsx\n * const rootRef = React.useRef<Instance>(null);\n * const refFork = useForkRef(rootRef, props.ref);\n *\n * return (\n *   <Root {...props} ref={refFork} />\n * );\n * ```\n *\n * @param {Array<React.Ref<Instance> | undefined>} refs The ref array.\n * @returns {React.RefCallback<Instance> | null} The new ref callback.\n */ function useForkRef(...refs) {\n    /**\n   * This will create a new function if the refs passed to this hook change and are all defined.\n   * This means react will call the old forkRef with `null` and the new forkRef\n   * with the ref. Cleanup naturally emerges from this behavior.\n   */ return react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"useForkRef.useMemo\": ()=>{\n            if (refs.every({\n                \"useForkRef.useMemo\": (ref)=>ref == null\n            }[\"useForkRef.useMemo\"])) {\n                return null;\n            }\n            return ({\n                \"useForkRef.useMemo\": (instance)=>{\n                    refs.forEach({\n                        \"useForkRef.useMemo\": (ref)=>{\n                            (0,_setRef_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(ref, instance);\n                        }\n                    }[\"useForkRef.useMemo\"]);\n                }\n            })[\"useForkRef.useMemo\"];\n        // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- intentionally ignoring that the dependency array must be an array literal\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"useForkRef.useMemo\"], refs);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRvb2xwYWQvY29yZS9ub2RlX21vZHVsZXMvQG11aS91dGlscy9lc20vdXNlRm9ya1JlZi91c2VGb3JrUmVmLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs2REFFK0I7QUFDUztBQUV4Qzs7Ozs7Ozs7Ozs7Ozs7O0NBZUMsR0FDYyxTQUFTRSxXQUFXLEdBQUdDLElBQUk7SUFDeEM7Ozs7R0FJQyxHQUNELE9BQU9ILDBDQUFhOzhCQUFDO1lBQ25CLElBQUlHLEtBQUtFLEtBQUs7c0NBQUNDLENBQUFBLE1BQU9BLE9BQU87c0NBQU87Z0JBQ2xDLE9BQU87WUFDVDtZQUNBO3NDQUFPQyxDQUFBQTtvQkFDTEosS0FBS0ssT0FBTzs4Q0FBQ0YsQ0FBQUE7NEJBQ1hMLDREQUFNQSxDQUFDSyxLQUFLQzt3QkFDZDs7Z0JBQ0Y7O1FBQ0EscU1BQXFNO1FBQ3JNLHVEQUF1RDtRQUN6RDs2QkFBR0o7QUFDTCIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHRvb2xwYWRcXGNvcmVcXG5vZGVfbW9kdWxlc1xcQG11aVxcdXRpbHNcXGVzbVxcdXNlRm9ya1JlZlxcdXNlRm9ya1JlZi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBzZXRSZWYgZnJvbSBcIi4uL3NldFJlZi9pbmRleC5qc1wiO1xuXG4vKipcbiAqIFRha2VzIGFuIGFycmF5IG9mIHJlZnMgYW5kIHJldHVybnMgYSBuZXcgcmVmIHdoaWNoIHdpbGwgYXBwbHkgYW55IG1vZGlmaWNhdGlvbiB0byBhbGwgb2YgdGhlIHJlZnMuXG4gKiBUaGlzIGlzIHVzZWZ1bCB3aGVuIHlvdSB3YW50IHRvIGhhdmUgdGhlIHJlZiB1c2VkIGluIG11bHRpcGxlIHBsYWNlcy5cbiAqXG4gKiBgYGB0c3hcbiAqIGNvbnN0IHJvb3RSZWYgPSBSZWFjdC51c2VSZWY8SW5zdGFuY2U+KG51bGwpO1xuICogY29uc3QgcmVmRm9yayA9IHVzZUZvcmtSZWYocm9vdFJlZiwgcHJvcHMucmVmKTtcbiAqXG4gKiByZXR1cm4gKFxuICogICA8Um9vdCB7Li4ucHJvcHN9IHJlZj17cmVmRm9ya30gLz5cbiAqICk7XG4gKiBgYGBcbiAqXG4gKiBAcGFyYW0ge0FycmF5PFJlYWN0LlJlZjxJbnN0YW5jZT4gfCB1bmRlZmluZWQ+fSByZWZzIFRoZSByZWYgYXJyYXkuXG4gKiBAcmV0dXJucyB7UmVhY3QuUmVmQ2FsbGJhY2s8SW5zdGFuY2U+IHwgbnVsbH0gVGhlIG5ldyByZWYgY2FsbGJhY2suXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUZvcmtSZWYoLi4ucmVmcykge1xuICAvKipcbiAgICogVGhpcyB3aWxsIGNyZWF0ZSBhIG5ldyBmdW5jdGlvbiBpZiB0aGUgcmVmcyBwYXNzZWQgdG8gdGhpcyBob29rIGNoYW5nZSBhbmQgYXJlIGFsbCBkZWZpbmVkLlxuICAgKiBUaGlzIG1lYW5zIHJlYWN0IHdpbGwgY2FsbCB0aGUgb2xkIGZvcmtSZWYgd2l0aCBgbnVsbGAgYW5kIHRoZSBuZXcgZm9ya1JlZlxuICAgKiB3aXRoIHRoZSByZWYuIENsZWFudXAgbmF0dXJhbGx5IGVtZXJnZXMgZnJvbSB0aGlzIGJlaGF2aW9yLlxuICAgKi9cbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4ge1xuICAgIGlmIChyZWZzLmV2ZXJ5KHJlZiA9PiByZWYgPT0gbnVsbCkpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICByZXR1cm4gaW5zdGFuY2UgPT4ge1xuICAgICAgcmVmcy5mb3JFYWNoKHJlZiA9PiB7XG4gICAgICAgIHNldFJlZihyZWYsIGluc3RhbmNlKTtcbiAgICAgIH0pO1xuICAgIH07XG4gICAgLy8gVE9ETzogdW5jb21tZW50IG9uY2Ugd2UgZW5hYmxlIGVzbGludC1wbHVnaW4tcmVhY3QtY29tcGlsZXIgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWNvbXBpbGVyL3JlYWN0LWNvbXBpbGVyIC0tIGludGVudGlvbmFsbHkgaWdub3JpbmcgdGhhdCB0aGUgZGVwZW5kZW5jeSBhcnJheSBtdXN0IGJlIGFuIGFycmF5IGxpdGVyYWxcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gIH0sIHJlZnMpO1xufSJdLCJuYW1lcyI6WyJSZWFjdCIsInNldFJlZiIsInVzZUZvcmtSZWYiLCJyZWZzIiwidXNlTWVtbyIsImV2ZXJ5IiwicmVmIiwiaW5zdGFuY2UiLCJmb3JFYWNoIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/useForkRef/useForkRef.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@toolpad/core/node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _useForkRef_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useForkRef/index.js */ \"(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/useForkRef/useForkRef.js\");\n/* harmony import */ var _appendOwnerState_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../appendOwnerState/index.js */ \"(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/appendOwnerState/appendOwnerState.js\");\n/* harmony import */ var _mergeSlotProps_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../mergeSlotProps/index.js */ \"(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/mergeSlotProps/mergeSlotProps.js\");\n/* harmony import */ var _resolveComponentProps_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resolveComponentProps/index.js */ \"(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/resolveComponentProps/resolveComponentProps.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */ function useSlotProps(parameters) {\n    const { elementType, externalSlotProps, ownerState, skipResolvingSlotProps = false, ...other } = parameters;\n    const resolvedComponentsProps = skipResolvingSlotProps ? {} : (0,_resolveComponentProps_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(externalSlotProps, ownerState);\n    const { props: mergedProps, internalRef } = (0,_mergeSlotProps_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        ...other,\n        externalSlotProps: resolvedComponentsProps\n    });\n    const ref = (0,_useForkRef_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(internalRef, resolvedComponentsProps?.ref, parameters.additionalProps?.ref);\n    const props = (0,_appendOwnerState_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(elementType, {\n        ...mergedProps,\n        ref\n    }, ownerState);\n    return props;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSlotProps);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/core/node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@toolpad/utils/esm/react.js":
/*!**************************************************!*\
  !*** ./node_modules/@toolpad/utils/esm/react.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createGlobalState: () => (/* binding */ createGlobalState),\n/* harmony export */   createProvidedContext: () => (/* binding */ createProvidedContext),\n/* harmony export */   \"default\": () => (/* binding */ getComponentDisplayName),\n/* harmony export */   interleave: () => (/* binding */ interleave),\n/* harmony export */   useAssertedContext: () => (/* binding */ useAssertedContext),\n/* harmony export */   useNonNullableContext: () => (/* binding */ useNonNullableContext),\n/* harmony export */   useTraceUpdates: () => (/* binding */ useTraceUpdates)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_is__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-is */ \"(ssr)/./node_modules/react-is/cjs/react-is.development.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n/**\n * Like `Array.prototype.join`, but for React nodes.\n */\n\nfunction interleave(items, separator) {\n  const result = [];\n  for (let i = 0; i < items.length; i += 1) {\n    if (i > 0) {\n      if (react_is__WEBPACK_IMPORTED_MODULE_2__.isElement(separator)) {\n        result.push(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(separator, {\n          key: `separator-${i}`\n        }));\n      } else {\n        result.push(separator);\n      }\n    }\n    const item = items[i];\n    result.push(item);\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n    children: result\n  });\n}\n\n/**\n * Consume a context but throw when used outside of a provider.\n */\nfunction useNonNullableContext(context, name) {\n  const maybeContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n  if (maybeContext === null || maybeContext === undefined) {\n    throw new Error(`context \"${name}\" was used without a Provider`);\n  }\n  return maybeContext;\n}\n\n/**\n * Context that throws when used outside of a provider.\n */\nfunction createProvidedContext(name) {\n  const context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(undefined);\n  const useContext = () => useNonNullableContext(context, name);\n  return [useContext, context.Provider];\n}\nfunction useAssertedContext(context) {\n  const value = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n  if (value === undefined) {\n    throw new Error('context was used without a Provider');\n  }\n  return value;\n}\n\n/**\n * Debugging tool that logs updates to props.\n */\nfunction useTraceUpdates(prefix, props) {\n  const prev = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const changedProps = {};\n    for (const key of Object.keys(props)) {\n      if (!Object.is(prev.current[key], props[key])) {\n        changedProps[key] = props[key];\n      }\n    }\n    if (Object.keys(changedProps).length > 0) {\n      // eslint-disable-next-line no-console\n      console.log(`${prefix} changed props:`, changedProps);\n    }\n    prev.current = props;\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getComponentDisplayName(Component) {\n  if (typeof Component === 'string') {\n    return Component || 'Unknown';\n  }\n  return Component.displayName || Component.name;\n}\n\n/**\n * Create a shared state to be used across the application. Returns a useState hook that\n * is synchronized on the same state between all instances where it is called.\n */\nfunction createGlobalState(initialState) {\n  let state = initialState;\n  const listeners = [];\n  const subscribe = cb => {\n    listeners.push(cb);\n    return () => {\n      const index = listeners.indexOf(cb);\n      listeners.splice(index, 1);\n    };\n  };\n  const getState = () => state;\n  const setState = newState => {\n    state = typeof newState === 'function' ? newState(state) : newState;\n    listeners.forEach(cb => cb(state));\n  };\n  const useValue = () => react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(subscribe, getState, getState);\n  const useState = () => {\n    const value = useValue();\n    return [value, setState];\n  };\n  return {\n    getState,\n    setState,\n    useValue,\n    useState,\n    subscribe\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@toolpad/utils/esm/react.js\n");

/***/ })

};
;