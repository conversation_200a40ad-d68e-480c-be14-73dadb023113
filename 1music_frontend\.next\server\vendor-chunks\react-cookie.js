"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-cookie";
exports.ids = ["vendor-chunks/react-cookie"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-cookie/esm/index.mjs":
/*!*************************************************!*\
  !*** ./node_modules/react-cookie/esm/index.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cookies: () => (/* reexport safe */ universal_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CookiesProvider: () => (/* binding */ CookiesProvider),\n/* harmony export */   useCookies: () => (/* binding */ useCookies),\n/* harmony export */   withCookies: () => (/* binding */ withCookies)\n/* harmony export */ });\n/* harmony import */ var universal_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! universal-cookie */ \"(ssr)/./node_modules/universal-cookie/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\nconst CookiesContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext(new universal_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"]());\nconst { Provider, Consumer } = CookiesContext;\n\nclass CookiesProvider extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props) {\n        super(props);\n        if (props.cookies) {\n            this.cookies = props.cookies;\n        }\n        else {\n            this.cookies = new universal_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"](undefined, props.defaultSetOptions);\n        }\n    }\n    render() {\n        return react__WEBPACK_IMPORTED_MODULE_1__.createElement(Provider, { value: this.cookies }, this.props.children);\n    }\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nfunction getDefaultExportFromCjs (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\nvar reactIs = {exports: {}};\n\nvar reactIs_production_min = {};\n\n/** @license React v16.13.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredReactIs_production_min;\n\nfunction requireReactIs_production_min () {\n\tif (hasRequiredReactIs_production_min) return reactIs_production_min;\n\thasRequiredReactIs_production_min = 1;\nvar b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,q=b?\n\tSymbol.for(\"react.suspense_list\"):60120,r=b?Symbol.for(\"react.memo\"):60115,t=b?Symbol.for(\"react.lazy\"):60116,v=b?Symbol.for(\"react.block\"):60121,w=b?Symbol.for(\"react.fundamental\"):60117,x=b?Symbol.for(\"react.responder\"):60118,y=b?Symbol.for(\"react.scope\"):60119;\n\tfunction z(a){if(\"object\"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function A(a){return z(a)===m}reactIs_production_min.AsyncMode=l;reactIs_production_min.ConcurrentMode=m;reactIs_production_min.ContextConsumer=k;reactIs_production_min.ContextProvider=h;reactIs_production_min.Element=c;reactIs_production_min.ForwardRef=n;reactIs_production_min.Fragment=e;reactIs_production_min.Lazy=t;reactIs_production_min.Memo=r;reactIs_production_min.Portal=d;\n\treactIs_production_min.Profiler=g;reactIs_production_min.StrictMode=f;reactIs_production_min.Suspense=p;reactIs_production_min.isAsyncMode=function(a){return A(a)||z(a)===l};reactIs_production_min.isConcurrentMode=A;reactIs_production_min.isContextConsumer=function(a){return z(a)===k};reactIs_production_min.isContextProvider=function(a){return z(a)===h};reactIs_production_min.isElement=function(a){return \"object\"===typeof a&&null!==a&&a.$$typeof===c};reactIs_production_min.isForwardRef=function(a){return z(a)===n};reactIs_production_min.isFragment=function(a){return z(a)===e};reactIs_production_min.isLazy=function(a){return z(a)===t};\n\treactIs_production_min.isMemo=function(a){return z(a)===r};reactIs_production_min.isPortal=function(a){return z(a)===d};reactIs_production_min.isProfiler=function(a){return z(a)===g};reactIs_production_min.isStrictMode=function(a){return z(a)===f};reactIs_production_min.isSuspense=function(a){return z(a)===p};\n\treactIs_production_min.isValidElementType=function(a){return \"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===w||a.$$typeof===x||a.$$typeof===y||a.$$typeof===v)};reactIs_production_min.typeOf=z;\n\treturn reactIs_production_min;\n}\n\nvar reactIs_development = {};\n\n/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredReactIs_development;\n\nfunction requireReactIs_development () {\n\tif (hasRequiredReactIs_development) return reactIs_development;\n\thasRequiredReactIs_development = 1;\n\n\n\n\tif (true) {\n\t  (function() {\n\n\t// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n\t// nor polyfill, then a plain number is used for performance.\n\tvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\n\tvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\n\tvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\n\tvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\n\tvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\n\tvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\n\tvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\n\tvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n\t// (unstable) APIs that have been removed. Can we remove the symbols?\n\n\tvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\n\tvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\n\tvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\n\tvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\n\tvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\n\tvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\n\tvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\n\tvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\n\tvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\n\tvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\n\tvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\n\tfunction isValidElementType(type) {\n\t  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n\t  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n\t}\n\n\tfunction typeOf(object) {\n\t  if (typeof object === 'object' && object !== null) {\n\t    var $$typeof = object.$$typeof;\n\n\t    switch ($$typeof) {\n\t      case REACT_ELEMENT_TYPE:\n\t        var type = object.type;\n\n\t        switch (type) {\n\t          case REACT_ASYNC_MODE_TYPE:\n\t          case REACT_CONCURRENT_MODE_TYPE:\n\t          case REACT_FRAGMENT_TYPE:\n\t          case REACT_PROFILER_TYPE:\n\t          case REACT_STRICT_MODE_TYPE:\n\t          case REACT_SUSPENSE_TYPE:\n\t            return type;\n\n\t          default:\n\t            var $$typeofType = type && type.$$typeof;\n\n\t            switch ($$typeofType) {\n\t              case REACT_CONTEXT_TYPE:\n\t              case REACT_FORWARD_REF_TYPE:\n\t              case REACT_LAZY_TYPE:\n\t              case REACT_MEMO_TYPE:\n\t              case REACT_PROVIDER_TYPE:\n\t                return $$typeofType;\n\n\t              default:\n\t                return $$typeof;\n\t            }\n\n\t        }\n\n\t      case REACT_PORTAL_TYPE:\n\t        return $$typeof;\n\t    }\n\t  }\n\n\t  return undefined;\n\t} // AsyncMode is deprecated along with isAsyncMode\n\n\tvar AsyncMode = REACT_ASYNC_MODE_TYPE;\n\tvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\n\tvar ContextConsumer = REACT_CONTEXT_TYPE;\n\tvar ContextProvider = REACT_PROVIDER_TYPE;\n\tvar Element = REACT_ELEMENT_TYPE;\n\tvar ForwardRef = REACT_FORWARD_REF_TYPE;\n\tvar Fragment = REACT_FRAGMENT_TYPE;\n\tvar Lazy = REACT_LAZY_TYPE;\n\tvar Memo = REACT_MEMO_TYPE;\n\tvar Portal = REACT_PORTAL_TYPE;\n\tvar Profiler = REACT_PROFILER_TYPE;\n\tvar StrictMode = REACT_STRICT_MODE_TYPE;\n\tvar Suspense = REACT_SUSPENSE_TYPE;\n\tvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\n\tfunction isAsyncMode(object) {\n\t  {\n\t    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n\t      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n\t      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n\t    }\n\t  }\n\n\t  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n\t}\n\tfunction isConcurrentMode(object) {\n\t  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n\t}\n\tfunction isContextConsumer(object) {\n\t  return typeOf(object) === REACT_CONTEXT_TYPE;\n\t}\n\tfunction isContextProvider(object) {\n\t  return typeOf(object) === REACT_PROVIDER_TYPE;\n\t}\n\tfunction isElement(object) {\n\t  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n\t}\n\tfunction isForwardRef(object) {\n\t  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n\t}\n\tfunction isFragment(object) {\n\t  return typeOf(object) === REACT_FRAGMENT_TYPE;\n\t}\n\tfunction isLazy(object) {\n\t  return typeOf(object) === REACT_LAZY_TYPE;\n\t}\n\tfunction isMemo(object) {\n\t  return typeOf(object) === REACT_MEMO_TYPE;\n\t}\n\tfunction isPortal(object) {\n\t  return typeOf(object) === REACT_PORTAL_TYPE;\n\t}\n\tfunction isProfiler(object) {\n\t  return typeOf(object) === REACT_PROFILER_TYPE;\n\t}\n\tfunction isStrictMode(object) {\n\t  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n\t}\n\tfunction isSuspense(object) {\n\t  return typeOf(object) === REACT_SUSPENSE_TYPE;\n\t}\n\n\treactIs_development.AsyncMode = AsyncMode;\n\treactIs_development.ConcurrentMode = ConcurrentMode;\n\treactIs_development.ContextConsumer = ContextConsumer;\n\treactIs_development.ContextProvider = ContextProvider;\n\treactIs_development.Element = Element;\n\treactIs_development.ForwardRef = ForwardRef;\n\treactIs_development.Fragment = Fragment;\n\treactIs_development.Lazy = Lazy;\n\treactIs_development.Memo = Memo;\n\treactIs_development.Portal = Portal;\n\treactIs_development.Profiler = Profiler;\n\treactIs_development.StrictMode = StrictMode;\n\treactIs_development.Suspense = Suspense;\n\treactIs_development.isAsyncMode = isAsyncMode;\n\treactIs_development.isConcurrentMode = isConcurrentMode;\n\treactIs_development.isContextConsumer = isContextConsumer;\n\treactIs_development.isContextProvider = isContextProvider;\n\treactIs_development.isElement = isElement;\n\treactIs_development.isForwardRef = isForwardRef;\n\treactIs_development.isFragment = isFragment;\n\treactIs_development.isLazy = isLazy;\n\treactIs_development.isMemo = isMemo;\n\treactIs_development.isPortal = isPortal;\n\treactIs_development.isProfiler = isProfiler;\n\treactIs_development.isStrictMode = isStrictMode;\n\treactIs_development.isSuspense = isSuspense;\n\treactIs_development.isValidElementType = isValidElementType;\n\treactIs_development.typeOf = typeOf;\n\t  })();\n\t}\n\treturn reactIs_development;\n}\n\nvar hasRequiredReactIs;\n\nfunction requireReactIs () {\n\tif (hasRequiredReactIs) return reactIs.exports;\n\thasRequiredReactIs = 1;\n\n\tif (false) {} else {\n\t  reactIs.exports = requireReactIs_development();\n\t}\n\treturn reactIs.exports;\n}\n\nvar hoistNonReactStatics_cjs;\nvar hasRequiredHoistNonReactStatics_cjs;\n\nfunction requireHoistNonReactStatics_cjs () {\n\tif (hasRequiredHoistNonReactStatics_cjs) return hoistNonReactStatics_cjs;\n\thasRequiredHoistNonReactStatics_cjs = 1;\n\n\tvar reactIs = requireReactIs();\n\n\t/**\n\t * Copyright 2015, Yahoo! Inc.\n\t * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n\t */\n\tvar REACT_STATICS = {\n\t  childContextTypes: true,\n\t  contextType: true,\n\t  contextTypes: true,\n\t  defaultProps: true,\n\t  displayName: true,\n\t  getDefaultProps: true,\n\t  getDerivedStateFromError: true,\n\t  getDerivedStateFromProps: true,\n\t  mixins: true,\n\t  propTypes: true,\n\t  type: true\n\t};\n\tvar KNOWN_STATICS = {\n\t  name: true,\n\t  length: true,\n\t  prototype: true,\n\t  caller: true,\n\t  callee: true,\n\t  arguments: true,\n\t  arity: true\n\t};\n\tvar FORWARD_REF_STATICS = {\n\t  '$$typeof': true,\n\t  render: true,\n\t  defaultProps: true,\n\t  displayName: true,\n\t  propTypes: true\n\t};\n\tvar MEMO_STATICS = {\n\t  '$$typeof': true,\n\t  compare: true,\n\t  defaultProps: true,\n\t  displayName: true,\n\t  propTypes: true,\n\t  type: true\n\t};\n\tvar TYPE_STATICS = {};\n\tTYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\n\tTYPE_STATICS[reactIs.Memo] = MEMO_STATICS;\n\n\tfunction getStatics(component) {\n\t  // React v16.11 and below\n\t  if (reactIs.isMemo(component)) {\n\t    return MEMO_STATICS;\n\t  } // React v16.12 and above\n\n\n\t  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n\t}\n\n\tvar defineProperty = Object.defineProperty;\n\tvar getOwnPropertyNames = Object.getOwnPropertyNames;\n\tvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\n\tvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\tvar getPrototypeOf = Object.getPrototypeOf;\n\tvar objectPrototype = Object.prototype;\n\tfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n\t  if (typeof sourceComponent !== 'string') {\n\t    // don't hoist over string (html) components\n\t    if (objectPrototype) {\n\t      var inheritedComponent = getPrototypeOf(sourceComponent);\n\n\t      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n\t        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n\t      }\n\t    }\n\n\t    var keys = getOwnPropertyNames(sourceComponent);\n\n\t    if (getOwnPropertySymbols) {\n\t      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n\t    }\n\n\t    var targetStatics = getStatics(targetComponent);\n\t    var sourceStatics = getStatics(sourceComponent);\n\n\t    for (var i = 0; i < keys.length; ++i) {\n\t      var key = keys[i];\n\n\t      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n\t        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n\t        try {\n\t          // Avoid failures from read-only properties\n\t          defineProperty(targetComponent, key, descriptor);\n\t        } catch (e) {}\n\t      }\n\t    }\n\t  }\n\n\t  return targetComponent;\n\t}\n\n\thoistNonReactStatics_cjs = hoistNonReactStatics;\n\treturn hoistNonReactStatics_cjs;\n}\n\nvar hoistNonReactStatics_cjsExports = requireHoistNonReactStatics_cjs();\nvar hoistStatics = /*@__PURE__*/getDefaultExportFromCjs(hoistNonReactStatics_cjsExports);\n\nfunction withCookies(WrappedComponent) {\n    // @ts-ignore\n    const name = WrappedComponent.displayName || WrappedComponent.name;\n    class CookieWrapper extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n        constructor() {\n            super(...arguments);\n            this.onChange = () => {\n                // Make sure to update children with new values\n                this.forceUpdate();\n            };\n        }\n        listen() {\n            this.props.cookies.addChangeListener(this.onChange);\n        }\n        unlisten(cookies) {\n            (cookies || this.props.cookies).removeChangeListener(this.onChange);\n        }\n        componentDidMount() {\n            this.listen();\n        }\n        componentDidUpdate(prevProps) {\n            if (prevProps.cookies !== this.props.cookies) {\n                this.unlisten(prevProps.cookies);\n                this.listen();\n            }\n        }\n        componentWillUnmount() {\n            this.unlisten();\n        }\n        render() {\n            const _a = this.props, { forwardedRef, cookies } = _a, restProps = __rest(_a, [\"forwardedRef\", \"cookies\"]);\n            const allCookies = cookies.getAll({ doNotUpdate: true });\n            return (react__WEBPACK_IMPORTED_MODULE_1__.createElement(WrappedComponent, Object.assign({}, restProps, { ref: forwardedRef, cookies: cookies, allCookies: allCookies })));\n        }\n    }\n    CookieWrapper.displayName = `withCookies(${name})`;\n    CookieWrapper.WrappedComponent = WrappedComponent;\n    const ForwardedComponent = react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((props, ref) => {\n        return (react__WEBPACK_IMPORTED_MODULE_1__.createElement(Consumer, null, (cookies) => (react__WEBPACK_IMPORTED_MODULE_1__.createElement(CookieWrapper, Object.assign({ cookies: cookies }, props, { forwardedRef: ref })))));\n    });\n    ForwardedComponent.displayName = CookieWrapper.displayName;\n    ForwardedComponent.WrappedComponent = CookieWrapper.WrappedComponent;\n    return hoistStatics(ForwardedComponent, WrappedComponent);\n}\n\nfunction isInBrowser() {\n    return (typeof window !== 'undefined' &&\n        typeof window.document !== 'undefined' &&\n        typeof window.document.createElement !== 'undefined');\n}\n\nfunction useCookies(dependencies, options) {\n    const cookies = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CookiesContext);\n    if (!cookies) {\n        throw new Error('Missing <CookiesProvider>');\n    }\n    const defaultOptions = { doNotUpdate: true };\n    const getOptions = Object.assign(Object.assign({}, defaultOptions), options);\n    const [allCookies, setCookies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(() => cookies.getAll(getOptions));\n    if (isInBrowser()) {\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n            function onChange() {\n                const newCookies = cookies.getAll(getOptions);\n                if (shouldUpdate(dependencies || null, newCookies, allCookies)) {\n                    setCookies(newCookies);\n                }\n            }\n            cookies.addChangeListener(onChange);\n            return () => {\n                cookies.removeChangeListener(onChange);\n            };\n        }, [cookies, allCookies]);\n    }\n    const setCookie = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => cookies.set.bind(cookies), [cookies]);\n    const removeCookie = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => cookies.remove.bind(cookies), [cookies]);\n    const updateCookies = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => cookies.update.bind(cookies), [cookies]);\n    return [allCookies, setCookie, removeCookie, updateCookies];\n}\nfunction shouldUpdate(dependencies, newCookies, oldCookies) {\n    if (!dependencies) {\n        return true;\n    }\n    for (let dependency of dependencies) {\n        if (newCookies[dependency] !== oldCookies[dependency]) {\n            return true;\n        }\n    }\n    return false;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-cookie/esm/index.mjs\n");

/***/ })

};
;