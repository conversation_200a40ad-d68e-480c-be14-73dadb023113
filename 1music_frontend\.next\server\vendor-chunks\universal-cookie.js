"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/universal-cookie";
exports.ids = ["vendor-chunks/universal-cookie"];
exports.modules = {

/***/ "(ssr)/./node_modules/universal-cookie/esm/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/universal-cookie/esm/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Cookies)\n/* harmony export */ });\nvar cookie = {};\n\n/*!\n * cookie\n * Copyright(c) 2012-2014 Roman Shtylman\n * Copyright(c) 2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\nvar hasRequiredCookie;\n\nfunction requireCookie () {\n\tif (hasRequiredCookie) return cookie;\n\thasRequiredCookie = 1;\n\n\t/**\n\t * Module exports.\n\t * @public\n\t */\n\n\tcookie.parse = parse;\n\tcookie.serialize = serialize;\n\n\t/**\n\t * Module variables.\n\t * @private\n\t */\n\n\tvar __toString = Object.prototype.toString;\n\tvar __hasOwnProperty = Object.prototype.hasOwnProperty;\n\n\t/**\n\t * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n\t * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n\t * which has been replaced by the token definition in RFC 7230 appendix B.\n\t *\n\t * cookie-name       = token\n\t * token             = 1*tchar\n\t * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n\t *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n\t *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n\t */\n\n\tvar cookieNameRegExp = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/;\n\n\t/**\n\t * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n\t *\n\t * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n\t * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n\t *                     ; US-ASCII characters excluding CTLs,\n\t *                     ; whitespace DQUOTE, comma, semicolon,\n\t *                     ; and backslash\n\t */\n\n\tvar cookieValueRegExp = /^(\"?)[\\u0021\\u0023-\\u002B\\u002D-\\u003A\\u003C-\\u005B\\u005D-\\u007E]*\\1$/;\n\n\t/**\n\t * RegExp to match domain-value in RFC 6265 sec 4.1.1\n\t *\n\t * domain-value      = <subdomain>\n\t *                     ; defined in [RFC1034], Section 3.5, as\n\t *                     ; enhanced by [RFC1123], Section 2.1\n\t * <subdomain>       = <label> | <subdomain> \".\" <label>\n\t * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n\t *                     Labels must be 63 characters or less.\n\t *                     'let-dig' not 'letter' in the first char, per RFC1123\n\t * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n\t * <let-dig-hyp>     = <let-dig> | \"-\"\n\t * <let-dig>         = <letter> | <digit>\n\t * <letter>          = any one of the 52 alphabetic characters A through Z in\n\t *                     upper case and a through z in lower case\n\t * <digit>           = any one of the ten digits 0 through 9\n\t *\n\t * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n\t *\n\t * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n\t * character is not permitted, but a trailing %x2E (\".\"), if present, will\n\t * cause the user agent to ignore the attribute.)\n\t */\n\n\tvar domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n\t/**\n\t * RegExp to match path-value in RFC 6265 sec 4.1.1\n\t *\n\t * path-value        = <any CHAR except CTLs or \";\">\n\t * CHAR              = %x01-7F\n\t *                     ; defined in RFC 5234 appendix B.1\n\t */\n\n\tvar pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\n\t/**\n\t * Parse a cookie header.\n\t *\n\t * Parse the given cookie header string into an object\n\t * The object has the various cookies as keys(names) => values\n\t *\n\t * @param {string} str\n\t * @param {object} [opt]\n\t * @return {object}\n\t * @public\n\t */\n\n\tfunction parse(str, opt) {\n\t  if (typeof str !== 'string') {\n\t    throw new TypeError('argument str must be a string');\n\t  }\n\n\t  var obj = {};\n\t  var len = str.length;\n\t  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n\t  if (len < 2) return obj;\n\n\t  var dec = (opt && opt.decode) || decode;\n\t  var index = 0;\n\t  var eqIdx = 0;\n\t  var endIdx = 0;\n\n\t  do {\n\t    eqIdx = str.indexOf('=', index);\n\t    if (eqIdx === -1) break; // No more cookie pairs.\n\n\t    endIdx = str.indexOf(';', index);\n\n\t    if (endIdx === -1) {\n\t      endIdx = len;\n\t    } else if (eqIdx > endIdx) {\n\t      // backtrack on prior semicolon\n\t      index = str.lastIndexOf(';', eqIdx - 1) + 1;\n\t      continue;\n\t    }\n\n\t    var keyStartIdx = startIndex(str, index, eqIdx);\n\t    var keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n\t    var key = str.slice(keyStartIdx, keyEndIdx);\n\n\t    // only assign once\n\t    if (!__hasOwnProperty.call(obj, key)) {\n\t      var valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n\t      var valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n\t      if (str.charCodeAt(valStartIdx) === 0x22 /* \" */ && str.charCodeAt(valEndIdx - 1) === 0x22 /* \" */) {\n\t        valStartIdx++;\n\t        valEndIdx--;\n\t      }\n\n\t      var val = str.slice(valStartIdx, valEndIdx);\n\t      obj[key] = tryDecode(val, dec);\n\t    }\n\n\t    index = endIdx + 1;\n\t  } while (index < len);\n\n\t  return obj;\n\t}\n\n\tfunction startIndex(str, index, max) {\n\t  do {\n\t    var code = str.charCodeAt(index);\n\t    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n\t  } while (++index < max);\n\t  return max;\n\t}\n\n\tfunction endIndex(str, index, min) {\n\t  while (index > min) {\n\t    var code = str.charCodeAt(--index);\n\t    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n\t  }\n\t  return min;\n\t}\n\n\t/**\n\t * Serialize data into a cookie header.\n\t *\n\t * Serialize a name value pair into a cookie string suitable for\n\t * http headers. An optional options object specifies cookie parameters.\n\t *\n\t * serialize('foo', 'bar', { httpOnly: true })\n\t *   => \"foo=bar; httpOnly\"\n\t *\n\t * @param {string} name\n\t * @param {string} val\n\t * @param {object} [opt]\n\t * @return {string}\n\t * @public\n\t */\n\n\tfunction serialize(name, val, opt) {\n\t  var enc = (opt && opt.encode) || encodeURIComponent;\n\n\t  if (typeof enc !== 'function') {\n\t    throw new TypeError('option encode is invalid');\n\t  }\n\n\t  if (!cookieNameRegExp.test(name)) {\n\t    throw new TypeError('argument name is invalid');\n\t  }\n\n\t  var value = enc(val);\n\n\t  if (!cookieValueRegExp.test(value)) {\n\t    throw new TypeError('argument val is invalid');\n\t  }\n\n\t  var str = name + '=' + value;\n\t  if (!opt) return str;\n\n\t  if (null != opt.maxAge) {\n\t    var maxAge = Math.floor(opt.maxAge);\n\n\t    if (!isFinite(maxAge)) {\n\t      throw new TypeError('option maxAge is invalid')\n\t    }\n\n\t    str += '; Max-Age=' + maxAge;\n\t  }\n\n\t  if (opt.domain) {\n\t    if (!domainValueRegExp.test(opt.domain)) {\n\t      throw new TypeError('option domain is invalid');\n\t    }\n\n\t    str += '; Domain=' + opt.domain;\n\t  }\n\n\t  if (opt.path) {\n\t    if (!pathValueRegExp.test(opt.path)) {\n\t      throw new TypeError('option path is invalid');\n\t    }\n\n\t    str += '; Path=' + opt.path;\n\t  }\n\n\t  if (opt.expires) {\n\t    var expires = opt.expires;\n\n\t    if (!isDate(expires) || isNaN(expires.valueOf())) {\n\t      throw new TypeError('option expires is invalid');\n\t    }\n\n\t    str += '; Expires=' + expires.toUTCString();\n\t  }\n\n\t  if (opt.httpOnly) {\n\t    str += '; HttpOnly';\n\t  }\n\n\t  if (opt.secure) {\n\t    str += '; Secure';\n\t  }\n\n\t  if (opt.partitioned) {\n\t    str += '; Partitioned';\n\t  }\n\n\t  if (opt.priority) {\n\t    var priority = typeof opt.priority === 'string'\n\t      ? opt.priority.toLowerCase() : opt.priority;\n\n\t    switch (priority) {\n\t      case 'low':\n\t        str += '; Priority=Low';\n\t        break\n\t      case 'medium':\n\t        str += '; Priority=Medium';\n\t        break\n\t      case 'high':\n\t        str += '; Priority=High';\n\t        break\n\t      default:\n\t        throw new TypeError('option priority is invalid')\n\t    }\n\t  }\n\n\t  if (opt.sameSite) {\n\t    var sameSite = typeof opt.sameSite === 'string'\n\t      ? opt.sameSite.toLowerCase() : opt.sameSite;\n\n\t    switch (sameSite) {\n\t      case true:\n\t        str += '; SameSite=Strict';\n\t        break;\n\t      case 'lax':\n\t        str += '; SameSite=Lax';\n\t        break;\n\t      case 'strict':\n\t        str += '; SameSite=Strict';\n\t        break;\n\t      case 'none':\n\t        str += '; SameSite=None';\n\t        break;\n\t      default:\n\t        throw new TypeError('option sameSite is invalid');\n\t    }\n\t  }\n\n\t  return str;\n\t}\n\n\t/**\n\t * URL-decode string value. Optimized to skip native call when no %.\n\t *\n\t * @param {string} str\n\t * @returns {string}\n\t */\n\n\tfunction decode (str) {\n\t  return str.indexOf('%') !== -1\n\t    ? decodeURIComponent(str)\n\t    : str\n\t}\n\n\t/**\n\t * Determine if value is a Date.\n\t *\n\t * @param {*} val\n\t * @private\n\t */\n\n\tfunction isDate (val) {\n\t  return __toString.call(val) === '[object Date]';\n\t}\n\n\t/**\n\t * Try decoding a string using a decoding function.\n\t *\n\t * @param {string} str\n\t * @param {function} decode\n\t * @private\n\t */\n\n\tfunction tryDecode(str, decode) {\n\t  try {\n\t    return decode(str);\n\t  } catch (e) {\n\t    return str;\n\t  }\n\t}\n\treturn cookie;\n}\n\nvar cookieExports = requireCookie();\n\nfunction hasDocumentCookie() {\n    const testingValue = typeof global === 'undefined'\n        ? undefined\n        : global.TEST_HAS_DOCUMENT_COOKIE;\n    if (typeof testingValue === 'boolean') {\n        return testingValue;\n    }\n    // Can we get/set cookies on document.cookie?\n    return typeof document === 'object' && typeof document.cookie === 'string';\n}\nfunction parseCookies(cookies) {\n    if (typeof cookies === 'string') {\n        return cookieExports.parse(cookies);\n    }\n    else if (typeof cookies === 'object' && cookies !== null) {\n        return cookies;\n    }\n    else {\n        return {};\n    }\n}\nfunction readCookie(value, options = {}) {\n    const cleanValue = cleanupCookieValue(value);\n    if (!options.doNotParse) {\n        try {\n            return JSON.parse(cleanValue);\n        }\n        catch (e) {\n            // At least we tried\n        }\n    }\n    // Ignore clean value if we failed the deserialization\n    // It is not relevant anymore to trim those values\n    return value;\n}\nfunction cleanupCookieValue(value) {\n    // express prepend j: before serializing a cookie\n    if (value && value[0] === 'j' && value[1] === ':') {\n        return value.substr(2);\n    }\n    return value;\n}\n\nclass Cookies {\n    constructor(cookies, defaultSetOptions = {}) {\n        this.changeListeners = [];\n        this.HAS_DOCUMENT_COOKIE = false;\n        this.update = () => {\n            if (!this.HAS_DOCUMENT_COOKIE) {\n                return;\n            }\n            const previousCookies = this.cookies;\n            this.cookies = cookieExports.parse(document.cookie);\n            this._checkChanges(previousCookies);\n        };\n        const domCookies = typeof document === 'undefined' ? '' : document.cookie;\n        this.cookies = parseCookies(cookies || domCookies);\n        this.defaultSetOptions = defaultSetOptions;\n        this.HAS_DOCUMENT_COOKIE = hasDocumentCookie();\n    }\n    _emitChange(params) {\n        for (let i = 0; i < this.changeListeners.length; ++i) {\n            this.changeListeners[i](params);\n        }\n    }\n    _checkChanges(previousCookies) {\n        const names = new Set(Object.keys(previousCookies).concat(Object.keys(this.cookies)));\n        names.forEach((name) => {\n            if (previousCookies[name] !== this.cookies[name]) {\n                this._emitChange({\n                    name,\n                    value: readCookie(this.cookies[name]),\n                });\n            }\n        });\n    }\n    _startPolling() {\n        this.pollingInterval = setInterval(this.update, 300);\n    }\n    _stopPolling() {\n        if (this.pollingInterval) {\n            clearInterval(this.pollingInterval);\n        }\n    }\n    get(name, options = {}) {\n        if (!options.doNotUpdate) {\n            this.update();\n        }\n        return readCookie(this.cookies[name], options);\n    }\n    getAll(options = {}) {\n        if (!options.doNotUpdate) {\n            this.update();\n        }\n        const result = {};\n        for (let name in this.cookies) {\n            result[name] = readCookie(this.cookies[name], options);\n        }\n        return result;\n    }\n    set(name, value, options) {\n        if (options) {\n            options = Object.assign(Object.assign({}, this.defaultSetOptions), options);\n        }\n        else {\n            options = this.defaultSetOptions;\n        }\n        const stringValue = typeof value === 'string' ? value : JSON.stringify(value);\n        this.cookies = Object.assign(Object.assign({}, this.cookies), { [name]: stringValue });\n        if (this.HAS_DOCUMENT_COOKIE) {\n            document.cookie = cookieExports.serialize(name, stringValue, options);\n        }\n        this._emitChange({ name, value, options });\n    }\n    remove(name, options) {\n        const finalOptions = (options = Object.assign(Object.assign(Object.assign({}, this.defaultSetOptions), options), { expires: new Date(1970, 1, 1, 0, 0, 1), maxAge: 0 }));\n        this.cookies = Object.assign({}, this.cookies);\n        delete this.cookies[name];\n        if (this.HAS_DOCUMENT_COOKIE) {\n            document.cookie = cookieExports.serialize(name, '', finalOptions);\n        }\n        this._emitChange({ name, value: undefined, options });\n    }\n    addChangeListener(callback) {\n        this.changeListeners.push(callback);\n        if (this.HAS_DOCUMENT_COOKIE && this.changeListeners.length === 1) {\n            if (typeof window === 'object' && 'cookieStore' in window) {\n                window.cookieStore.addEventListener('change', this.update);\n            }\n            else {\n                this._startPolling();\n            }\n        }\n    }\n    removeChangeListener(callback) {\n        const idx = this.changeListeners.indexOf(callback);\n        if (idx >= 0) {\n            this.changeListeners.splice(idx, 1);\n        }\n        if (this.HAS_DOCUMENT_COOKIE && this.changeListeners.length === 0) {\n            if (typeof window === 'object' && 'cookieStore' in window) {\n                window.cookieStore.removeEventListener('change', this.update);\n            }\n            else {\n                this._stopPolling();\n            }\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW5pdmVyc2FsLWNvb2tpZS9lc20vaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUIsMEJBQTBCO0FBQzFCLDBCQUEwQjtBQUMxQjs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQiwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxtREFBbUQsS0FBSyxrQ0FBa0MsS0FBSzs7QUFFL0Y7QUFDQTtBQUNBO0FBQ0EsbURBQW1EO0FBQ25EO0FBQ0EsMEJBQTBCO0FBQzFCOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQixZQUFZLFFBQVE7QUFDcEIsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDhCQUE4Qjs7QUFFOUIsNEJBQTRCOztBQUU1QjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLEtBQUs7O0FBRUw7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixnQkFBZ0I7QUFDOUMsbUJBQW1CO0FBQ25CO0FBQ0EsWUFBWSxRQUFRO0FBQ3BCLFlBQVksUUFBUTtBQUNwQixZQUFZLFFBQVE7QUFDcEIsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGVBQWU7QUFDZjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxlQUFlO0FBQ2Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsZUFBZTtBQUNmOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGVBQWU7QUFDZjs7QUFFQTtBQUNBLGVBQWU7QUFDZjs7QUFFQTtBQUNBLGVBQWU7QUFDZjs7QUFFQTtBQUNBLGVBQWU7QUFDZjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsWUFBWSxRQUFRO0FBQ3BCLGNBQWM7QUFDZDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVksR0FBRztBQUNmO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQixZQUFZLFVBQVU7QUFDdEI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUM7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLCtDQUErQztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixpQ0FBaUM7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9EQUFvRDtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELG1CQUFtQixxQkFBcUI7QUFDN0Y7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNCQUFzQjtBQUNqRDtBQUNBO0FBQ0Esb0ZBQW9GLHVDQUF1QyxtREFBbUQ7QUFDOUssdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLGlDQUFpQztBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcd2ViXFwxbXVzaWNcXDFtdXNpY19mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFx1bml2ZXJzYWwtY29va2llXFxlc21cXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgY29va2llID0ge307XG5cbi8qIVxuICogY29va2llXG4gKiBDb3B5cmlnaHQoYykgMjAxMi0yMDE0IFJvbWFuIFNodHlsbWFuXG4gKiBDb3B5cmlnaHQoYykgMjAxNSBEb3VnbGFzIENocmlzdG9waGVyIFdpbHNvblxuICogTUlUIExpY2Vuc2VkXG4gKi9cblxudmFyIGhhc1JlcXVpcmVkQ29va2llO1xuXG5mdW5jdGlvbiByZXF1aXJlQ29va2llICgpIHtcblx0aWYgKGhhc1JlcXVpcmVkQ29va2llKSByZXR1cm4gY29va2llO1xuXHRoYXNSZXF1aXJlZENvb2tpZSA9IDE7XG5cblx0LyoqXG5cdCAqIE1vZHVsZSBleHBvcnRzLlxuXHQgKiBAcHVibGljXG5cdCAqL1xuXG5cdGNvb2tpZS5wYXJzZSA9IHBhcnNlO1xuXHRjb29raWUuc2VyaWFsaXplID0gc2VyaWFsaXplO1xuXG5cdC8qKlxuXHQgKiBNb2R1bGUgdmFyaWFibGVzLlxuXHQgKiBAcHJpdmF0ZVxuXHQgKi9cblxuXHR2YXIgX190b1N0cmluZyA9IE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmc7XG5cdHZhciBfX2hhc093blByb3BlcnR5ID0gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eTtcblxuXHQvKipcblx0ICogUmVnRXhwIHRvIG1hdGNoIGNvb2tpZS1uYW1lIGluIFJGQyA2MjY1IHNlYyA0LjEuMVxuXHQgKiBUaGlzIHJlZmVycyBvdXQgdG8gdGhlIG9ic29sZXRlZCBkZWZpbml0aW9uIG9mIHRva2VuIGluIFJGQyAyNjE2IHNlYyAyLjJcblx0ICogd2hpY2ggaGFzIGJlZW4gcmVwbGFjZWQgYnkgdGhlIHRva2VuIGRlZmluaXRpb24gaW4gUkZDIDcyMzAgYXBwZW5kaXggQi5cblx0ICpcblx0ICogY29va2llLW5hbWUgICAgICAgPSB0b2tlblxuXHQgKiB0b2tlbiAgICAgICAgICAgICA9IDEqdGNoYXJcblx0ICogdGNoYXIgICAgICAgICAgICAgPSBcIiFcIiAvIFwiI1wiIC8gXCIkXCIgLyBcIiVcIiAvIFwiJlwiIC8gXCInXCIgL1xuXHQgKiAgICAgICAgICAgICAgICAgICAgIFwiKlwiIC8gXCIrXCIgLyBcIi1cIiAvIFwiLlwiIC8gXCJeXCIgLyBcIl9cIiAvXG5cdCAqICAgICAgICAgICAgICAgICAgICAgXCJgXCIgLyBcInxcIiAvIFwiflwiIC8gRElHSVQgLyBBTFBIQVxuXHQgKi9cblxuXHR2YXIgY29va2llTmFtZVJlZ0V4cCA9IC9eWyEjJCUmJyorXFwtLl5fYHx+MC05QS1aYS16XSskLztcblxuXHQvKipcblx0ICogUmVnRXhwIHRvIG1hdGNoIGNvb2tpZS12YWx1ZSBpbiBSRkMgNjI2NSBzZWMgNC4xLjFcblx0ICpcblx0ICogY29va2llLXZhbHVlICAgICAgPSAqY29va2llLW9jdGV0IC8gKCBEUVVPVEUgKmNvb2tpZS1vY3RldCBEUVVPVEUgKVxuXHQgKiBjb29raWUtb2N0ZXQgICAgICA9ICV4MjEgLyAleDIzLTJCIC8gJXgyRC0zQSAvICV4M0MtNUIgLyAleDVELTdFXG5cdCAqICAgICAgICAgICAgICAgICAgICAgOyBVUy1BU0NJSSBjaGFyYWN0ZXJzIGV4Y2x1ZGluZyBDVExzLFxuXHQgKiAgICAgICAgICAgICAgICAgICAgIDsgd2hpdGVzcGFjZSBEUVVPVEUsIGNvbW1hLCBzZW1pY29sb24sXG5cdCAqICAgICAgICAgICAgICAgICAgICAgOyBhbmQgYmFja3NsYXNoXG5cdCAqL1xuXG5cdHZhciBjb29raWVWYWx1ZVJlZ0V4cCA9IC9eKFwiPylbXFx1MDAyMVxcdTAwMjMtXFx1MDAyQlxcdTAwMkQtXFx1MDAzQVxcdTAwM0MtXFx1MDA1QlxcdTAwNUQtXFx1MDA3RV0qXFwxJC87XG5cblx0LyoqXG5cdCAqIFJlZ0V4cCB0byBtYXRjaCBkb21haW4tdmFsdWUgaW4gUkZDIDYyNjUgc2VjIDQuMS4xXG5cdCAqXG5cdCAqIGRvbWFpbi12YWx1ZSAgICAgID0gPHN1YmRvbWFpbj5cblx0ICogICAgICAgICAgICAgICAgICAgICA7IGRlZmluZWQgaW4gW1JGQzEwMzRdLCBTZWN0aW9uIDMuNSwgYXNcblx0ICogICAgICAgICAgICAgICAgICAgICA7IGVuaGFuY2VkIGJ5IFtSRkMxMTIzXSwgU2VjdGlvbiAyLjFcblx0ICogPHN1YmRvbWFpbj4gICAgICAgPSA8bGFiZWw+IHwgPHN1YmRvbWFpbj4gXCIuXCIgPGxhYmVsPlxuXHQgKiA8bGFiZWw+ICAgICAgICAgICA9IDxsZXQtZGlnPiBbIFsgPGxkaC1zdHI+IF0gPGxldC1kaWc+IF1cblx0ICogICAgICAgICAgICAgICAgICAgICBMYWJlbHMgbXVzdCBiZSA2MyBjaGFyYWN0ZXJzIG9yIGxlc3MuXG5cdCAqICAgICAgICAgICAgICAgICAgICAgJ2xldC1kaWcnIG5vdCAnbGV0dGVyJyBpbiB0aGUgZmlyc3QgY2hhciwgcGVyIFJGQzExMjNcblx0ICogPGxkaC1zdHI+ICAgICAgICAgPSA8bGV0LWRpZy1oeXA+IHwgPGxldC1kaWctaHlwPiA8bGRoLXN0cj5cblx0ICogPGxldC1kaWctaHlwPiAgICAgPSA8bGV0LWRpZz4gfCBcIi1cIlxuXHQgKiA8bGV0LWRpZz4gICAgICAgICA9IDxsZXR0ZXI+IHwgPGRpZ2l0PlxuXHQgKiA8bGV0dGVyPiAgICAgICAgICA9IGFueSBvbmUgb2YgdGhlIDUyIGFscGhhYmV0aWMgY2hhcmFjdGVycyBBIHRocm91Z2ggWiBpblxuXHQgKiAgICAgICAgICAgICAgICAgICAgIHVwcGVyIGNhc2UgYW5kIGEgdGhyb3VnaCB6IGluIGxvd2VyIGNhc2Vcblx0ICogPGRpZ2l0PiAgICAgICAgICAgPSBhbnkgb25lIG9mIHRoZSB0ZW4gZGlnaXRzIDAgdGhyb3VnaCA5XG5cdCAqXG5cdCAqIEtlZXAgc3VwcG9ydCBmb3IgbGVhZGluZyBkb3Q6IGh0dHBzOi8vZ2l0aHViLmNvbS9qc2h0dHAvY29va2llL2lzc3Vlcy8xNzNcblx0ICpcblx0ICogPiAoTm90ZSB0aGF0IGEgbGVhZGluZyAleDJFIChcIi5cIiksIGlmIHByZXNlbnQsIGlzIGlnbm9yZWQgZXZlbiB0aG91Z2ggdGhhdFxuXHQgKiBjaGFyYWN0ZXIgaXMgbm90IHBlcm1pdHRlZCwgYnV0IGEgdHJhaWxpbmcgJXgyRSAoXCIuXCIpLCBpZiBwcmVzZW50LCB3aWxsXG5cdCAqIGNhdXNlIHRoZSB1c2VyIGFnZW50IHRvIGlnbm9yZSB0aGUgYXR0cmlidXRlLilcblx0ICovXG5cblx0dmFyIGRvbWFpblZhbHVlUmVnRXhwID0gL14oWy5dP1thLXowLTldKFthLXowLTktXXswLDYxfVthLXowLTldKT8pKFsuXVthLXowLTldKFthLXowLTktXXswLDYxfVthLXowLTldKT8pKiQvaTtcblxuXHQvKipcblx0ICogUmVnRXhwIHRvIG1hdGNoIHBhdGgtdmFsdWUgaW4gUkZDIDYyNjUgc2VjIDQuMS4xXG5cdCAqXG5cdCAqIHBhdGgtdmFsdWUgICAgICAgID0gPGFueSBDSEFSIGV4Y2VwdCBDVExzIG9yIFwiO1wiPlxuXHQgKiBDSEFSICAgICAgICAgICAgICA9ICV4MDEtN0Zcblx0ICogICAgICAgICAgICAgICAgICAgICA7IGRlZmluZWQgaW4gUkZDIDUyMzQgYXBwZW5kaXggQi4xXG5cdCAqL1xuXG5cdHZhciBwYXRoVmFsdWVSZWdFeHAgPSAvXltcXHUwMDIwLVxcdTAwM0FcXHUwMDNELVxcdTAwN0VdKiQvO1xuXG5cdC8qKlxuXHQgKiBQYXJzZSBhIGNvb2tpZSBoZWFkZXIuXG5cdCAqXG5cdCAqIFBhcnNlIHRoZSBnaXZlbiBjb29raWUgaGVhZGVyIHN0cmluZyBpbnRvIGFuIG9iamVjdFxuXHQgKiBUaGUgb2JqZWN0IGhhcyB0aGUgdmFyaW91cyBjb29raWVzIGFzIGtleXMobmFtZXMpID0+IHZhbHVlc1xuXHQgKlxuXHQgKiBAcGFyYW0ge3N0cmluZ30gc3RyXG5cdCAqIEBwYXJhbSB7b2JqZWN0fSBbb3B0XVxuXHQgKiBAcmV0dXJuIHtvYmplY3R9XG5cdCAqIEBwdWJsaWNcblx0ICovXG5cblx0ZnVuY3Rpb24gcGFyc2Uoc3RyLCBvcHQpIHtcblx0ICBpZiAodHlwZW9mIHN0ciAhPT0gJ3N0cmluZycpIHtcblx0ICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2FyZ3VtZW50IHN0ciBtdXN0IGJlIGEgc3RyaW5nJyk7XG5cdCAgfVxuXG5cdCAgdmFyIG9iaiA9IHt9O1xuXHQgIHZhciBsZW4gPSBzdHIubGVuZ3RoO1xuXHQgIC8vIFJGQyA2MjY1IHNlYyA0LjEuMSwgUkZDIDI2MTYgMi4yIGRlZmluZXMgYSBjb29raWUgbmFtZSBjb25zaXN0cyBvZiBvbmUgY2hhciBtaW5pbXVtLCBwbHVzICc9Jy5cblx0ICBpZiAobGVuIDwgMikgcmV0dXJuIG9iajtcblxuXHQgIHZhciBkZWMgPSAob3B0ICYmIG9wdC5kZWNvZGUpIHx8IGRlY29kZTtcblx0ICB2YXIgaW5kZXggPSAwO1xuXHQgIHZhciBlcUlkeCA9IDA7XG5cdCAgdmFyIGVuZElkeCA9IDA7XG5cblx0ICBkbyB7XG5cdCAgICBlcUlkeCA9IHN0ci5pbmRleE9mKCc9JywgaW5kZXgpO1xuXHQgICAgaWYgKGVxSWR4ID09PSAtMSkgYnJlYWs7IC8vIE5vIG1vcmUgY29va2llIHBhaXJzLlxuXG5cdCAgICBlbmRJZHggPSBzdHIuaW5kZXhPZignOycsIGluZGV4KTtcblxuXHQgICAgaWYgKGVuZElkeCA9PT0gLTEpIHtcblx0ICAgICAgZW5kSWR4ID0gbGVuO1xuXHQgICAgfSBlbHNlIGlmIChlcUlkeCA+IGVuZElkeCkge1xuXHQgICAgICAvLyBiYWNrdHJhY2sgb24gcHJpb3Igc2VtaWNvbG9uXG5cdCAgICAgIGluZGV4ID0gc3RyLmxhc3RJbmRleE9mKCc7JywgZXFJZHggLSAxKSArIDE7XG5cdCAgICAgIGNvbnRpbnVlO1xuXHQgICAgfVxuXG5cdCAgICB2YXIga2V5U3RhcnRJZHggPSBzdGFydEluZGV4KHN0ciwgaW5kZXgsIGVxSWR4KTtcblx0ICAgIHZhciBrZXlFbmRJZHggPSBlbmRJbmRleChzdHIsIGVxSWR4LCBrZXlTdGFydElkeCk7XG5cdCAgICB2YXIga2V5ID0gc3RyLnNsaWNlKGtleVN0YXJ0SWR4LCBrZXlFbmRJZHgpO1xuXG5cdCAgICAvLyBvbmx5IGFzc2lnbiBvbmNlXG5cdCAgICBpZiAoIV9faGFzT3duUHJvcGVydHkuY2FsbChvYmosIGtleSkpIHtcblx0ICAgICAgdmFyIHZhbFN0YXJ0SWR4ID0gc3RhcnRJbmRleChzdHIsIGVxSWR4ICsgMSwgZW5kSWR4KTtcblx0ICAgICAgdmFyIHZhbEVuZElkeCA9IGVuZEluZGV4KHN0ciwgZW5kSWR4LCB2YWxTdGFydElkeCk7XG5cblx0ICAgICAgaWYgKHN0ci5jaGFyQ29kZUF0KHZhbFN0YXJ0SWR4KSA9PT0gMHgyMiAvKiBcIiAqLyAmJiBzdHIuY2hhckNvZGVBdCh2YWxFbmRJZHggLSAxKSA9PT0gMHgyMiAvKiBcIiAqLykge1xuXHQgICAgICAgIHZhbFN0YXJ0SWR4Kys7XG5cdCAgICAgICAgdmFsRW5kSWR4LS07XG5cdCAgICAgIH1cblxuXHQgICAgICB2YXIgdmFsID0gc3RyLnNsaWNlKHZhbFN0YXJ0SWR4LCB2YWxFbmRJZHgpO1xuXHQgICAgICBvYmpba2V5XSA9IHRyeURlY29kZSh2YWwsIGRlYyk7XG5cdCAgICB9XG5cblx0ICAgIGluZGV4ID0gZW5kSWR4ICsgMTtcblx0ICB9IHdoaWxlIChpbmRleCA8IGxlbik7XG5cblx0ICByZXR1cm4gb2JqO1xuXHR9XG5cblx0ZnVuY3Rpb24gc3RhcnRJbmRleChzdHIsIGluZGV4LCBtYXgpIHtcblx0ICBkbyB7XG5cdCAgICB2YXIgY29kZSA9IHN0ci5jaGFyQ29kZUF0KGluZGV4KTtcblx0ICAgIGlmIChjb2RlICE9PSAweDIwIC8qICAgKi8gJiYgY29kZSAhPT0gMHgwOSAvKiBcXHQgKi8pIHJldHVybiBpbmRleDtcblx0ICB9IHdoaWxlICgrK2luZGV4IDwgbWF4KTtcblx0ICByZXR1cm4gbWF4O1xuXHR9XG5cblx0ZnVuY3Rpb24gZW5kSW5kZXgoc3RyLCBpbmRleCwgbWluKSB7XG5cdCAgd2hpbGUgKGluZGV4ID4gbWluKSB7XG5cdCAgICB2YXIgY29kZSA9IHN0ci5jaGFyQ29kZUF0KC0taW5kZXgpO1xuXHQgICAgaWYgKGNvZGUgIT09IDB4MjAgLyogICAqLyAmJiBjb2RlICE9PSAweDA5IC8qIFxcdCAqLykgcmV0dXJuIGluZGV4ICsgMTtcblx0ICB9XG5cdCAgcmV0dXJuIG1pbjtcblx0fVxuXG5cdC8qKlxuXHQgKiBTZXJpYWxpemUgZGF0YSBpbnRvIGEgY29va2llIGhlYWRlci5cblx0ICpcblx0ICogU2VyaWFsaXplIGEgbmFtZSB2YWx1ZSBwYWlyIGludG8gYSBjb29raWUgc3RyaW5nIHN1aXRhYmxlIGZvclxuXHQgKiBodHRwIGhlYWRlcnMuIEFuIG9wdGlvbmFsIG9wdGlvbnMgb2JqZWN0IHNwZWNpZmllcyBjb29raWUgcGFyYW1ldGVycy5cblx0ICpcblx0ICogc2VyaWFsaXplKCdmb28nLCAnYmFyJywgeyBodHRwT25seTogdHJ1ZSB9KVxuXHQgKiAgID0+IFwiZm9vPWJhcjsgaHR0cE9ubHlcIlxuXHQgKlxuXHQgKiBAcGFyYW0ge3N0cmluZ30gbmFtZVxuXHQgKiBAcGFyYW0ge3N0cmluZ30gdmFsXG5cdCAqIEBwYXJhbSB7b2JqZWN0fSBbb3B0XVxuXHQgKiBAcmV0dXJuIHtzdHJpbmd9XG5cdCAqIEBwdWJsaWNcblx0ICovXG5cblx0ZnVuY3Rpb24gc2VyaWFsaXplKG5hbWUsIHZhbCwgb3B0KSB7XG5cdCAgdmFyIGVuYyA9IChvcHQgJiYgb3B0LmVuY29kZSkgfHwgZW5jb2RlVVJJQ29tcG9uZW50O1xuXG5cdCAgaWYgKHR5cGVvZiBlbmMgIT09ICdmdW5jdGlvbicpIHtcblx0ICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ29wdGlvbiBlbmNvZGUgaXMgaW52YWxpZCcpO1xuXHQgIH1cblxuXHQgIGlmICghY29va2llTmFtZVJlZ0V4cC50ZXN0KG5hbWUpKSB7XG5cdCAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdhcmd1bWVudCBuYW1lIGlzIGludmFsaWQnKTtcblx0ICB9XG5cblx0ICB2YXIgdmFsdWUgPSBlbmModmFsKTtcblxuXHQgIGlmICghY29va2llVmFsdWVSZWdFeHAudGVzdCh2YWx1ZSkpIHtcblx0ICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2FyZ3VtZW50IHZhbCBpcyBpbnZhbGlkJyk7XG5cdCAgfVxuXG5cdCAgdmFyIHN0ciA9IG5hbWUgKyAnPScgKyB2YWx1ZTtcblx0ICBpZiAoIW9wdCkgcmV0dXJuIHN0cjtcblxuXHQgIGlmIChudWxsICE9IG9wdC5tYXhBZ2UpIHtcblx0ICAgIHZhciBtYXhBZ2UgPSBNYXRoLmZsb29yKG9wdC5tYXhBZ2UpO1xuXG5cdCAgICBpZiAoIWlzRmluaXRlKG1heEFnZSkpIHtcblx0ICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignb3B0aW9uIG1heEFnZSBpcyBpbnZhbGlkJylcblx0ICAgIH1cblxuXHQgICAgc3RyICs9ICc7IE1heC1BZ2U9JyArIG1heEFnZTtcblx0ICB9XG5cblx0ICBpZiAob3B0LmRvbWFpbikge1xuXHQgICAgaWYgKCFkb21haW5WYWx1ZVJlZ0V4cC50ZXN0KG9wdC5kb21haW4pKSB7XG5cdCAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ29wdGlvbiBkb21haW4gaXMgaW52YWxpZCcpO1xuXHQgICAgfVxuXG5cdCAgICBzdHIgKz0gJzsgRG9tYWluPScgKyBvcHQuZG9tYWluO1xuXHQgIH1cblxuXHQgIGlmIChvcHQucGF0aCkge1xuXHQgICAgaWYgKCFwYXRoVmFsdWVSZWdFeHAudGVzdChvcHQucGF0aCkpIHtcblx0ICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignb3B0aW9uIHBhdGggaXMgaW52YWxpZCcpO1xuXHQgICAgfVxuXG5cdCAgICBzdHIgKz0gJzsgUGF0aD0nICsgb3B0LnBhdGg7XG5cdCAgfVxuXG5cdCAgaWYgKG9wdC5leHBpcmVzKSB7XG5cdCAgICB2YXIgZXhwaXJlcyA9IG9wdC5leHBpcmVzO1xuXG5cdCAgICBpZiAoIWlzRGF0ZShleHBpcmVzKSB8fCBpc05hTihleHBpcmVzLnZhbHVlT2YoKSkpIHtcblx0ICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignb3B0aW9uIGV4cGlyZXMgaXMgaW52YWxpZCcpO1xuXHQgICAgfVxuXG5cdCAgICBzdHIgKz0gJzsgRXhwaXJlcz0nICsgZXhwaXJlcy50b1VUQ1N0cmluZygpO1xuXHQgIH1cblxuXHQgIGlmIChvcHQuaHR0cE9ubHkpIHtcblx0ICAgIHN0ciArPSAnOyBIdHRwT25seSc7XG5cdCAgfVxuXG5cdCAgaWYgKG9wdC5zZWN1cmUpIHtcblx0ICAgIHN0ciArPSAnOyBTZWN1cmUnO1xuXHQgIH1cblxuXHQgIGlmIChvcHQucGFydGl0aW9uZWQpIHtcblx0ICAgIHN0ciArPSAnOyBQYXJ0aXRpb25lZCc7XG5cdCAgfVxuXG5cdCAgaWYgKG9wdC5wcmlvcml0eSkge1xuXHQgICAgdmFyIHByaW9yaXR5ID0gdHlwZW9mIG9wdC5wcmlvcml0eSA9PT0gJ3N0cmluZydcblx0ICAgICAgPyBvcHQucHJpb3JpdHkudG9Mb3dlckNhc2UoKSA6IG9wdC5wcmlvcml0eTtcblxuXHQgICAgc3dpdGNoIChwcmlvcml0eSkge1xuXHQgICAgICBjYXNlICdsb3cnOlxuXHQgICAgICAgIHN0ciArPSAnOyBQcmlvcml0eT1Mb3cnO1xuXHQgICAgICAgIGJyZWFrXG5cdCAgICAgIGNhc2UgJ21lZGl1bSc6XG5cdCAgICAgICAgc3RyICs9ICc7IFByaW9yaXR5PU1lZGl1bSc7XG5cdCAgICAgICAgYnJlYWtcblx0ICAgICAgY2FzZSAnaGlnaCc6XG5cdCAgICAgICAgc3RyICs9ICc7IFByaW9yaXR5PUhpZ2gnO1xuXHQgICAgICAgIGJyZWFrXG5cdCAgICAgIGRlZmF1bHQ6XG5cdCAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignb3B0aW9uIHByaW9yaXR5IGlzIGludmFsaWQnKVxuXHQgICAgfVxuXHQgIH1cblxuXHQgIGlmIChvcHQuc2FtZVNpdGUpIHtcblx0ICAgIHZhciBzYW1lU2l0ZSA9IHR5cGVvZiBvcHQuc2FtZVNpdGUgPT09ICdzdHJpbmcnXG5cdCAgICAgID8gb3B0LnNhbWVTaXRlLnRvTG93ZXJDYXNlKCkgOiBvcHQuc2FtZVNpdGU7XG5cblx0ICAgIHN3aXRjaCAoc2FtZVNpdGUpIHtcblx0ICAgICAgY2FzZSB0cnVlOlxuXHQgICAgICAgIHN0ciArPSAnOyBTYW1lU2l0ZT1TdHJpY3QnO1xuXHQgICAgICAgIGJyZWFrO1xuXHQgICAgICBjYXNlICdsYXgnOlxuXHQgICAgICAgIHN0ciArPSAnOyBTYW1lU2l0ZT1MYXgnO1xuXHQgICAgICAgIGJyZWFrO1xuXHQgICAgICBjYXNlICdzdHJpY3QnOlxuXHQgICAgICAgIHN0ciArPSAnOyBTYW1lU2l0ZT1TdHJpY3QnO1xuXHQgICAgICAgIGJyZWFrO1xuXHQgICAgICBjYXNlICdub25lJzpcblx0ICAgICAgICBzdHIgKz0gJzsgU2FtZVNpdGU9Tm9uZSc7XG5cdCAgICAgICAgYnJlYWs7XG5cdCAgICAgIGRlZmF1bHQ6XG5cdCAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignb3B0aW9uIHNhbWVTaXRlIGlzIGludmFsaWQnKTtcblx0ICAgIH1cblx0ICB9XG5cblx0ICByZXR1cm4gc3RyO1xuXHR9XG5cblx0LyoqXG5cdCAqIFVSTC1kZWNvZGUgc3RyaW5nIHZhbHVlLiBPcHRpbWl6ZWQgdG8gc2tpcCBuYXRpdmUgY2FsbCB3aGVuIG5vICUuXG5cdCAqXG5cdCAqIEBwYXJhbSB7c3RyaW5nfSBzdHJcblx0ICogQHJldHVybnMge3N0cmluZ31cblx0ICovXG5cblx0ZnVuY3Rpb24gZGVjb2RlIChzdHIpIHtcblx0ICByZXR1cm4gc3RyLmluZGV4T2YoJyUnKSAhPT0gLTFcblx0ICAgID8gZGVjb2RlVVJJQ29tcG9uZW50KHN0cilcblx0ICAgIDogc3RyXG5cdH1cblxuXHQvKipcblx0ICogRGV0ZXJtaW5lIGlmIHZhbHVlIGlzIGEgRGF0ZS5cblx0ICpcblx0ICogQHBhcmFtIHsqfSB2YWxcblx0ICogQHByaXZhdGVcblx0ICovXG5cblx0ZnVuY3Rpb24gaXNEYXRlICh2YWwpIHtcblx0ICByZXR1cm4gX190b1N0cmluZy5jYWxsKHZhbCkgPT09ICdbb2JqZWN0IERhdGVdJztcblx0fVxuXG5cdC8qKlxuXHQgKiBUcnkgZGVjb2RpbmcgYSBzdHJpbmcgdXNpbmcgYSBkZWNvZGluZyBmdW5jdGlvbi5cblx0ICpcblx0ICogQHBhcmFtIHtzdHJpbmd9IHN0clxuXHQgKiBAcGFyYW0ge2Z1bmN0aW9ufSBkZWNvZGVcblx0ICogQHByaXZhdGVcblx0ICovXG5cblx0ZnVuY3Rpb24gdHJ5RGVjb2RlKHN0ciwgZGVjb2RlKSB7XG5cdCAgdHJ5IHtcblx0ICAgIHJldHVybiBkZWNvZGUoc3RyKTtcblx0ICB9IGNhdGNoIChlKSB7XG5cdCAgICByZXR1cm4gc3RyO1xuXHQgIH1cblx0fVxuXHRyZXR1cm4gY29va2llO1xufVxuXG52YXIgY29va2llRXhwb3J0cyA9IHJlcXVpcmVDb29raWUoKTtcblxuZnVuY3Rpb24gaGFzRG9jdW1lbnRDb29raWUoKSB7XG4gICAgY29uc3QgdGVzdGluZ1ZhbHVlID0gdHlwZW9mIGdsb2JhbCA9PT0gJ3VuZGVmaW5lZCdcbiAgICAgICAgPyB1bmRlZmluZWRcbiAgICAgICAgOiBnbG9iYWwuVEVTVF9IQVNfRE9DVU1FTlRfQ09PS0lFO1xuICAgIGlmICh0eXBlb2YgdGVzdGluZ1ZhbHVlID09PSAnYm9vbGVhbicpIHtcbiAgICAgICAgcmV0dXJuIHRlc3RpbmdWYWx1ZTtcbiAgICB9XG4gICAgLy8gQ2FuIHdlIGdldC9zZXQgY29va2llcyBvbiBkb2N1bWVudC5jb29raWU/XG4gICAgcmV0dXJuIHR5cGVvZiBkb2N1bWVudCA9PT0gJ29iamVjdCcgJiYgdHlwZW9mIGRvY3VtZW50LmNvb2tpZSA9PT0gJ3N0cmluZyc7XG59XG5mdW5jdGlvbiBwYXJzZUNvb2tpZXMoY29va2llcykge1xuICAgIGlmICh0eXBlb2YgY29va2llcyA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIGNvb2tpZUV4cG9ydHMucGFyc2UoY29va2llcyk7XG4gICAgfVxuICAgIGVsc2UgaWYgKHR5cGVvZiBjb29raWVzID09PSAnb2JqZWN0JyAmJiBjb29raWVzICE9PSBudWxsKSB7XG4gICAgICAgIHJldHVybiBjb29raWVzO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHt9O1xuICAgIH1cbn1cbmZ1bmN0aW9uIHJlYWRDb29raWUodmFsdWUsIG9wdGlvbnMgPSB7fSkge1xuICAgIGNvbnN0IGNsZWFuVmFsdWUgPSBjbGVhbnVwQ29va2llVmFsdWUodmFsdWUpO1xuICAgIGlmICghb3B0aW9ucy5kb05vdFBhcnNlKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gSlNPTi5wYXJzZShjbGVhblZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgLy8gQXQgbGVhc3Qgd2UgdHJpZWRcbiAgICAgICAgfVxuICAgIH1cbiAgICAvLyBJZ25vcmUgY2xlYW4gdmFsdWUgaWYgd2UgZmFpbGVkIHRoZSBkZXNlcmlhbGl6YXRpb25cbiAgICAvLyBJdCBpcyBub3QgcmVsZXZhbnQgYW55bW9yZSB0byB0cmltIHRob3NlIHZhbHVlc1xuICAgIHJldHVybiB2YWx1ZTtcbn1cbmZ1bmN0aW9uIGNsZWFudXBDb29raWVWYWx1ZSh2YWx1ZSkge1xuICAgIC8vIGV4cHJlc3MgcHJlcGVuZCBqOiBiZWZvcmUgc2VyaWFsaXppbmcgYSBjb29raWVcbiAgICBpZiAodmFsdWUgJiYgdmFsdWVbMF0gPT09ICdqJyAmJiB2YWx1ZVsxXSA9PT0gJzonKSB7XG4gICAgICAgIHJldHVybiB2YWx1ZS5zdWJzdHIoMik7XG4gICAgfVxuICAgIHJldHVybiB2YWx1ZTtcbn1cblxuY2xhc3MgQ29va2llcyB7XG4gICAgY29uc3RydWN0b3IoY29va2llcywgZGVmYXVsdFNldE9wdGlvbnMgPSB7fSkge1xuICAgICAgICB0aGlzLmNoYW5nZUxpc3RlbmVycyA9IFtdO1xuICAgICAgICB0aGlzLkhBU19ET0NVTUVOVF9DT09LSUUgPSBmYWxzZTtcbiAgICAgICAgdGhpcy51cGRhdGUgPSAoKSA9PiB7XG4gICAgICAgICAgICBpZiAoIXRoaXMuSEFTX0RPQ1VNRU5UX0NPT0tJRSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHByZXZpb3VzQ29va2llcyA9IHRoaXMuY29va2llcztcbiAgICAgICAgICAgIHRoaXMuY29va2llcyA9IGNvb2tpZUV4cG9ydHMucGFyc2UoZG9jdW1lbnQuY29va2llKTtcbiAgICAgICAgICAgIHRoaXMuX2NoZWNrQ2hhbmdlcyhwcmV2aW91c0Nvb2tpZXMpO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCBkb21Db29raWVzID0gdHlwZW9mIGRvY3VtZW50ID09PSAndW5kZWZpbmVkJyA/ICcnIDogZG9jdW1lbnQuY29va2llO1xuICAgICAgICB0aGlzLmNvb2tpZXMgPSBwYXJzZUNvb2tpZXMoY29va2llcyB8fCBkb21Db29raWVzKTtcbiAgICAgICAgdGhpcy5kZWZhdWx0U2V0T3B0aW9ucyA9IGRlZmF1bHRTZXRPcHRpb25zO1xuICAgICAgICB0aGlzLkhBU19ET0NVTUVOVF9DT09LSUUgPSBoYXNEb2N1bWVudENvb2tpZSgpO1xuICAgIH1cbiAgICBfZW1pdENoYW5nZShwYXJhbXMpIHtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmNoYW5nZUxpc3RlbmVycy5sZW5ndGg7ICsraSkge1xuICAgICAgICAgICAgdGhpcy5jaGFuZ2VMaXN0ZW5lcnNbaV0ocGFyYW1zKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBfY2hlY2tDaGFuZ2VzKHByZXZpb3VzQ29va2llcykge1xuICAgICAgICBjb25zdCBuYW1lcyA9IG5ldyBTZXQoT2JqZWN0LmtleXMocHJldmlvdXNDb29raWVzKS5jb25jYXQoT2JqZWN0LmtleXModGhpcy5jb29raWVzKSkpO1xuICAgICAgICBuYW1lcy5mb3JFYWNoKChuYW1lKSA9PiB7XG4gICAgICAgICAgICBpZiAocHJldmlvdXNDb29raWVzW25hbWVdICE9PSB0aGlzLmNvb2tpZXNbbmFtZV0pIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9lbWl0Q2hhbmdlKHtcbiAgICAgICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHJlYWRDb29raWUodGhpcy5jb29raWVzW25hbWVdKSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuICAgIF9zdGFydFBvbGxpbmcoKSB7XG4gICAgICAgIHRoaXMucG9sbGluZ0ludGVydmFsID0gc2V0SW50ZXJ2YWwodGhpcy51cGRhdGUsIDMwMCk7XG4gICAgfVxuICAgIF9zdG9wUG9sbGluZygpIHtcbiAgICAgICAgaWYgKHRoaXMucG9sbGluZ0ludGVydmFsKSB7XG4gICAgICAgICAgICBjbGVhckludGVydmFsKHRoaXMucG9sbGluZ0ludGVydmFsKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBnZXQobmFtZSwgb3B0aW9ucyA9IHt9KSB7XG4gICAgICAgIGlmICghb3B0aW9ucy5kb05vdFVwZGF0ZSkge1xuICAgICAgICAgICAgdGhpcy51cGRhdGUoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVhZENvb2tpZSh0aGlzLmNvb2tpZXNbbmFtZV0sIG9wdGlvbnMpO1xuICAgIH1cbiAgICBnZXRBbGwob3B0aW9ucyA9IHt9KSB7XG4gICAgICAgIGlmICghb3B0aW9ucy5kb05vdFVwZGF0ZSkge1xuICAgICAgICAgICAgdGhpcy51cGRhdGUoKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCByZXN1bHQgPSB7fTtcbiAgICAgICAgZm9yIChsZXQgbmFtZSBpbiB0aGlzLmNvb2tpZXMpIHtcbiAgICAgICAgICAgIHJlc3VsdFtuYW1lXSA9IHJlYWRDb29raWUodGhpcy5jb29raWVzW25hbWVdLCBvcHRpb25zKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH1cbiAgICBzZXQobmFtZSwgdmFsdWUsIG9wdGlvbnMpIHtcbiAgICAgICAgaWYgKG9wdGlvbnMpIHtcbiAgICAgICAgICAgIG9wdGlvbnMgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHRoaXMuZGVmYXVsdFNldE9wdGlvbnMpLCBvcHRpb25zKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIG9wdGlvbnMgPSB0aGlzLmRlZmF1bHRTZXRPcHRpb25zO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHN0cmluZ1ZhbHVlID0gdHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyA/IHZhbHVlIDogSlNPTi5zdHJpbmdpZnkodmFsdWUpO1xuICAgICAgICB0aGlzLmNvb2tpZXMgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHRoaXMuY29va2llcyksIHsgW25hbWVdOiBzdHJpbmdWYWx1ZSB9KTtcbiAgICAgICAgaWYgKHRoaXMuSEFTX0RPQ1VNRU5UX0NPT0tJRSkge1xuICAgICAgICAgICAgZG9jdW1lbnQuY29va2llID0gY29va2llRXhwb3J0cy5zZXJpYWxpemUobmFtZSwgc3RyaW5nVmFsdWUsIG9wdGlvbnMpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuX2VtaXRDaGFuZ2UoeyBuYW1lLCB2YWx1ZSwgb3B0aW9ucyB9KTtcbiAgICB9XG4gICAgcmVtb3ZlKG5hbWUsIG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3QgZmluYWxPcHRpb25zID0gKG9wdGlvbnMgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5kZWZhdWx0U2V0T3B0aW9ucyksIG9wdGlvbnMpLCB7IGV4cGlyZXM6IG5ldyBEYXRlKDE5NzAsIDEsIDEsIDAsIDAsIDEpLCBtYXhBZ2U6IDAgfSkpO1xuICAgICAgICB0aGlzLmNvb2tpZXMgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLmNvb2tpZXMpO1xuICAgICAgICBkZWxldGUgdGhpcy5jb29raWVzW25hbWVdO1xuICAgICAgICBpZiAodGhpcy5IQVNfRE9DVU1FTlRfQ09PS0lFKSB7XG4gICAgICAgICAgICBkb2N1bWVudC5jb29raWUgPSBjb29raWVFeHBvcnRzLnNlcmlhbGl6ZShuYW1lLCAnJywgZmluYWxPcHRpb25zKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9lbWl0Q2hhbmdlKHsgbmFtZSwgdmFsdWU6IHVuZGVmaW5lZCwgb3B0aW9ucyB9KTtcbiAgICB9XG4gICAgYWRkQ2hhbmdlTGlzdGVuZXIoY2FsbGJhY2spIHtcbiAgICAgICAgdGhpcy5jaGFuZ2VMaXN0ZW5lcnMucHVzaChjYWxsYmFjayk7XG4gICAgICAgIGlmICh0aGlzLkhBU19ET0NVTUVOVF9DT09LSUUgJiYgdGhpcy5jaGFuZ2VMaXN0ZW5lcnMubGVuZ3RoID09PSAxKSB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ29iamVjdCcgJiYgJ2Nvb2tpZVN0b3JlJyBpbiB3aW5kb3cpIHtcbiAgICAgICAgICAgICAgICB3aW5kb3cuY29va2llU3RvcmUuYWRkRXZlbnRMaXN0ZW5lcignY2hhbmdlJywgdGhpcy51cGRhdGUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fc3RhcnRQb2xsaW5nKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmVtb3ZlQ2hhbmdlTGlzdGVuZXIoY2FsbGJhY2spIHtcbiAgICAgICAgY29uc3QgaWR4ID0gdGhpcy5jaGFuZ2VMaXN0ZW5lcnMuaW5kZXhPZihjYWxsYmFjayk7XG4gICAgICAgIGlmIChpZHggPj0gMCkge1xuICAgICAgICAgICAgdGhpcy5jaGFuZ2VMaXN0ZW5lcnMuc3BsaWNlKGlkeCwgMSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuSEFTX0RPQ1VNRU5UX0NPT0tJRSAmJiB0aGlzLmNoYW5nZUxpc3RlbmVycy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAnb2JqZWN0JyAmJiAnY29va2llU3RvcmUnIGluIHdpbmRvdykge1xuICAgICAgICAgICAgICAgIHdpbmRvdy5jb29raWVTdG9yZS5yZW1vdmVFdmVudExpc3RlbmVyKCdjaGFuZ2UnLCB0aGlzLnVwZGF0ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9zdG9wUG9sbGluZygpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxufVxuXG5leHBvcnQgeyBDb29raWVzIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/universal-cookie/esm/index.mjs\n");

/***/ })

};
;