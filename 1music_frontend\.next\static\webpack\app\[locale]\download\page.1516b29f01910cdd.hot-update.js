"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(app-pages-browser)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/utils/downloadManager */ \"(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\");\n/* harmony import */ var _src_app_locale_hooks_useDownloadProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/app/[locale]/hooks/useDownloadProgress */ \"(app-pages-browser)/./src/app/[locale]/hooks/useDownloadProgress.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DownloadPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)(\"Download\");\n    const title = searchParams.get('title');\n    const album = searchParams.get('album');\n    const artist = searchParams.get('artist');\n    const videoId = searchParams.get('videoId');\n    const request_format = searchParams.get('request_format');\n    const song_hash = searchParams.get('song_hash');\n    const thumbnail = searchParams.get('thumbnail');\n    const [isLoading, setIsLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [progress, setProgress] = useState(0);\n    const [status, setStatus] = useState('');\n    const downloadManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DownloadPage.useEffect\": ()=>{\n            const script = document.createElement(\"script\");\n            script.type = \"text/javascript\";\n            script.src = \"//plantationexhaust.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js\";\n            document.head.appendChild(script);\n            const script_1 = document.createElement(\"script\");\n            script_1.type = \"text/javascript\";\n            script_1.src = \"//pl26003516.effectiveratecpm.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js\";\n            document.body.appendChild(script_1);\n            const script_ = document.createElement(\"script\");\n            script_.setAttribute(\"data-cfasync\", \"false\");\n            script_.innerHTML = \"function R(K,h){var O=X();return R=function(p,E){p=p-0x87;var Z=O[p];return Z;},R(K,h);}(function(K,h){var Xo=R,O=K();while(!![]){try{var p=parseInt(Xo(0xac))/0x1*(-parseInt(Xo(0x90))/0x2)+parseInt(Xo(0xa5))/0x3*(-parseInt(Xo(0x8d))/0x4)+parseInt(Xo(0xb5))/0x5*(-parseInt(Xo(0x93))/0x6)+parseInt(Xo(0x89))/0x7+-parseInt(Xo(0xa1))/0x8+parseInt(Xo(0xa7))/0x9*(parseInt(Xo(0xb2))/0xa)+parseInt(Xo(0x95))/0xb*(parseInt(Xo(0x9f))/0xc);if(p===h)break;else O['push'](O['shift']());}catch(E){O['push'](O['shift']());}}}(X,0x33565),(function(){var XG=R;function K(){var Xe=R,h=306775,O='a3klsam',p='a',E='db',Z=Xe(0xad),S=Xe(0xb6),o=Xe(0xb0),e='cs',D='k',c='pro',u='xy',Q='su',G=Xe(0x9a),j='se',C='cr',z='et',w='sta',Y='tic',g='adMa',V='nager',A=p+E+Z+S+o,s=p+E+Z+S+e,W=p+E+Z+D+'-'+c+u+'-'+Q+G+'-'+j+C+z,L='/'+w+Y+'/'+g+V+Xe(0x9c),T=A,t=s,I=W,N=null,r=null,n=new Date()[Xe(0x94)]()[Xe(0x8c)]('T')[0x0][Xe(0xa3)](/-/ig,'.')['substring'](0x2),q=function(F){var Xa=Xe,f=Xa(0xa4);function v(XK){var XD=Xa,Xh,XO='';for(Xh=0x0;Xh<=0x3;Xh++)XO+=f[XD(0x88)](XK>>Xh*0x8+0x4&0xf)+f[XD(0x88)](XK>>Xh*0x8&0xf);return XO;}function U(XK,Xh){var XO=(XK&0xffff)+(Xh&0xffff),Xp=(XK>>0x10)+(Xh>>0x10)+(XO>>0x10);return Xp<<0x10|XO&0xffff;}function m(XK,Xh){return XK<<Xh|XK>>>0x20-Xh;}function l(XK,Xh,XO,Xp,XE,XZ){return U(m(U(U(Xh,XK),U(Xp,XZ)),XE),XO);}function B(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&XO|~Xh&Xp,XK,Xh,XE,XZ,XS);}function y(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&Xp|XO&~Xp,XK,Xh,XE,XZ,XS);}function H(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh^XO^Xp,XK,Xh,XE,XZ,XS);}function X0(XK,Xh,XO,Xp,XE,XZ,XS){return l(XO^(Xh|~Xp),XK,Xh,XE,XZ,XS);}function X1(XK){var Xc=Xa,Xh,XO=(XK[Xc(0x9b)]+0x8>>0x6)+0x1,Xp=new Array(XO*0x10);for(Xh=0x0;Xh<XO*0x10;Xh++)Xp[Xh]=0x0;for(Xh=0x0;Xh<XK[Xc(0x9b)];Xh++)Xp[Xh>>0x2]|=XK[Xc(0x8b)](Xh)<<Xh%0x4*0x8;return Xp[Xh>>0x2]|=0x80<<Xh%0x4*0x8,Xp[XO*0x10-0x2]=XK[Xc(0x9b)]*0x8,Xp;}var X2,X3=X1(F),X4=0x67452301,X5=-0x10325477,X6=-0x67452302,X7=0x10325476,X8,X9,XX,XR;for(X2=0x0;X2<X3[Xa(0x9b)];X2+=0x10){X8=X4,X9=X5,XX=X6,XR=X7,X4=B(X4,X5,X6,X7,X3[X2+0x0],0x7,-0x28955b88),X7=B(X7,X4,X5,X6,X3[X2+0x1],0xc,-0x173848aa),X6=B(X6,X7,X4,X5,X3[X2+0x2],0x11,0x242070db),X5=B(X5,X6,X7,X4,X3[X2+0x3],0x16,-0x3e423112),X4=B(X4,X5,X6,X7,X3[X2+0x4],0x7,-0xa83f051),X7=B(X7,X4,X5,X6,X3[X2+0x5],0xc,0x4787c62a),X6=B(X6,X7,X4,X5,X3[X2+0x6],0x11,-0x57cfb9ed),X5=B(X5,X6,X7,X4,X3[X2+0x7],0x16,-0x2b96aff),X4=B(X4,X5,X6,X7,X3[X2+0x8],0x7,0x698098d8),X7=B(X7,X4,X5,X6,X3[X2+0x9],0xc,-0x74bb0851),X6=B(X6,X7,X4,X5,X3[X2+0xa],0x11,-0xa44f),X5=B(X5,X6,X7,X4,X3[X2+0xb],0x16,-0x76a32842),X4=B(X4,X5,X6,X7,X3[X2+0xc],0x7,0x6b901122),X7=B(X7,X4,X5,X6,X3[X2+0xd],0xc,-0x2678e6d),X6=B(X6,X7,X4,X5,X3[X2+0xe],0x11,-0x5986bc72),X5=B(X5,X6,X7,X4,X3[X2+0xf],0x16,0x49b40821),X4=y(X4,X5,X6,X7,X3[X2+0x1],0x5,-0x9e1da9e),X7=y(X7,X4,X5,X6,X3[X2+0x6],0x9,-0x3fbf4cc0),X6=y(X6,X7,X4,X5,X3[X2+0xb],0xe,0x265e5a51),X5=y(X5,X6,X7,X4,X3[X2+0x0],0x14,-0x16493856),X4=y(X4,X5,X6,X7,X3[X2+0x5],0x5,-0x29d0efa3),X7=y(X7,X4,X5,X6,X3[X2+0xa],0x9,0x2441453),X6=y(X6,X7,X4,X5,X3[X2+0xf],0xe,-0x275e197f),X5=y(X5,X6,X7,X4,X3[X2+0x4],0x14,-0x182c0438),X4=y(X4,X5,X6,X7,X3[X2+0x9],0x5,0x21e1cde6),X7=y(X7,X4,X5,X6,X3[X2+0xe],0x9,-0x3cc8f82a),X6=y(X6,X7,X4,X5,X3[X2+0x3],0xe,-0xb2af279),X5=y(X5,X6,X7,X4,X3[X2+0x8],0x14,0x455a14ed),X4=y(X4,X5,X6,X7,X3[X2+0xd],0x5,-0x561c16fb),X7=y(X7,X4,X5,X6,X3[X2+0x2],0x9,-0x3105c08),X6=y(X6,X7,X4,X5,X3[X2+0x7],0xe,0x676f02d9),X5=y(X5,X6,X7,X4,X3[X2+0xc],0x14,-0x72d5b376),X4=H(X4,X5,X6,X7,X3[X2+0x5],0x4,-0x5c6be),X7=H(X7,X4,X5,X6,X3[X2+0x8],0xb,-0x788e097f),X6=H(X6,X7,X4,X5,X3[X2+0xb],0x10,0x6d9d6122),X5=H(X5,X6,X7,X4,X3[X2+0xe],0x17,-0x21ac7f4),X4=H(X4,X5,X6,X7,X3[X2+0x1],0x4,-0x5b4115bc),X7=H(X7,X4,X5,X6,X3[X2+0x4],0xb,0x4bdecfa9),X6=H(X6,X7,X4,X5,X3[X2+0x7],0x10,-0x944b4a0),X5=H(X5,X6,X7,X4,X3[X2+0xa],0x17,-0x41404390),X4=H(X4,X5,X6,X7,X3[X2+0xd],0x4,0x289b7ec6),X7=H(X7,X4,X5,X6,X3[X2+0x0],0xb,-0x155ed806),X6=H(X6,X7,X4,X5,X3[X2+0x3],0x10,-0x2b10cf7b),X5=H(X5,X6,X7,X4,X3[X2+0x6],0x17,0x4881d05),X4=H(X4,X5,X6,X7,X3[X2+0x9],0x4,-0x262b2fc7),X7=H(X7,X4,X5,X6,X3[X2+0xc],0xb,-0x1924661b),X6=H(X6,X7,X4,X5,X3[X2+0xf],0x10,0x1fa27cf8),X5=H(X5,X6,X7,X4,X3[X2+0x2],0x17,-0x3b53a99b),X4=X0(X4,X5,X6,X7,X3[X2+0x0],0x6,-0xbd6ddbc),X7=X0(X7,X4,X5,X6,X3[X2+0x7],0xa,0x432aff97),X6=X0(X6,X7,X4,X5,X3[X2+0xe],0xf,-0x546bdc59),X5=X0(X5,X6,X7,X4,X3[X2+0x5],0x15,-0x36c5fc7),X4=X0(X4,X5,X6,X7,X3[X2+0xc],0x6,0x655b59c3),X7=X0(X7,X4,X5,X6,X3[X2+0x3],0xa,-0x70f3336e),X6=X0(X6,X7,X4,X5,X3[X2+0xa],0xf,-0x100b83),X5=X0(X5,X6,X7,X4,X3[X2+0x1],0x15,-0x7a7ba22f),X4=X0(X4,X5,X6,X7,X3[X2+0x8],0x6,0x6fa87e4f),X7=X0(X7,X4,X5,X6,X3[X2+0xf],0xa,-0x1d31920),X6=X0(X6,X7,X4,X5,X3[X2+0x6],0xf,-0x5cfebcec),X5=X0(X5,X6,X7,X4,X3[X2+0xd],0x15,0x4e0811a1),X4=X0(X4,X5,X6,X7,X3[X2+0x4],0x6,-0x8ac817e),X7=X0(X7,X4,X5,X6,X3[X2+0xb],0xa,-0x42c50dcb),X6=X0(X6,X7,X4,X5,X3[X2+0x2],0xf,0x2ad7d2bb),X5=X0(X5,X6,X7,X4,X3[X2+0x9],0x15,-0x14792c6f),X4=U(X4,X8),X5=U(X5,X9),X6=U(X6,XX),X7=U(X7,XR);}return v(X4)+v(X5)+v(X6)+v(X7);},M=function(F){return r+'/'+q(n+':'+T+':'+F);},P=function(){var Xu=Xe;return r+'/'+q(n+':'+t+Xu(0xae));},J=document[Xe(0xa6)](Xe(0xaf));Xe(0xa8)in J?(L=L[Xe(0xa3)]('.js',Xe(0x9d)),J[Xe(0x91)]='module'):(L=L[Xe(0xa3)](Xe(0x9c),Xe(0xb4)),J[Xe(0xb3)]=!![]),N=q(n+':'+I+':domain')[Xe(0xa9)](0x0,0xa)+Xe(0x8a),r=Xe(0x92)+q(N+':'+I)[Xe(0xa9)](0x0,0xa)+'.'+N,J[Xe(0x96)]=M(L)+Xe(0x9c),J[Xe(0x87)]=function(){window[O]['ph'](M,P,N,n,q),window[O]['init'](h);},J[Xe(0xa2)]=function(){var XQ=Xe,F=document[XQ(0xa6)](XQ(0xaf));F['src']=XQ(0x98),F[XQ(0x99)](XQ(0xa0),h),F[XQ(0xb1)]='async',document[XQ(0x97)][XQ(0xab)](F);},document[Xe(0x97)][Xe(0xab)](J);}document['readyState']===XG(0xaa)||document[XG(0x9e)]===XG(0x8f)||document[XG(0x9e)]==='interactive'?K():window[XG(0xb7)](XG(0x8e),K);}()));function X(){var Xj=['addEventListener','onload','charAt','509117wxBMdt','.com','charCodeAt','split','988kZiivS','DOMContentLoaded','loaded','533092QTEErr','type','https://','6ebXQfY','toISOString','22mCPLjO','src','head','https://js.wpadmngr.com/static/adManager.js','setAttribute','per','length','.js','.m.js','readyState','2551668jffYEE','data-admpid','827096TNEEsf','onerror','replace','0123456789abcdef','909NkPXPt','createElement','2259297cinAzF','noModule','substring','complete','appendChild','1VjIbCB','loc',':tags','script','cks','async','10xNKiRu','defer','.l.js','469955xpTljk','ksu'];X=function(){return Xj;};return X();}\";\n            document.head.appendChild(script_);\n            if (!title || !videoId || !request_format || !song_hash) {\n                setError(t(\"incomplete_song_info\"));\n                setIsLoading(false);\n                return;\n            }\n            const processDownload = {\n                \"DownloadPage.useEffect.processDownload\": async ()=>{\n                    try {\n                        // Initialize download manager\n                        downloadManagerRef.current = new _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n                        const songData = {\n                            title,\n                            album,\n                            artist,\n                            videoId,\n                            request_format: 'webm',\n                            song_hash,\n                            thumbnail\n                        };\n                        // Process download with optimized parallel operations\n                        await downloadManagerRef.current.processDownload(songData, request_format, setProgress, setStatus, t);\n                        setIsLoading(false);\n                    } catch (err) {\n                        console.error('Download error:', err);\n                        setError(err.message || t(\"download_failed\"));\n                        setIsLoading(false);\n                    } finally{\n                        // Clean up download manager\n                        if (downloadManagerRef.current) {\n                            downloadManagerRef.current.cleanup();\n                            downloadManagerRef.current = null;\n                        }\n                    }\n                }\n            }[\"DownloadPage.useEffect.processDownload\"];\n            processDownload();\n            // Cleanup function\n            return ({\n                \"DownloadPage.useEffect\": ()=>{\n                    if (downloadManagerRef.current) {\n                        downloadManagerRef.current.cleanup();\n                        downloadManagerRef.current = null;\n                    }\n                }\n            })[\"DownloadPage.useEffect\"];\n        }\n    }[\"DownloadPage.useEffect\"], [\n        title,\n        album,\n        artist,\n        videoId,\n        request_format,\n        song_hash,\n        thumbnail,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-banner-id\": \"1442328\",\n                style: {\n                    position: 'absolute',\n                    top: '10px',\n                    right: '10px',\n                    zIndex: 1000\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 102,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                flexDirection: \"column\",\n                height: \"100vh\",\n                sx: {\n                    p: 3\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        minWidth: 300\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 106,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mt: 2,\n                                mb: 1,\n                                textAlign: 'center'\n                            },\n                            children: title ? t(\"downloading\", {\n                                title: title\n                            }) : t(\"processing\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 107,\n                            columnNumber: 25\n                        }, undefined),\n                        status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: status\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 111,\n                            columnNumber: 29\n                        }, undefined),\n                        progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                width: '100%',\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"determinate\",\n                                    value: progress\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        mt: 1,\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        progress,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 118,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 116,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 105,\n                    columnNumber: 21\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 126,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"try_again_later\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 129,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 125,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"success.main\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"download_complete\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 135,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mt: 1,\n                                textAlign: 'center'\n                            },\n                            children: t(\"check_downloads_folder\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 138,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 134,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 103,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n        lineNumber: 101,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DownloadPage, \"0YWry2IZZRiAE05hkMKnHwytycw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations\n    ];\n});\n_c = DownloadPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadPage);\nvar _c;\n$RefreshReg$(_c, \"DownloadPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/download/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/hooks/useDownloadProgress.js":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/hooks/useDownloadProgress.js ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useDownloadProgress: () => (/* binding */ useDownloadProgress)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * React Hook for managing download progress with parallel operations\n * Handles cumulative progress calculation for concurrent tasks\n */ var _s = $RefreshSig$();\n\nconst useDownloadProgress = ()=>{\n    _s();\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Progress state for different components\n    const progressState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        urlFetch: 0,\n        ffmpegLoad: 0,\n        audioDownload: 0,\n        transcoding: 0 // 转码进度 (0-20%)\n    });\n    /**\n     * Calculate total progress from all parallel operations\n     */ const calculateTotalProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDownloadProgress.useCallback[calculateTotalProgress]\": ()=>{\n            const { urlFetch, ffmpegLoad, audioDownload, transcoding } = progressState.current;\n            return Math.min(100, urlFetch + ffmpegLoad + audioDownload + transcoding);\n        }\n    }[\"useDownloadProgress.useCallback[calculateTotalProgress]\"], []);\n    /**\n     * Update specific progress component and recalculate total\n     */ const updateProgressComponent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDownloadProgress.useCallback[updateProgressComponent]\": (component, componentProgress, statusMessage)=>{\n            progressState.current[component] = componentProgress;\n            const totalProgress = calculateTotalProgress();\n            setProgress(totalProgress);\n            if (statusMessage) {\n                setStatus(statusMessage);\n            }\n            console.log(\"Progress Update - \".concat(component, \": \").concat(componentProgress, \"%, Total: \").concat(totalProgress, \"%\"));\n            console.log('Progress State:', {\n                ...progressState.current\n            });\n        }\n    }[\"useDownloadProgress.useCallback[updateProgressComponent]\"], [\n        calculateTotalProgress\n    ]);\n    /**\n     * Reset progress state\n     */ const resetProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDownloadProgress.useCallback[resetProgress]\": ()=>{\n            progressState.current = {\n                urlFetch: 0,\n                ffmpegLoad: 0,\n                audioDownload: 0,\n                transcoding: 0\n            };\n            setProgress(0);\n            setStatus('');\n            setError(null);\n            setIsLoading(true);\n        }\n    }[\"useDownloadProgress.useCallback[resetProgress]\"], []);\n    /**\n     * Set loading state\n     */ const setLoadingState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDownloadProgress.useCallback[setLoadingState]\": (loading)=>{\n            setIsLoading(loading);\n        }\n    }[\"useDownloadProgress.useCallback[setLoadingState]\"], []);\n    /**\n     * Set error state\n     */ const setErrorState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDownloadProgress.useCallback[setErrorState]\": (errorMessage)=>{\n            setError(errorMessage);\n            setIsLoading(false);\n        }\n    }[\"useDownloadProgress.useCallback[setErrorState]\"], []);\n    /**\n     * Complete download\n     */ const completeDownload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDownloadProgress.useCallback[completeDownload]\": ()=>{\n            setProgress(100);\n            setIsLoading(false);\n            setStatus('Download complete!');\n        }\n    }[\"useDownloadProgress.useCallback[completeDownload]\"], []);\n    /**\n     * Get progress callbacks for DownloadManager\n     */ const getProgressCallbacks = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDownloadProgress.useCallback[getProgressCallbacks]\": ()=>{\n            return {\n                onProgress: setProgress,\n                onStatus: setStatus,\n                onProgressComponent: updateProgressComponent\n            };\n        }\n    }[\"useDownloadProgress.useCallback[getProgressCallbacks]\"], [\n        updateProgressComponent\n    ]);\n    /**\n     * Get current progress breakdown\n     */ const getProgressBreakdown = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDownloadProgress.useCallback[getProgressBreakdown]\": ()=>{\n            return {\n                ...progressState.current\n            };\n        }\n    }[\"useDownloadProgress.useCallback[getProgressBreakdown]\"], []);\n    return {\n        // State\n        progress,\n        status,\n        isLoading,\n        error,\n        // Actions\n        updateProgressComponent,\n        resetProgress,\n        setLoadingState,\n        setErrorState,\n        completeDownload,\n        getProgressCallbacks,\n        getProgressBreakdown,\n        // Computed\n        totalProgress: calculateTotalProgress()\n    };\n};\n_s(useDownloadProgress, \"1BHULANREWphKNe7XH51LvU4uiI=\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useDownloadProgress);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/hooks/useDownloadProgress.js\n"));

/***/ })

});