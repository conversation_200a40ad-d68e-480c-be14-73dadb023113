"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\n\nclass DownloadManager {\n    /**\n     * Update specific progress component\n     * @param {string} component - Progress component name\n     * @param {number} progress - Progress value for this component\n     */ _updateProgressComponent(component, progress) {\n        if (this.progressComponentCallback) {\n            this.progressComponentCallback(component, progress);\n        }\n    }\n    /**\n     * Process download with component-based progress tracking\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgressComponent - Component progress callback\n     * @param {Function} t - Translation function\n     */ async processDownloadWithComponents(songData, requestFormat, onProgressComponent, t) {\n        this.abortController = new AbortController();\n        this.progressComponentCallback = onProgressComponent;\n        try {\n            // Reset progress state\n            this.progressState = {\n                urlFetch: 0,\n                ffmpegLoad: 0,\n                audioDownload: 0,\n                transcoding: 0\n            };\n            this._updateProgressComponent('urlFetch', 0);\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 0-20% of transcoding component)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = progress * 20; // 0-20%\n                this._updateProgressComponent('transcoding', transcodingProgress);\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : this._skipFFmpegLoad();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgressComponent('transcoding', 20);\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            // Start transcoding\n            this._updateProgressComponent('transcoding', 0);\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            // Transcoding complete\n            this._updateProgressComponent('transcoding', 20);\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            this._updateProgressComponent('transcoding', 20);\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress(t) {\n        this._updateProgressComponent('ffmpegLoad', 0);\n        await this.transcoder.load();\n        this._updateProgressComponent('ffmpegLoad', 10);\n        return true;\n    }\n    /**\n     * Skip FFmpeg loading for webm format\n     */ async _skipFFmpegLoad() {\n        this._updateProgressComponent('ffmpegLoad', 10);\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts (0-20% range)\n            const progressStep = Math.min(20, (retries + 1) * (20 / maxRetries));\n            this._updateProgressComponent('urlFetch', progressStep);\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        this._updateProgressComponent('urlFetch', 20);\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with progress tracking using axios\n     */ async _fetchAudioWithProgress(url, t) {\n        try {\n            this._updateProgressComponent('audioDownload', 0, t(\"downloading_files\"));\n            const response = await (0,axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                method: 'GET',\n                url: url,\n                responseType: 'blob',\n                signal: this.abortController.signal,\n                onDownloadProgress: (progressEvent)=>{\n                    if (progressEvent.lengthComputable) {\n                        // Map download progress to 0-50% range for audioDownload component\n                        const downloadPercent = progressEvent.loaded / progressEvent.total * 100;\n                        const mappedProgress = downloadPercent / 100 * 50; // 0-50%\n                        this._updateProgressComponent('audioDownload', mappedProgress, t(\"downloading_files\"));\n                    } else {\n                        // If we can't track progress, show incremental updates\n                        const currentTime = Date.now();\n                        if (!this.downloadStartTime) {\n                            this.downloadStartTime = currentTime;\n                        }\n                        const elapsed = (currentTime - this.downloadStartTime) / 1000;\n                        const estimatedProgress = Math.min(45, elapsed * 2); // Slow increment up to 45%\n                        this._updateProgressComponent('audioDownload', estimatedProgress, t(\"downloading_files\"));\n                    }\n                }\n            });\n            // Download completed\n            this._updateProgressComponent('audioDownload', 50, t(\"download_complete_preparing\"));\n            return response.data;\n        } catch (error) {\n            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n    }\n    /**\n     * Fetch audio file with error handling (fallback method)\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.progressComponentCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});