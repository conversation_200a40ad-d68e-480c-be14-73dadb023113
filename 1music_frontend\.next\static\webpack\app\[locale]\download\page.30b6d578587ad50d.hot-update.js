"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\n\nclass DownloadManager {\n    /**\n     * Update specific progress component\n     * @param {string} component - Progress component name\n     * @param {number} progress - Progress value for this component\n     */ _updateProgressComponent(component, progress) {\n        if (this.progressComponentCallback) {\n            this.progressComponentCallback(component, progress);\n        }\n    }\n    /**\n     * Process download with component-based progress tracking\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgressComponent - Component progress callback\n     * @param {Function} t - Translation function\n     */ async processDownloadWithComponents(songData, requestFormat, onProgressComponent, t) {\n        this.abortController = new AbortController();\n        this.progressComponentCallback = onProgressComponent;\n        try {\n            // Reset progress state\n            this.progressState = {\n                urlFetch: 0,\n                ffmpegLoad: 0,\n                audioDownload: 0,\n                transcoding: 0\n            };\n            this._updateProgressComponent('urlFetch', 0);\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 0-20% of transcoding component)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = progress * 20; // 0-20%\n                this._updateProgressComponent('transcoding', transcodingProgress);\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : this._skipFFmpegLoad();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgressComponent('transcoding', 20);\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            // Start transcoding\n            this._updateProgressComponent('transcoding', 0);\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            // Transcoding complete\n            this._updateProgressComponent('transcoding', 20);\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            this._updateProgressComponent('transcoding', 20);\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress(t) {\n        this._updateProgressComponent('ffmpegLoad', 0, t(\"loading_transcoder\"));\n        await this.transcoder.load();\n        this._updateProgressComponent('ffmpegLoad', 10, t(\"transcoder_ready\"));\n        return true;\n    }\n    /**\n     * Skip FFmpeg loading for webm format\n     */ async _skipFFmpegLoad() {\n        this._updateProgressComponent('ffmpegLoad', 10, \"FFmpeg not needed for webm\");\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts (0-20% range)\n            const progressStep = Math.min(20, (retries + 1) * (20 / maxRetries));\n            this._updateProgressComponent('urlFetch', progressStep, t(\"fetching_audio\"));\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        this._updateProgressComponent('urlFetch', 20, t(\"download_url_ready\"));\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with progress tracking using axios\n     */ async _fetchAudioWithProgress(url, t) {\n        try {\n            this._updateProgressComponent('audioDownload', 0, t(\"downloading_files\"));\n            const response = await (0,axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                method: 'GET',\n                url: url,\n                responseType: 'blob',\n                signal: this.abortController.signal,\n                onDownloadProgress: (progressEvent)=>{\n                    if (progressEvent.lengthComputable) {\n                        // Map download progress to 0-50% range for audioDownload component\n                        const downloadPercent = progressEvent.loaded / progressEvent.total * 100;\n                        const mappedProgress = downloadPercent / 100 * 50; // 0-50%\n                        this._updateProgressComponent('audioDownload', mappedProgress, t(\"downloading_files\"));\n                    } else {\n                        // If we can't track progress, show incremental updates\n                        const currentTime = Date.now();\n                        if (!this.downloadStartTime) {\n                            this.downloadStartTime = currentTime;\n                        }\n                        const elapsed = (currentTime - this.downloadStartTime) / 1000;\n                        const estimatedProgress = Math.min(45, elapsed * 2); // Slow increment up to 45%\n                        this._updateProgressComponent('audioDownload', estimatedProgress, t(\"downloading_files\"));\n                    }\n                }\n            });\n            // Download completed\n            this._updateProgressComponent('audioDownload', 50, t(\"download_complete_preparing\"));\n            return response.data;\n        } catch (error) {\n            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n    }\n    /**\n     * Fetch audio file with error handling (fallback method)\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.progressComponentCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});