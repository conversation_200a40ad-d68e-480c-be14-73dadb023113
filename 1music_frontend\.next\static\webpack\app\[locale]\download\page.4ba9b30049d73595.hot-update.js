"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(app-pages-browser)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/utils/downloadManager */ \"(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DownloadPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"Download\");\n    const title = searchParams.get('title');\n    const album = searchParams.get('album');\n    const artist = searchParams.get('artist');\n    const videoId = searchParams.get('videoId');\n    const request_format = searchParams.get('request_format');\n    const song_hash = searchParams.get('song_hash');\n    const thumbnail = searchParams.get('thumbnail');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const downloadManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DownloadPage.useEffect\": ()=>{\n            const script = document.createElement(\"script\");\n            script.type = \"text/javascript\";\n            script.src = \"//plantationexhaust.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js\";\n            document.head.appendChild(script);\n            const script_1 = document.createElement(\"script\");\n            script_1.type = \"text/javascript\";\n            script_1.src = \"//pl26003516.effectiveratecpm.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js\";\n            document.body.appendChild(script_1);\n            const script_ = document.createElement(\"script\");\n            script_.setAttribute(\"data-cfasync\", \"false\");\n            script_.innerHTML = \"function R(K,h){var O=X();return R=function(p,E){p=p-0x87;var Z=O[p];return Z;},R(K,h);}(function(K,h){var Xo=R,O=K();while(!![]){try{var p=parseInt(Xo(0xac))/0x1*(-parseInt(Xo(0x90))/0x2)+parseInt(Xo(0xa5))/0x3*(-parseInt(Xo(0x8d))/0x4)+parseInt(Xo(0xb5))/0x5*(-parseInt(Xo(0x93))/0x6)+parseInt(Xo(0x89))/0x7+-parseInt(Xo(0xa1))/0x8+parseInt(Xo(0xa7))/0x9*(parseInt(Xo(0xb2))/0xa)+parseInt(Xo(0x95))/0xb*(parseInt(Xo(0x9f))/0xc);if(p===h)break;else O['push'](O['shift']());}catch(E){O['push'](O['shift']());}}}(X,0x33565),(function(){var XG=R;function K(){var Xe=R,h=306775,O='a3klsam',p='a',E='db',Z=Xe(0xad),S=Xe(0xb6),o=Xe(0xb0),e='cs',D='k',c='pro',u='xy',Q='su',G=Xe(0x9a),j='se',C='cr',z='et',w='sta',Y='tic',g='adMa',V='nager',A=p+E+Z+S+o,s=p+E+Z+S+e,W=p+E+Z+D+'-'+c+u+'-'+Q+G+'-'+j+C+z,L='/'+w+Y+'/'+g+V+Xe(0x9c),T=A,t=s,I=W,N=null,r=null,n=new Date()[Xe(0x94)]()[Xe(0x8c)]('T')[0x0][Xe(0xa3)](/-/ig,'.')['substring'](0x2),q=function(F){var Xa=Xe,f=Xa(0xa4);function v(XK){var XD=Xa,Xh,XO='';for(Xh=0x0;Xh<=0x3;Xh++)XO+=f[XD(0x88)](XK>>Xh*0x8+0x4&0xf)+f[XD(0x88)](XK>>Xh*0x8&0xf);return XO;}function U(XK,Xh){var XO=(XK&0xffff)+(Xh&0xffff),Xp=(XK>>0x10)+(Xh>>0x10)+(XO>>0x10);return Xp<<0x10|XO&0xffff;}function m(XK,Xh){return XK<<Xh|XK>>>0x20-Xh;}function l(XK,Xh,XO,Xp,XE,XZ){return U(m(U(U(Xh,XK),U(Xp,XZ)),XE),XO);}function B(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&XO|~Xh&Xp,XK,Xh,XE,XZ,XS);}function y(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&Xp|XO&~Xp,XK,Xh,XE,XZ,XS);}function H(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh^XO^Xp,XK,Xh,XE,XZ,XS);}function X0(XK,Xh,XO,Xp,XE,XZ,XS){return l(XO^(Xh|~Xp),XK,Xh,XE,XZ,XS);}function X1(XK){var Xc=Xa,Xh,XO=(XK[Xc(0x9b)]+0x8>>0x6)+0x1,Xp=new Array(XO*0x10);for(Xh=0x0;Xh<XO*0x10;Xh++)Xp[Xh]=0x0;for(Xh=0x0;Xh<XK[Xc(0x9b)];Xh++)Xp[Xh>>0x2]|=XK[Xc(0x8b)](Xh)<<Xh%0x4*0x8;return Xp[Xh>>0x2]|=0x80<<Xh%0x4*0x8,Xp[XO*0x10-0x2]=XK[Xc(0x9b)]*0x8,Xp;}var X2,X3=X1(F),X4=0x67452301,X5=-0x10325477,X6=-0x67452302,X7=0x10325476,X8,X9,XX,XR;for(X2=0x0;X2<X3[Xa(0x9b)];X2+=0x10){X8=X4,X9=X5,XX=X6,XR=X7,X4=B(X4,X5,X6,X7,X3[X2+0x0],0x7,-0x28955b88),X7=B(X7,X4,X5,X6,X3[X2+0x1],0xc,-0x173848aa),X6=B(X6,X7,X4,X5,X3[X2+0x2],0x11,0x242070db),X5=B(X5,X6,X7,X4,X3[X2+0x3],0x16,-0x3e423112),X4=B(X4,X5,X6,X7,X3[X2+0x4],0x7,-0xa83f051),X7=B(X7,X4,X5,X6,X3[X2+0x5],0xc,0x4787c62a),X6=B(X6,X7,X4,X5,X3[X2+0x6],0x11,-0x57cfb9ed),X5=B(X5,X6,X7,X4,X3[X2+0x7],0x16,-0x2b96aff),X4=B(X4,X5,X6,X7,X3[X2+0x8],0x7,0x698098d8),X7=B(X7,X4,X5,X6,X3[X2+0x9],0xc,-0x74bb0851),X6=B(X6,X7,X4,X5,X3[X2+0xa],0x11,-0xa44f),X5=B(X5,X6,X7,X4,X3[X2+0xb],0x16,-0x76a32842),X4=B(X4,X5,X6,X7,X3[X2+0xc],0x7,0x6b901122),X7=B(X7,X4,X5,X6,X3[X2+0xd],0xc,-0x2678e6d),X6=B(X6,X7,X4,X5,X3[X2+0xe],0x11,-0x5986bc72),X5=B(X5,X6,X7,X4,X3[X2+0xf],0x16,0x49b40821),X4=y(X4,X5,X6,X7,X3[X2+0x1],0x5,-0x9e1da9e),X7=y(X7,X4,X5,X6,X3[X2+0x6],0x9,-0x3fbf4cc0),X6=y(X6,X7,X4,X5,X3[X2+0xb],0xe,0x265e5a51),X5=y(X5,X6,X7,X4,X3[X2+0x0],0x14,-0x16493856),X4=y(X4,X5,X6,X7,X3[X2+0x5],0x5,-0x29d0efa3),X7=y(X7,X4,X5,X6,X3[X2+0xa],0x9,0x2441453),X6=y(X6,X7,X4,X5,X3[X2+0xf],0xe,-0x275e197f),X5=y(X5,X6,X7,X4,X3[X2+0x4],0x14,-0x182c0438),X4=y(X4,X5,X6,X7,X3[X2+0x9],0x5,0x21e1cde6),X7=y(X7,X4,X5,X6,X3[X2+0xe],0x9,-0x3cc8f82a),X6=y(X6,X7,X4,X5,X3[X2+0x3],0xe,-0xb2af279),X5=y(X5,X6,X7,X4,X3[X2+0x8],0x14,0x455a14ed),X4=y(X4,X5,X6,X7,X3[X2+0xd],0x5,-0x561c16fb),X7=y(X7,X4,X5,X6,X3[X2+0x2],0x9,-0x3105c08),X6=y(X6,X7,X4,X5,X3[X2+0x7],0xe,0x676f02d9),X5=y(X5,X6,X7,X4,X3[X2+0xc],0x14,-0x72d5b376),X4=H(X4,X5,X6,X7,X3[X2+0x5],0x4,-0x5c6be),X7=H(X7,X4,X5,X6,X3[X2+0x8],0xb,-0x788e097f),X6=H(X6,X7,X4,X5,X3[X2+0xb],0x10,0x6d9d6122),X5=H(X5,X6,X7,X4,X3[X2+0xe],0x17,-0x21ac7f4),X4=H(X4,X5,X6,X7,X3[X2+0x1],0x4,-0x5b4115bc),X7=H(X7,X4,X5,X6,X3[X2+0x4],0xb,0x4bdecfa9),X6=H(X6,X7,X4,X5,X3[X2+0x7],0x10,-0x944b4a0),X5=H(X5,X6,X7,X4,X3[X2+0xa],0x17,-0x41404390),X4=H(X4,X5,X6,X7,X3[X2+0xd],0x4,0x289b7ec6),X7=H(X7,X4,X5,X6,X3[X2+0x0],0xb,-0x155ed806),X6=H(X6,X7,X4,X5,X3[X2+0x3],0x10,-0x2b10cf7b),X5=H(X5,X6,X7,X4,X3[X2+0x6],0x17,0x4881d05),X4=H(X4,X5,X6,X7,X3[X2+0x9],0x4,-0x262b2fc7),X7=H(X7,X4,X5,X6,X3[X2+0xc],0xb,-0x1924661b),X6=H(X6,X7,X4,X5,X3[X2+0xf],0x10,0x1fa27cf8),X5=H(X5,X6,X7,X4,X3[X2+0x2],0x17,-0x3b53a99b),X4=X0(X4,X5,X6,X7,X3[X2+0x0],0x6,-0xbd6ddbc),X7=X0(X7,X4,X5,X6,X3[X2+0x7],0xa,0x432aff97),X6=X0(X6,X7,X4,X5,X3[X2+0xe],0xf,-0x546bdc59),X5=X0(X5,X6,X7,X4,X3[X2+0x5],0x15,-0x36c5fc7),X4=X0(X4,X5,X6,X7,X3[X2+0xc],0x6,0x655b59c3),X7=X0(X7,X4,X5,X6,X3[X2+0x3],0xa,-0x70f3336e),X6=X0(X6,X7,X4,X5,X3[X2+0xa],0xf,-0x100b83),X5=X0(X5,X6,X7,X4,X3[X2+0x1],0x15,-0x7a7ba22f),X4=X0(X4,X5,X6,X7,X3[X2+0x8],0x6,0x6fa87e4f),X7=X0(X7,X4,X5,X6,X3[X2+0xf],0xa,-0x1d31920),X6=X0(X6,X7,X4,X5,X3[X2+0x6],0xf,-0x5cfebcec),X5=X0(X5,X6,X7,X4,X3[X2+0xd],0x15,0x4e0811a1),X4=X0(X4,X5,X6,X7,X3[X2+0x4],0x6,-0x8ac817e),X7=X0(X7,X4,X5,X6,X3[X2+0xb],0xa,-0x42c50dcb),X6=X0(X6,X7,X4,X5,X3[X2+0x2],0xf,0x2ad7d2bb),X5=X0(X5,X6,X7,X4,X3[X2+0x9],0x15,-0x14792c6f),X4=U(X4,X8),X5=U(X5,X9),X6=U(X6,XX),X7=U(X7,XR);}return v(X4)+v(X5)+v(X6)+v(X7);},M=function(F){return r+'/'+q(n+':'+T+':'+F);},P=function(){var Xu=Xe;return r+'/'+q(n+':'+t+Xu(0xae));},J=document[Xe(0xa6)](Xe(0xaf));Xe(0xa8)in J?(L=L[Xe(0xa3)]('.js',Xe(0x9d)),J[Xe(0x91)]='module'):(L=L[Xe(0xa3)](Xe(0x9c),Xe(0xb4)),J[Xe(0xb3)]=!![]),N=q(n+':'+I+':domain')[Xe(0xa9)](0x0,0xa)+Xe(0x8a),r=Xe(0x92)+q(N+':'+I)[Xe(0xa9)](0x0,0xa)+'.'+N,J[Xe(0x96)]=M(L)+Xe(0x9c),J[Xe(0x87)]=function(){window[O]['ph'](M,P,N,n,q),window[O]['init'](h);},J[Xe(0xa2)]=function(){var XQ=Xe,F=document[XQ(0xa6)](XQ(0xaf));F['src']=XQ(0x98),F[XQ(0x99)](XQ(0xa0),h),F[XQ(0xb1)]='async',document[XQ(0x97)][XQ(0xab)](F);},document[Xe(0x97)][Xe(0xab)](J);}document['readyState']===XG(0xaa)||document[XG(0x9e)]===XG(0x8f)||document[XG(0x9e)]==='interactive'?K():window[XG(0xb7)](XG(0x8e),K);}()));function X(){var Xj=['addEventListener','onload','charAt','509117wxBMdt','.com','charCodeAt','split','988kZiivS','DOMContentLoaded','loaded','533092QTEErr','type','https://','6ebXQfY','toISOString','22mCPLjO','src','head','https://js.wpadmngr.com/static/adManager.js','setAttribute','per','length','.js','.m.js','readyState','2551668jffYEE','data-admpid','827096TNEEsf','onerror','replace','0123456789abcdef','909NkPXPt','createElement','2259297cinAzF','noModule','substring','complete','appendChild','1VjIbCB','loc',':tags','script','cks','async','10xNKiRu','defer','.l.js','469955xpTljk','ksu'];X=function(){return Xj;};return X();}\";\n            document.head.appendChild(script_);\n            if (!title || !videoId || !request_format || !song_hash) {\n                setError(t(\"incomplete_song_info\"));\n                setIsLoading(false);\n                return;\n            }\n            const processDownload = {\n                \"DownloadPage.useEffect.processDownload\": async ()=>{\n                    try {\n                        // Initialize download manager\n                        downloadManagerRef.current = new _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n                        const songData = {\n                            title,\n                            album,\n                            artist,\n                            videoId,\n                            request_format: 'webm',\n                            song_hash,\n                            thumbnail\n                        };\n                        // Process download with optimized parallel operations\n                        await downloadManagerRef.current.processDownload(songData, request_format, setProgress, setStatus, t);\n                        setIsLoading(false);\n                    } catch (err) {\n                        console.error('Download error:', err);\n                        setError(err.message || t(\"download_failed\"));\n                        setIsLoading(false);\n                    } finally{\n                        // Clean up download manager\n                        if (downloadManagerRef.current) {\n                            downloadManagerRef.current.cleanup();\n                            downloadManagerRef.current = null;\n                        }\n                    }\n                }\n            }[\"DownloadPage.useEffect.processDownload\"];\n            processDownload();\n            // Cleanup function\n            return ({\n                \"DownloadPage.useEffect\": ()=>{\n                    if (downloadManagerRef.current) {\n                        downloadManagerRef.current.cleanup();\n                        downloadManagerRef.current = null;\n                    }\n                }\n            })[\"DownloadPage.useEffect\"];\n        }\n    }[\"DownloadPage.useEffect\"], [\n        title,\n        album,\n        artist,\n        videoId,\n        request_format,\n        song_hash,\n        thumbnail,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-banner-id\": \"1442328\",\n                style: {\n                    position: 'absolute',\n                    top: '10px',\n                    right: '10px',\n                    zIndex: 1000\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 101,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                flexDirection: \"column\",\n                height: \"100vh\",\n                sx: {\n                    p: 3\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        minWidth: 300\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 105,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mt: 2,\n                                mb: 1,\n                                textAlign: 'center'\n                            },\n                            children: title ? t(\"downloading\", {\n                                title: title\n                            }) : t(\"processing\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 106,\n                            columnNumber: 25\n                        }, undefined),\n                        status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: status\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 110,\n                            columnNumber: 29\n                        }, undefined),\n                        progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                width: '100%',\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"determinate\",\n                                    value: progress\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 116,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        mt: 1,\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        progress,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 115,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 104,\n                    columnNumber: 21\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 125,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"try_again_later\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 128,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 124,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"success.main\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"download_complete\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 134,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mt: 1,\n                                textAlign: 'center'\n                            },\n                            children: t(\"check_downloads_folder\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 137,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 133,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 102,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n        lineNumber: 100,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DownloadPage, \"0YWry2IZZRiAE05hkMKnHwytycw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c = DownloadPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadPage);\nvar _c;\n$RefreshReg$(_c, \"DownloadPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/download/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\nclass DownloadManager {\n    /**\n     * Process download with optimized parallel operations\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgress - Progress callback\n     * @param {Function} onStatus - Status update callback\n     * @param {Function} t - Translation function\n     */ async processDownload(songData, requestFormat, onProgress, onStatus, t) {\n        this.abortController = new AbortController();\n        try {\n            onStatus(t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                onProgress(Math.round(progress * 100));\n            });\n            // Create parallel promises\n            const promises = [];\n            // 1. Load FFmpeg (only if transcoding is needed)\n            if (requestFormat !== 'webm') {\n                promises.push(this.transcoder.load());\n            } else {\n                promises.push(Promise.resolve());\n            }\n            // 2. Get download URL\n            promises.push(this._getDownloadUrl(songData, t));\n            // 3. Pre-fetch thumbnail if available (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            onStatus(t(\"fetching_audio\"));\n            // Wait for critical operations (FFmpeg + download URL)\n            const [, originalAudioUrl] = await Promise.all(promises);\n            onStatus(t(\"downloading_files\"));\n            // Download audio and thumbnail in parallel\n            const [audioBlob, imageBlob] = await Promise.all([\n                this._fetchAudio(originalAudioUrl, t),\n                thumbnailPromise\n            ]);\n            // Handle direct download for webm format\n            if (requestFormat === 'webm') {\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                onStatus(t(\"download_complete\"));\n                return;\n            }\n            onStatus(t(\"transcoding_audio\"));\n            // Transcode audio\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            onStatus(t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            onStatus(t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get download URL with retries and timeout\n     */ async _getDownloadUrl(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with error handling\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});