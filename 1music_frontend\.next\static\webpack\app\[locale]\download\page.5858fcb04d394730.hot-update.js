"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/audioTranscoder.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"(app-pages-browser)/./node_modules/@ffmpeg/ffmpeg/dist/esm/index.js\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ffmpeg/util */ \"(app-pages-browser)/./node_modules/@ffmpeg/util/dist/esm/index.js\");\n/* harmony import */ var _imageProcessor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./imageProcessor */ \"(app-pages-browser)/./src/app/[locale]/utils/imageProcessor.js\");\n\n\n\nclass AudioTranscoder {\n    async load() {\n        // Return existing promise if already loading\n        if (this.loadPromise) {\n            return this.loadPromise;\n        }\n        // Return immediately if already loaded\n        if (this.isLoaded) {\n            return Promise.resolve();\n        }\n        // Create and cache the load promise\n        this.loadPromise = this._loadFFmpeg();\n        try {\n            await this.loadPromise;\n            this.isLoaded = true;\n        } catch (error) {\n            // Reset promise on failure so it can be retried\n            this.loadPromise = null;\n            throw error;\n        }\n    }\n    async _loadFFmpeg() {\n        this.ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__.FFmpeg();\n        // Load FFmpeg with CDN URLs in parallel\n        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';\n        const [coreURL, wasmURL] = await Promise.all([\n            (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.js\"), 'text/javascript'),\n            (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.wasm\"), 'application/wasm')\n        ]);\n        await this.ffmpeg.load({\n            coreURL,\n            wasmURL\n        });\n    }\n    async cropImageToSquare(imageFile) {\n        // Use Canvas API for image processing instead of ffmpeg\n        return await _imageProcessor__WEBPACK_IMPORTED_MODULE_2__.ImageProcessor.cropToSquareJPEG(imageFile, 500, 0.9);\n    }\n    async transcodeAudio(audioFile, coverImageFile, format) {\n        let metadata = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n        if (!this.isLoaded) await this.load();\n        const inputAudioName = 'input_audio';\n        const inputImageName = 'input_image.jpg';\n        const outputName = \"output.\".concat(format);\n        try {\n            // Write input audio file\n            await this.ffmpeg.writeFile(inputAudioName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(audioFile));\n            // Process cover image if provided\n            let processedImageBlob = null;\n            if (coverImageFile) {\n                processedImageBlob = await this.cropImageToSquare(coverImageFile);\n                await this.ffmpeg.writeFile(inputImageName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(processedImageBlob));\n            }\n            // Build FFmpeg command based on format\n            let command = [\n                '-i',\n                inputAudioName\n            ];\n            if (coverImageFile) {\n                command.push('-i', inputImageName);\n                command.push('-map', '0:a', '-map', '1');\n            }\n            if (format === 'mp3') {\n                command.push('-codec:a', 'libmp3lame', '-b:a', '320k');\n                if (coverImageFile) {\n                    command.push('-c:v', 'mjpeg', '-id3v2_version', '3', '-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-metadata:s:v', 'handler_name=Album cover');\n                }\n            } else if (format === 'flac') {\n                command.push('-codec:a', 'flac');\n                if (coverImageFile) {\n                    command.push('-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-disposition:v', 'attached_pic');\n                }\n            } else {\n                throw new Error(\"Unsupported format: \".concat(format));\n            }\n            // Add metadata\n            if (metadata.title) command.push('-metadata', \"title=\".concat(metadata.title));\n            if (metadata.artist) command.push('-metadata', \"artist=\".concat(metadata.artist));\n            if (metadata.album) command.push('-metadata', \"album=\".concat(metadata.album));\n            // Add custom metadata\n            command.push('-metadata', 'PURL=1music.cc', '-metadata', 'COMMENT=1music.cc');\n            command.push('-y', outputName);\n            // Execute transcoding\n            await this.ffmpeg.exec(command);\n            // Read output file\n            const data = await this.ffmpeg.readFile(outputName);\n            // Clean up\n            await this.ffmpeg.deleteFile(inputAudioName);\n            if (coverImageFile) {\n                await this.ffmpeg.deleteFile(inputImageName);\n            }\n            await this.ffmpeg.deleteFile(outputName);\n            return new Uint8Array(data);\n        } catch (error) {\n            // Clean up on error\n            try {\n                await this.ffmpeg.deleteFile(inputAudioName);\n                if (coverImageFile) {\n                    await this.ffmpeg.deleteFile(inputImageName);\n                }\n                await this.ffmpeg.deleteFile(outputName);\n            } catch (cleanupError) {\n            // Ignore cleanup errors\n            }\n            throw error;\n        }\n    }\n    setProgressCallback(callback) {\n        if (this.ffmpeg) {\n            this.ffmpeg.on('progress', callback);\n        }\n    }\n    terminate() {\n        if (this.ffmpeg) {\n            this.ffmpeg.terminate();\n            this.isLoaded = false;\n        }\n    }\n    constructor(){\n        this.ffmpeg = null;\n        this.isLoaded = false;\n        this.loadPromise = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AudioTranscoder);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\n"));

/***/ })

});