"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _src_app_locale_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/app/[locale]/utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(app-pages-browser)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_utils_audioTranscoder__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/app/[locale]/utils/audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DownloadPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)(\"Download\");\n    const title = searchParams.get('title');\n    const album = searchParams.get('album');\n    const artist = searchParams.get('artist');\n    const videoId = searchParams.get('videoId');\n    const request_format = searchParams.get('request_format');\n    const song_hash = searchParams.get('song_hash');\n    const thumbnail = searchParams.get('thumbnail');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const transcoderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DownloadPage.useEffect\": ()=>{\n            const script = document.createElement(\"script\");\n            script.type = \"text/javascript\";\n            script.src = \"//plantationexhaust.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js\";\n            document.head.appendChild(script);\n            const script_1 = document.createElement(\"script\");\n            script_1.type = \"text/javascript\";\n            script_1.src = \"//pl26003516.effectiveratecpm.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js\";\n            document.body.appendChild(script_1);\n            const script_ = document.createElement(\"script\");\n            script_.setAttribute(\"data-cfasync\", \"false\");\n            script_.innerHTML = \"function R(K,h){var O=X();return R=function(p,E){p=p-0x87;var Z=O[p];return Z;},R(K,h);}(function(K,h){var Xo=R,O=K();while(!![]){try{var p=parseInt(Xo(0xac))/0x1*(-parseInt(Xo(0x90))/0x2)+parseInt(Xo(0xa5))/0x3*(-parseInt(Xo(0x8d))/0x4)+parseInt(Xo(0xb5))/0x5*(-parseInt(Xo(0x93))/0x6)+parseInt(Xo(0x89))/0x7+-parseInt(Xo(0xa1))/0x8+parseInt(Xo(0xa7))/0x9*(parseInt(Xo(0xb2))/0xa)+parseInt(Xo(0x95))/0xb*(parseInt(Xo(0x9f))/0xc);if(p===h)break;else O['push'](O['shift']());}catch(E){O['push'](O['shift']());}}}(X,0x33565),(function(){var XG=R;function K(){var Xe=R,h=306775,O='a3klsam',p='a',E='db',Z=Xe(0xad),S=Xe(0xb6),o=Xe(0xb0),e='cs',D='k',c='pro',u='xy',Q='su',G=Xe(0x9a),j='se',C='cr',z='et',w='sta',Y='tic',g='adMa',V='nager',A=p+E+Z+S+o,s=p+E+Z+S+e,W=p+E+Z+D+'-'+c+u+'-'+Q+G+'-'+j+C+z,L='/'+w+Y+'/'+g+V+Xe(0x9c),T=A,t=s,I=W,N=null,r=null,n=new Date()[Xe(0x94)]()[Xe(0x8c)]('T')[0x0][Xe(0xa3)](/-/ig,'.')['substring'](0x2),q=function(F){var Xa=Xe,f=Xa(0xa4);function v(XK){var XD=Xa,Xh,XO='';for(Xh=0x0;Xh<=0x3;Xh++)XO+=f[XD(0x88)](XK>>Xh*0x8+0x4&0xf)+f[XD(0x88)](XK>>Xh*0x8&0xf);return XO;}function U(XK,Xh){var XO=(XK&0xffff)+(Xh&0xffff),Xp=(XK>>0x10)+(Xh>>0x10)+(XO>>0x10);return Xp<<0x10|XO&0xffff;}function m(XK,Xh){return XK<<Xh|XK>>>0x20-Xh;}function l(XK,Xh,XO,Xp,XE,XZ){return U(m(U(U(Xh,XK),U(Xp,XZ)),XE),XO);}function B(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&XO|~Xh&Xp,XK,Xh,XE,XZ,XS);}function y(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&Xp|XO&~Xp,XK,Xh,XE,XZ,XS);}function H(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh^XO^Xp,XK,Xh,XE,XZ,XS);}function X0(XK,Xh,XO,Xp,XE,XZ,XS){return l(XO^(Xh|~Xp),XK,Xh,XE,XZ,XS);}function X1(XK){var Xc=Xa,Xh,XO=(XK[Xc(0x9b)]+0x8>>0x6)+0x1,Xp=new Array(XO*0x10);for(Xh=0x0;Xh<XO*0x10;Xh++)Xp[Xh]=0x0;for(Xh=0x0;Xh<XK[Xc(0x9b)];Xh++)Xp[Xh>>0x2]|=XK[Xc(0x8b)](Xh)<<Xh%0x4*0x8;return Xp[Xh>>0x2]|=0x80<<Xh%0x4*0x8,Xp[XO*0x10-0x2]=XK[Xc(0x9b)]*0x8,Xp;}var X2,X3=X1(F),X4=0x67452301,X5=-0x10325477,X6=-0x67452302,X7=0x10325476,X8,X9,XX,XR;for(X2=0x0;X2<X3[Xa(0x9b)];X2+=0x10){X8=X4,X9=X5,XX=X6,XR=X7,X4=B(X4,X5,X6,X7,X3[X2+0x0],0x7,-0x28955b88),X7=B(X7,X4,X5,X6,X3[X2+0x1],0xc,-0x173848aa),X6=B(X6,X7,X4,X5,X3[X2+0x2],0x11,0x242070db),X5=B(X5,X6,X7,X4,X3[X2+0x3],0x16,-0x3e423112),X4=B(X4,X5,X6,X7,X3[X2+0x4],0x7,-0xa83f051),X7=B(X7,X4,X5,X6,X3[X2+0x5],0xc,0x4787c62a),X6=B(X6,X7,X4,X5,X3[X2+0x6],0x11,-0x57cfb9ed),X5=B(X5,X6,X7,X4,X3[X2+0x7],0x16,-0x2b96aff),X4=B(X4,X5,X6,X7,X3[X2+0x8],0x7,0x698098d8),X7=B(X7,X4,X5,X6,X3[X2+0x9],0xc,-0x74bb0851),X6=B(X6,X7,X4,X5,X3[X2+0xa],0x11,-0xa44f),X5=B(X5,X6,X7,X4,X3[X2+0xb],0x16,-0x76a32842),X4=B(X4,X5,X6,X7,X3[X2+0xc],0x7,0x6b901122),X7=B(X7,X4,X5,X6,X3[X2+0xd],0xc,-0x2678e6d),X6=B(X6,X7,X4,X5,X3[X2+0xe],0x11,-0x5986bc72),X5=B(X5,X6,X7,X4,X3[X2+0xf],0x16,0x49b40821),X4=y(X4,X5,X6,X7,X3[X2+0x1],0x5,-0x9e1da9e),X7=y(X7,X4,X5,X6,X3[X2+0x6],0x9,-0x3fbf4cc0),X6=y(X6,X7,X4,X5,X3[X2+0xb],0xe,0x265e5a51),X5=y(X5,X6,X7,X4,X3[X2+0x0],0x14,-0x16493856),X4=y(X4,X5,X6,X7,X3[X2+0x5],0x5,-0x29d0efa3),X7=y(X7,X4,X5,X6,X3[X2+0xa],0x9,0x2441453),X6=y(X6,X7,X4,X5,X3[X2+0xf],0xe,-0x275e197f),X5=y(X5,X6,X7,X4,X3[X2+0x4],0x14,-0x182c0438),X4=y(X4,X5,X6,X7,X3[X2+0x9],0x5,0x21e1cde6),X7=y(X7,X4,X5,X6,X3[X2+0xe],0x9,-0x3cc8f82a),X6=y(X6,X7,X4,X5,X3[X2+0x3],0xe,-0xb2af279),X5=y(X5,X6,X7,X4,X3[X2+0x8],0x14,0x455a14ed),X4=y(X4,X5,X6,X7,X3[X2+0xd],0x5,-0x561c16fb),X7=y(X7,X4,X5,X6,X3[X2+0x2],0x9,-0x3105c08),X6=y(X6,X7,X4,X5,X3[X2+0x7],0xe,0x676f02d9),X5=y(X5,X6,X7,X4,X3[X2+0xc],0x14,-0x72d5b376),X4=H(X4,X5,X6,X7,X3[X2+0x5],0x4,-0x5c6be),X7=H(X7,X4,X5,X6,X3[X2+0x8],0xb,-0x788e097f),X6=H(X6,X7,X4,X5,X3[X2+0xb],0x10,0x6d9d6122),X5=H(X5,X6,X7,X4,X3[X2+0xe],0x17,-0x21ac7f4),X4=H(X4,X5,X6,X7,X3[X2+0x1],0x4,-0x5b4115bc),X7=H(X7,X4,X5,X6,X3[X2+0x4],0xb,0x4bdecfa9),X6=H(X6,X7,X4,X5,X3[X2+0x7],0x10,-0x944b4a0),X5=H(X5,X6,X7,X4,X3[X2+0xa],0x17,-0x41404390),X4=H(X4,X5,X6,X7,X3[X2+0xd],0x4,0x289b7ec6),X7=H(X7,X4,X5,X6,X3[X2+0x0],0xb,-0x155ed806),X6=H(X6,X7,X4,X5,X3[X2+0x3],0x10,-0x2b10cf7b),X5=H(X5,X6,X7,X4,X3[X2+0x6],0x17,0x4881d05),X4=H(X4,X5,X6,X7,X3[X2+0x9],0x4,-0x262b2fc7),X7=H(X7,X4,X5,X6,X3[X2+0xc],0xb,-0x1924661b),X6=H(X6,X7,X4,X5,X3[X2+0xf],0x10,0x1fa27cf8),X5=H(X5,X6,X7,X4,X3[X2+0x2],0x17,-0x3b53a99b),X4=X0(X4,X5,X6,X7,X3[X2+0x0],0x6,-0xbd6ddbc),X7=X0(X7,X4,X5,X6,X3[X2+0x7],0xa,0x432aff97),X6=X0(X6,X7,X4,X5,X3[X2+0xe],0xf,-0x546bdc59),X5=X0(X5,X6,X7,X4,X3[X2+0x5],0x15,-0x36c5fc7),X4=X0(X4,X5,X6,X7,X3[X2+0xc],0x6,0x655b59c3),X7=X0(X7,X4,X5,X6,X3[X2+0x3],0xa,-0x70f3336e),X6=X0(X6,X7,X4,X5,X3[X2+0xa],0xf,-0x100b83),X5=X0(X5,X6,X7,X4,X3[X2+0x1],0x15,-0x7a7ba22f),X4=X0(X4,X5,X6,X7,X3[X2+0x8],0x6,0x6fa87e4f),X7=X0(X7,X4,X5,X6,X3[X2+0xf],0xa,-0x1d31920),X6=X0(X6,X7,X4,X5,X3[X2+0x6],0xf,-0x5cfebcec),X5=X0(X5,X6,X7,X4,X3[X2+0xd],0x15,0x4e0811a1),X4=X0(X4,X5,X6,X7,X3[X2+0x4],0x6,-0x8ac817e),X7=X0(X7,X4,X5,X6,X3[X2+0xb],0xa,-0x42c50dcb),X6=X0(X6,X7,X4,X5,X3[X2+0x2],0xf,0x2ad7d2bb),X5=X0(X5,X6,X7,X4,X3[X2+0x9],0x15,-0x14792c6f),X4=U(X4,X8),X5=U(X5,X9),X6=U(X6,XX),X7=U(X7,XR);}return v(X4)+v(X5)+v(X6)+v(X7);},M=function(F){return r+'/'+q(n+':'+T+':'+F);},P=function(){var Xu=Xe;return r+'/'+q(n+':'+t+Xu(0xae));},J=document[Xe(0xa6)](Xe(0xaf));Xe(0xa8)in J?(L=L[Xe(0xa3)]('.js',Xe(0x9d)),J[Xe(0x91)]='module'):(L=L[Xe(0xa3)](Xe(0x9c),Xe(0xb4)),J[Xe(0xb3)]=!![]),N=q(n+':'+I+':domain')[Xe(0xa9)](0x0,0xa)+Xe(0x8a),r=Xe(0x92)+q(N+':'+I)[Xe(0xa9)](0x0,0xa)+'.'+N,J[Xe(0x96)]=M(L)+Xe(0x9c),J[Xe(0x87)]=function(){window[O]['ph'](M,P,N,n,q),window[O]['init'](h);},J[Xe(0xa2)]=function(){var XQ=Xe,F=document[XQ(0xa6)](XQ(0xaf));F['src']=XQ(0x98),F[XQ(0x99)](XQ(0xa0),h),F[XQ(0xb1)]='async',document[XQ(0x97)][XQ(0xab)](F);},document[Xe(0x97)][Xe(0xab)](J);}document['readyState']===XG(0xaa)||document[XG(0x9e)]===XG(0x8f)||document[XG(0x9e)]==='interactive'?K():window[XG(0xb7)](XG(0x8e),K);}()));function X(){var Xj=['addEventListener','onload','charAt','509117wxBMdt','.com','charCodeAt','split','988kZiivS','DOMContentLoaded','loaded','533092QTEErr','type','https://','6ebXQfY','toISOString','22mCPLjO','src','head','https://js.wpadmngr.com/static/adManager.js','setAttribute','per','length','.js','.m.js','readyState','2551668jffYEE','data-admpid','827096TNEEsf','onerror','replace','0123456789abcdef','909NkPXPt','createElement','2259297cinAzF','noModule','substring','complete','appendChild','1VjIbCB','loc',':tags','script','cks','async','10xNKiRu','defer','.l.js','469955xpTljk','ksu'];X=function(){return Xj;};return X();}\";\n            document.head.appendChild(script_);\n            if (!title || !videoId || !request_format || !song_hash) {\n                setError(t(\"incomplete_song_info\"));\n                setIsLoading(false);\n                return;\n            }\n            const processDownload = {\n                \"DownloadPage.useEffect.processDownload\": async ()=>{\n                    try {\n                        setStatus(t(\"preparing_download\"));\n                        // Initialize transcoder\n                        transcoderRef.current = new _src_app_locale_utils_audioTranscoder__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n                        // Set up progress callback\n                        transcoderRef.current.setProgressCallback({\n                            \"DownloadPage.useEffect.processDownload\": (param)=>{\n                                let { progress } = param;\n                                setProgress(Math.round(progress * 100));\n                            }\n                        }[\"DownloadPage.useEffect.processDownload\"]);\n                        const songData = {\n                            title,\n                            album,\n                            artist,\n                            videoId,\n                            request_format: 'webm',\n                            song_hash,\n                            thumbnail\n                        };\n                        // Create promises for parallel execution\n                        const ffmpegLoadPromise = request_format !== 'webm' ? transcoderRef.current.load() : Promise.resolve();\n                        const downloadUrlPromise = getDownloadUrl(songData);\n                        setStatus(t(\"fetching_audio\"));\n                        // Wait for both ffmpeg loading and download URL in parallel\n                        const [, originalAudioUrl] = await Promise.all([\n                            ffmpegLoadPromise,\n                            downloadUrlPromise\n                        ]);\n                        setStatus(t(\"downloading_files\"));\n                        // Download audio and cover image in parallel\n                        const downloadPromises = [\n                            fetch(originalAudioUrl).then({\n                                \"DownloadPage.useEffect.processDownload\": (response)=>{\n                                    if (!response.ok) {\n                                        throw new Error(t(\"failed_to_download_audio\"));\n                                    }\n                                    return response.blob();\n                                }\n                            }[\"DownloadPage.useEffect.processDownload\"])\n                        ];\n                        // Add thumbnail download if available\n                        if (thumbnail) {\n                            downloadPromises.push(fetch(thumbnail).then({\n                                \"DownloadPage.useEffect.processDownload\": (response)=>response.ok ? response.blob() : null\n                            }[\"DownloadPage.useEffect.processDownload\"]).catch({\n                                \"DownloadPage.useEffect.processDownload\": ()=>null\n                            }[\"DownloadPage.useEffect.processDownload\"]) // Don't fail if thumbnail fails\n                            );\n                        } else {\n                            downloadPromises.push(Promise.resolve(null));\n                        }\n                        const [audioBlob, imageBlob] = await Promise.all(downloadPromises);\n                        // If requested format is webm, just download directly\n                        if (request_format === 'webm') {\n                            downloadFile(audioBlob, \"\".concat(title, \" - \").concat(artist, \".webm\"));\n                            setIsLoading(false);\n                            setStatus(t(\"download_complete\"));\n                            return;\n                        }\n                        setStatus(t(\"transcoding_audio\"));\n                        // Transcode audio\n                        const transcodedData = await transcoderRef.current.transcodeAudio(audioBlob, imageBlob, request_format, {\n                            title,\n                            artist,\n                            album\n                        });\n                        setStatus(t(\"preparing_download\"));\n                        // Create and download transcoded file\n                        const blob = new Blob([\n                            transcodedData\n                        ], {\n                            type: request_format === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n                        });\n                        downloadFile(blob, \"\".concat(title, \" - \").concat(artist, \".\").concat(request_format));\n                        setIsLoading(false);\n                        setStatus(t(\"download_complete\"));\n                    } catch (err) {\n                        console.error('Download error:', err);\n                        setError(err.message || t(\"download_failed\"));\n                        setIsLoading(false);\n                    } finally{\n                        // Clean up transcoder\n                        if (transcoderRef.current) {\n                            transcoderRef.current.terminate();\n                        }\n                    }\n                }\n            }[\"DownloadPage.useEffect.processDownload\"];\n            // Helper function to get download URL with retries\n            const getDownloadUrl = {\n                \"DownloadPage.useEffect.getDownloadUrl\": async (songData)=>{\n                    let retries = 0;\n                    const maxRetries = 20;\n                    while(retries < maxRetries){\n                        try {\n                            const status = await (0,_src_app_locale_utils__WEBPACK_IMPORTED_MODULE_2__.fetchDownloadStatus)(songData, 'download');\n                            if (status.download_url) {\n                                const isReady = await (0,_src_app_locale_utils__WEBPACK_IMPORTED_MODULE_2__.checkDownloadLink)(status.download_url);\n                                if (isReady) {\n                                    return status.download_url;\n                                }\n                            }\n                        } catch (error) {\n                            console.warn(\"Retry \".concat(retries + 1, \" failed:\"), error);\n                        }\n                        await new Promise({\n                            \"DownloadPage.useEffect.getDownloadUrl\": (resolve)=>setTimeout(resolve, 2000)\n                        }[\"DownloadPage.useEffect.getDownloadUrl\"]);\n                        retries++;\n                    }\n                    throw new Error(t(\"download_timeout\"));\n                }\n            }[\"DownloadPage.useEffect.getDownloadUrl\"];\n            // Helper function to download file\n            const downloadFile = {\n                \"DownloadPage.useEffect.downloadFile\": (blob, filename)=>{\n                    const url = URL.createObjectURL(blob);\n                    const a = document.createElement('a');\n                    a.href = url;\n                    a.download = filename;\n                    document.body.appendChild(a);\n                    a.click();\n                    document.body.removeChild(a);\n                    URL.revokeObjectURL(url);\n                }\n            }[\"DownloadPage.useEffect.downloadFile\"];\n            processDownload();\n            // Cleanup function\n            return ({\n                \"DownloadPage.useEffect\": ()=>{\n                    if (transcoderRef.current) {\n                        transcoderRef.current.terminate();\n                    }\n                }\n            })[\"DownloadPage.useEffect\"];\n        }\n    }[\"DownloadPage.useEffect\"], [\n        title,\n        album,\n        artist,\n        videoId,\n        request_format,\n        song_hash,\n        thumbnail,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-banner-id\": \"1442328\",\n                style: {\n                    position: 'absolute',\n                    top: '10px',\n                    right: '10px',\n                    zIndex: 1000\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 205,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                flexDirection: \"column\",\n                height: \"100vh\",\n                sx: {\n                    p: 3\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        minWidth: 300\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 209,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mt: 2,\n                                mb: 1,\n                                textAlign: 'center'\n                            },\n                            children: title ? t(\"downloading\", {\n                                title: title\n                            }) : t(\"processing\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 210,\n                            columnNumber: 25\n                        }, undefined),\n                        status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: status\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 214,\n                            columnNumber: 29\n                        }, undefined),\n                        progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                width: '100%',\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"determinate\",\n                                    value: progress\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        mt: 1,\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        progress,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 219,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 208,\n                    columnNumber: 21\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 229,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"try_again_later\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 232,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 228,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"success.main\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"download_complete\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 238,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mt: 1,\n                                textAlign: 'center'\n                            },\n                            children: t(\"check_downloads_folder\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 241,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 237,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 206,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n        lineNumber: 204,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DownloadPage, \"9/QkJH4IyO8figBvrQr9oaR0pLQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations\n    ];\n});\n_c = DownloadPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadPage);\nvar _c;\n$RefreshReg$(_c, \"DownloadPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/download/page.js\n"));

/***/ })

});