"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\nclass DownloadManager {\n    /**\n     * Update progress with specific percentage\n     * @param {number} progress - Progress percentage (0-100)\n     * @param {string} status - Status message\n     */ _updateProgress(progress, status) {\n        this.currentProgress = progress;\n        if (this.progressCallback) {\n            this.progressCallback(progress);\n        }\n        if (this.statusCallback) {\n            this.statusCallback(status);\n        }\n    }\n    /**\n     * Process download with optimized parallel operations\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgress - Progress callback\n     * @param {Function} onStatus - Status update callback\n     * @param {Function} t - Translation function\n     */ async processDownload(songData, requestFormat, onProgress, onStatus, t) {\n        this.abortController = new AbortController();\n        this.progressCallback = onProgress;\n        this.statusCallback = onStatus;\n        try {\n            this._updateProgress(0, t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 70-90% range)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = 70 + progress * 20; // 70-90%\n                this._updateProgress(Math.round(transcodingProgress), t(\"transcoding_audio\"));\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : Promise.resolve();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            this._updateProgress(5, t(\"fetching_audio\"));\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            this._updateProgress(20, t(\"download_url_ready\"));\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgress(90, t(\"preparing_download\"));\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                this._updateProgress(100, t(\"download_complete\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            this._updateProgress(30, t(\"transcoder_ready\"));\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            this._updateProgress(70, t(\"transcoding_audio\"));\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            this._updateProgress(90, t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            this._updateProgress(100, t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get download URL with retries and timeout\n     */ async _getDownloadUrl(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with error handling\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.currentProgress = 0;\n        this.progressCallback = null;\n        this.statusCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});