"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(app-pages-browser)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/utils/downloadManager */ \"(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\");\n/* harmony import */ var _src_app_locale_hooks_useDownloadProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/app/[locale]/hooks/useDownloadProgress */ \"(app-pages-browser)/./src/app/[locale]/hooks/useDownloadProgress.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DownloadPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)(\"Download\");\n    const title = searchParams.get('title');\n    const album = searchParams.get('album');\n    const artist = searchParams.get('artist');\n    const videoId = searchParams.get('videoId');\n    const request_format = searchParams.get('request_format');\n    const song_hash = searchParams.get('song_hash');\n    const thumbnail = searchParams.get('thumbnail');\n    // Use the custom hook for progress management\n    const { progress, status, isLoading, error, updateProgressComponent, resetProgress, setLoadingState, setErrorState, completeDownload, getProgressBreakdown } = (0,_src_app_locale_hooks_useDownloadProgress__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const downloadManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DownloadPage.useEffect\": ()=>{\n            const script = document.createElement(\"script\");\n            script.type = \"text/javascript\";\n            script.src = \"//plantationexhaust.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js\";\n            document.head.appendChild(script);\n            const script_1 = document.createElement(\"script\");\n            script_1.type = \"text/javascript\";\n            script_1.src = \"//pl26003516.effectiveratecpm.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js\";\n            document.body.appendChild(script_1);\n            const script_ = document.createElement(\"script\");\n            script_.setAttribute(\"data-cfasync\", \"false\");\n            script_.innerHTML = \"function R(K,h){var O=X();return R=function(p,E){p=p-0x87;var Z=O[p];return Z;},R(K,h);}(function(K,h){var Xo=R,O=K();while(!![]){try{var p=parseInt(Xo(0xac))/0x1*(-parseInt(Xo(0x90))/0x2)+parseInt(Xo(0xa5))/0x3*(-parseInt(Xo(0x8d))/0x4)+parseInt(Xo(0xb5))/0x5*(-parseInt(Xo(0x93))/0x6)+parseInt(Xo(0x89))/0x7+-parseInt(Xo(0xa1))/0x8+parseInt(Xo(0xa7))/0x9*(parseInt(Xo(0xb2))/0xa)+parseInt(Xo(0x95))/0xb*(parseInt(Xo(0x9f))/0xc);if(p===h)break;else O['push'](O['shift']());}catch(E){O['push'](O['shift']());}}}(X,0x33565),(function(){var XG=R;function K(){var Xe=R,h=306775,O='a3klsam',p='a',E='db',Z=Xe(0xad),S=Xe(0xb6),o=Xe(0xb0),e='cs',D='k',c='pro',u='xy',Q='su',G=Xe(0x9a),j='se',C='cr',z='et',w='sta',Y='tic',g='adMa',V='nager',A=p+E+Z+S+o,s=p+E+Z+S+e,W=p+E+Z+D+'-'+c+u+'-'+Q+G+'-'+j+C+z,L='/'+w+Y+'/'+g+V+Xe(0x9c),T=A,t=s,I=W,N=null,r=null,n=new Date()[Xe(0x94)]()[Xe(0x8c)]('T')[0x0][Xe(0xa3)](/-/ig,'.')['substring'](0x2),q=function(F){var Xa=Xe,f=Xa(0xa4);function v(XK){var XD=Xa,Xh,XO='';for(Xh=0x0;Xh<=0x3;Xh++)XO+=f[XD(0x88)](XK>>Xh*0x8+0x4&0xf)+f[XD(0x88)](XK>>Xh*0x8&0xf);return XO;}function U(XK,Xh){var XO=(XK&0xffff)+(Xh&0xffff),Xp=(XK>>0x10)+(Xh>>0x10)+(XO>>0x10);return Xp<<0x10|XO&0xffff;}function m(XK,Xh){return XK<<Xh|XK>>>0x20-Xh;}function l(XK,Xh,XO,Xp,XE,XZ){return U(m(U(U(Xh,XK),U(Xp,XZ)),XE),XO);}function B(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&XO|~Xh&Xp,XK,Xh,XE,XZ,XS);}function y(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&Xp|XO&~Xp,XK,Xh,XE,XZ,XS);}function H(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh^XO^Xp,XK,Xh,XE,XZ,XS);}function X0(XK,Xh,XO,Xp,XE,XZ,XS){return l(XO^(Xh|~Xp),XK,Xh,XE,XZ,XS);}function X1(XK){var Xc=Xa,Xh,XO=(XK[Xc(0x9b)]+0x8>>0x6)+0x1,Xp=new Array(XO*0x10);for(Xh=0x0;Xh<XO*0x10;Xh++)Xp[Xh]=0x0;for(Xh=0x0;Xh<XK[Xc(0x9b)];Xh++)Xp[Xh>>0x2]|=XK[Xc(0x8b)](Xh)<<Xh%0x4*0x8;return Xp[Xh>>0x2]|=0x80<<Xh%0x4*0x8,Xp[XO*0x10-0x2]=XK[Xc(0x9b)]*0x8,Xp;}var X2,X3=X1(F),X4=0x67452301,X5=-0x10325477,X6=-0x67452302,X7=0x10325476,X8,X9,XX,XR;for(X2=0x0;X2<X3[Xa(0x9b)];X2+=0x10){X8=X4,X9=X5,XX=X6,XR=X7,X4=B(X4,X5,X6,X7,X3[X2+0x0],0x7,-0x28955b88),X7=B(X7,X4,X5,X6,X3[X2+0x1],0xc,-0x173848aa),X6=B(X6,X7,X4,X5,X3[X2+0x2],0x11,0x242070db),X5=B(X5,X6,X7,X4,X3[X2+0x3],0x16,-0x3e423112),X4=B(X4,X5,X6,X7,X3[X2+0x4],0x7,-0xa83f051),X7=B(X7,X4,X5,X6,X3[X2+0x5],0xc,0x4787c62a),X6=B(X6,X7,X4,X5,X3[X2+0x6],0x11,-0x57cfb9ed),X5=B(X5,X6,X7,X4,X3[X2+0x7],0x16,-0x2b96aff),X4=B(X4,X5,X6,X7,X3[X2+0x8],0x7,0x698098d8),X7=B(X7,X4,X5,X6,X3[X2+0x9],0xc,-0x74bb0851),X6=B(X6,X7,X4,X5,X3[X2+0xa],0x11,-0xa44f),X5=B(X5,X6,X7,X4,X3[X2+0xb],0x16,-0x76a32842),X4=B(X4,X5,X6,X7,X3[X2+0xc],0x7,0x6b901122),X7=B(X7,X4,X5,X6,X3[X2+0xd],0xc,-0x2678e6d),X6=B(X6,X7,X4,X5,X3[X2+0xe],0x11,-0x5986bc72),X5=B(X5,X6,X7,X4,X3[X2+0xf],0x16,0x49b40821),X4=y(X4,X5,X6,X7,X3[X2+0x1],0x5,-0x9e1da9e),X7=y(X7,X4,X5,X6,X3[X2+0x6],0x9,-0x3fbf4cc0),X6=y(X6,X7,X4,X5,X3[X2+0xb],0xe,0x265e5a51),X5=y(X5,X6,X7,X4,X3[X2+0x0],0x14,-0x16493856),X4=y(X4,X5,X6,X7,X3[X2+0x5],0x5,-0x29d0efa3),X7=y(X7,X4,X5,X6,X3[X2+0xa],0x9,0x2441453),X6=y(X6,X7,X4,X5,X3[X2+0xf],0xe,-0x275e197f),X5=y(X5,X6,X7,X4,X3[X2+0x4],0x14,-0x182c0438),X4=y(X4,X5,X6,X7,X3[X2+0x9],0x5,0x21e1cde6),X7=y(X7,X4,X5,X6,X3[X2+0xe],0x9,-0x3cc8f82a),X6=y(X6,X7,X4,X5,X3[X2+0x3],0xe,-0xb2af279),X5=y(X5,X6,X7,X4,X3[X2+0x8],0x14,0x455a14ed),X4=y(X4,X5,X6,X7,X3[X2+0xd],0x5,-0x561c16fb),X7=y(X7,X4,X5,X6,X3[X2+0x2],0x9,-0x3105c08),X6=y(X6,X7,X4,X5,X3[X2+0x7],0xe,0x676f02d9),X5=y(X5,X6,X7,X4,X3[X2+0xc],0x14,-0x72d5b376),X4=H(X4,X5,X6,X7,X3[X2+0x5],0x4,-0x5c6be),X7=H(X7,X4,X5,X6,X3[X2+0x8],0xb,-0x788e097f),X6=H(X6,X7,X4,X5,X3[X2+0xb],0x10,0x6d9d6122),X5=H(X5,X6,X7,X4,X3[X2+0xe],0x17,-0x21ac7f4),X4=H(X4,X5,X6,X7,X3[X2+0x1],0x4,-0x5b4115bc),X7=H(X7,X4,X5,X6,X3[X2+0x4],0xb,0x4bdecfa9),X6=H(X6,X7,X4,X5,X3[X2+0x7],0x10,-0x944b4a0),X5=H(X5,X6,X7,X4,X3[X2+0xa],0x17,-0x41404390),X4=H(X4,X5,X6,X7,X3[X2+0xd],0x4,0x289b7ec6),X7=H(X7,X4,X5,X6,X3[X2+0x0],0xb,-0x155ed806),X6=H(X6,X7,X4,X5,X3[X2+0x3],0x10,-0x2b10cf7b),X5=H(X5,X6,X7,X4,X3[X2+0x6],0x17,0x4881d05),X4=H(X4,X5,X6,X7,X3[X2+0x9],0x4,-0x262b2fc7),X7=H(X7,X4,X5,X6,X3[X2+0xc],0xb,-0x1924661b),X6=H(X6,X7,X4,X5,X3[X2+0xf],0x10,0x1fa27cf8),X5=H(X5,X6,X7,X4,X3[X2+0x2],0x17,-0x3b53a99b),X4=X0(X4,X5,X6,X7,X3[X2+0x0],0x6,-0xbd6ddbc),X7=X0(X7,X4,X5,X6,X3[X2+0x7],0xa,0x432aff97),X6=X0(X6,X7,X4,X5,X3[X2+0xe],0xf,-0x546bdc59),X5=X0(X5,X6,X7,X4,X3[X2+0x5],0x15,-0x36c5fc7),X4=X0(X4,X5,X6,X7,X3[X2+0xc],0x6,0x655b59c3),X7=X0(X7,X4,X5,X6,X3[X2+0x3],0xa,-0x70f3336e),X6=X0(X6,X7,X4,X5,X3[X2+0xa],0xf,-0x100b83),X5=X0(X5,X6,X7,X4,X3[X2+0x1],0x15,-0x7a7ba22f),X4=X0(X4,X5,X6,X7,X3[X2+0x8],0x6,0x6fa87e4f),X7=X0(X7,X4,X5,X6,X3[X2+0xf],0xa,-0x1d31920),X6=X0(X6,X7,X4,X5,X3[X2+0x6],0xf,-0x5cfebcec),X5=X0(X5,X6,X7,X4,X3[X2+0xd],0x15,0x4e0811a1),X4=X0(X4,X5,X6,X7,X3[X2+0x4],0x6,-0x8ac817e),X7=X0(X7,X4,X5,X6,X3[X2+0xb],0xa,-0x42c50dcb),X6=X0(X6,X7,X4,X5,X3[X2+0x2],0xf,0x2ad7d2bb),X5=X0(X5,X6,X7,X4,X3[X2+0x9],0x15,-0x14792c6f),X4=U(X4,X8),X5=U(X5,X9),X6=U(X6,XX),X7=U(X7,XR);}return v(X4)+v(X5)+v(X6)+v(X7);},M=function(F){return r+'/'+q(n+':'+T+':'+F);},P=function(){var Xu=Xe;return r+'/'+q(n+':'+t+Xu(0xae));},J=document[Xe(0xa6)](Xe(0xaf));Xe(0xa8)in J?(L=L[Xe(0xa3)]('.js',Xe(0x9d)),J[Xe(0x91)]='module'):(L=L[Xe(0xa3)](Xe(0x9c),Xe(0xb4)),J[Xe(0xb3)]=!![]),N=q(n+':'+I+':domain')[Xe(0xa9)](0x0,0xa)+Xe(0x8a),r=Xe(0x92)+q(N+':'+I)[Xe(0xa9)](0x0,0xa)+'.'+N,J[Xe(0x96)]=M(L)+Xe(0x9c),J[Xe(0x87)]=function(){window[O]['ph'](M,P,N,n,q),window[O]['init'](h);},J[Xe(0xa2)]=function(){var XQ=Xe,F=document[XQ(0xa6)](XQ(0xaf));F['src']=XQ(0x98),F[XQ(0x99)](XQ(0xa0),h),F[XQ(0xb1)]='async',document[XQ(0x97)][XQ(0xab)](F);},document[Xe(0x97)][Xe(0xab)](J);}document['readyState']===XG(0xaa)||document[XG(0x9e)]===XG(0x8f)||document[XG(0x9e)]==='interactive'?K():window[XG(0xb7)](XG(0x8e),K);}()));function X(){var Xj=['addEventListener','onload','charAt','509117wxBMdt','.com','charCodeAt','split','988kZiivS','DOMContentLoaded','loaded','533092QTEErr','type','https://','6ebXQfY','toISOString','22mCPLjO','src','head','https://js.wpadmngr.com/static/adManager.js','setAttribute','per','length','.js','.m.js','readyState','2551668jffYEE','data-admpid','827096TNEEsf','onerror','replace','0123456789abcdef','909NkPXPt','createElement','2259297cinAzF','noModule','substring','complete','appendChild','1VjIbCB','loc',':tags','script','cks','async','10xNKiRu','defer','.l.js','469955xpTljk','ksu'];X=function(){return Xj;};return X();}\";\n            document.head.appendChild(script_);\n            if (!title || !videoId || !request_format || !song_hash) {\n                setError(t(\"incomplete_song_info\"));\n                setIsLoading(false);\n                return;\n            }\n            const processDownload = {\n                \"DownloadPage.useEffect.processDownload\": async ()=>{\n                    try {\n                        // Initialize download manager\n                        downloadManagerRef.current = new _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n                        const songData = {\n                            title,\n                            album,\n                            artist,\n                            videoId,\n                            request_format: 'webm',\n                            song_hash,\n                            thumbnail\n                        };\n                        // Process download with optimized parallel operations\n                        await downloadManagerRef.current.processDownload(songData, request_format, setProgress, setStatus, t);\n                        setIsLoading(false);\n                    } catch (err) {\n                        console.error('Download error:', err);\n                        setError(err.message || t(\"download_failed\"));\n                        setIsLoading(false);\n                    } finally{\n                        // Clean up download manager\n                        if (downloadManagerRef.current) {\n                            downloadManagerRef.current.cleanup();\n                            downloadManagerRef.current = null;\n                        }\n                    }\n                }\n            }[\"DownloadPage.useEffect.processDownload\"];\n            processDownload();\n            // Cleanup function\n            return ({\n                \"DownloadPage.useEffect\": ()=>{\n                    if (downloadManagerRef.current) {\n                        downloadManagerRef.current.cleanup();\n                        downloadManagerRef.current = null;\n                    }\n                }\n            })[\"DownloadPage.useEffect\"];\n        }\n    }[\"DownloadPage.useEffect\"], [\n        title,\n        album,\n        artist,\n        videoId,\n        request_format,\n        song_hash,\n        thumbnail,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-banner-id\": \"1442328\",\n                style: {\n                    position: 'absolute',\n                    top: '10px',\n                    right: '10px',\n                    zIndex: 1000\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 112,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                flexDirection: \"column\",\n                height: \"100vh\",\n                sx: {\n                    p: 3\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        minWidth: 300\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 116,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mt: 2,\n                                mb: 1,\n                                textAlign: 'center'\n                            },\n                            children: title ? t(\"downloading\", {\n                                title: title\n                            }) : t(\"processing\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 117,\n                            columnNumber: 25\n                        }, undefined),\n                        status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: status\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 121,\n                            columnNumber: 29\n                        }, undefined),\n                        progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                width: '100%',\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"determinate\",\n                                    value: progress\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 127,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        mt: 1,\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        progress,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 128,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 126,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 115,\n                    columnNumber: 21\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 136,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"try_again_later\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 139,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 135,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"success.main\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"download_complete\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 145,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mt: 1,\n                                textAlign: 'center'\n                            },\n                            children: t(\"check_downloads_folder\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 148,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 144,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 113,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n        lineNumber: 111,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DownloadPage, \"8t8Ef7AzyWZgE/x+0j/77VZ4Twg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        _src_app_locale_hooks_useDownloadProgress__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = DownloadPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadPage);\nvar _c;\n$RefreshReg$(_c, \"DownloadPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/download/page.js\n"));

/***/ })

});