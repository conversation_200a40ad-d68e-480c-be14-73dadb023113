"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\n\nclass DownloadManager {\n    /**\n     * Update progress with specific percentage\n     * @param {number} progress - Progress percentage (0-100)\n     * @param {string} status - Status message\n     */ _updateProgress(progress, status) {\n        this.currentProgress = progress;\n        if (this.progressCallback) {\n            this.progressCallback(progress);\n        }\n        if (this.statusCallback) {\n            this.statusCallback(status);\n        }\n    }\n    /**\n     * Process download with optimized parallel operations\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgress - Progress callback\n     * @param {Function} onStatus - Status update callback\n     * @param {Function} t - Translation function\n     */ async processDownload(songData, requestFormat, onProgress, onStatus, t) {\n        this.abortController = new AbortController();\n        this.progressCallback = onProgress;\n        this.statusCallback = onStatus;\n        try {\n            this._updateProgress(0, t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 70-90% range)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = 70 + progress * 20; // 70-90%\n                this._updateProgress(Math.round(transcodingProgress), t(\"transcoding_audio\"));\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : Promise.resolve();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            this._updateProgress(5, t(\"fetching_audio\"));\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            this._updateProgress(20, t(\"download_url_ready\"));\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgress(90, t(\"preparing_download\"));\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                this._updateProgress(100, t(\"download_complete\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            this._updateProgress(30, t(\"transcoder_ready\"));\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            this._updateProgress(70, t(\"transcoding_audio\"));\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            this._updateProgress(90, t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            this._updateProgress(100, t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress(t) {\n        this._updateProgress(10, t(\"loading_transcoder\"));\n        await this.transcoder.load();\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts\n            const progressStep = Math.min(15, 5 + retries * 0.5);\n            this._updateProgress(progressStep, t(\"fetching_audio\"));\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with error handling\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.currentProgress = 0;\n        this.progressCallback = null;\n        this.statusCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});