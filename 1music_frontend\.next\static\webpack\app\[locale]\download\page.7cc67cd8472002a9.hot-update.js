"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\nclass DownloadManager {\n    /**\n     * Update progress with specific percentage\n     * @param {number} progress - Progress percentage (0-100)\n     * @param {string} status - Status message\n     */ _updateProgress(progress, status) {\n        this.currentProgress = progress;\n        if (this.progressCallback) {\n            this.progressCallback(progress);\n        }\n        if (this.statusCallback) {\n            this.statusCallback(status);\n        }\n    }\n    /**\n     * Process download with optimized parallel operations\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgress - Progress callback\n     * @param {Function} onStatus - Status update callback\n     * @param {Function} t - Translation function\n     */ async processDownload(songData, requestFormat, onProgress, onStatus, t) {\n        this.abortController = new AbortController();\n        this.progressCallback = onProgress;\n        this.statusCallback = onStatus;\n        try {\n            this._updateProgress(0, t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 70-90% range)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = 70 + progress * 20; // 70-90%\n                this._updateProgress(Math.round(transcodingProgress), t(\"transcoding_audio\"));\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : Promise.resolve();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            this._updateProgress(5, t(\"fetching_audio\"));\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            this._updateProgress(20, t(\"download_url_ready\"));\n            onStatus(t(\"downloading_files\"));\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudio(originalAudioUrl, t);\n            // Update status based on what's still loading\n            if (requestFormat !== 'webm') {\n                // Check if FFmpeg is still loading\n                const ffmpegStatus = await Promise.race([\n                    ffmpegLoadPromise.then(()=>'loaded'),\n                    new Promise((resolve)=>setTimeout(()=>resolve('loading'), 100))\n                ]);\n                if (ffmpegStatus === 'loading') {\n                    onStatus(t(\"loading_transcoder\"));\n                }\n            }\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                onStatus(t(\"download_complete\"));\n                return;\n            }\n            // For transcoding formats, wait for both audio download and FFmpeg loading\n            const [audioBlob, , imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                ffmpegLoadPromise,\n                thumbnailPromise\n            ]);\n            onStatus(t(\"transcoding_audio\"));\n            // Transcode audio\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            onStatus(t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            onStatus(t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get download URL with retries and timeout\n     */ async _getDownloadUrl(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with error handling\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.currentProgress = 0;\n        this.progressCallback = null;\n        this.statusCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});