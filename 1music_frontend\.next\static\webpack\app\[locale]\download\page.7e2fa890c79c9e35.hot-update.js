"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\n\nclass DownloadManager {\n    /**\n     * Update specific progress component\n     * @param {string} component - Progress component name\n     * @param {number} progress - Progress value for this component\n     */ _updateProgressComponent(component, progress) {\n        if (this.progressComponentCallback) {\n            this.progressComponentCallback(component, progress);\n        }\n    }\n    /**\n     * Process download with component-based progress tracking\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgressComponent - Component progress callback\n     * @param {Function} t - Translation function\n     */ async processDownloadWithComponents(songData, requestFormat, onProgressComponent, t) {\n        this.abortController = new AbortController();\n        this.progressComponentCallback = onProgressComponent;\n        try {\n            // Reset progress state\n            this.progressState = {\n                urlFetch: 0,\n                ffmpegLoad: 0,\n                audioDownload: 0,\n                transcoding: 0\n            };\n            this._updateProgressComponent('urlFetch', 0, t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 0-20% of transcoding component)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = progress * 20; // 0-20%\n                this._updateProgressComponent('transcoding', transcodingProgress, t(\"transcoding_audio\"));\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : this._skipFFmpegLoad();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgressComponent('transcoding', 20, t(\"preparing_download\"));\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            // Start transcoding\n            this._updateProgressComponent('transcoding', 0, t(\"transcoding_audio\"));\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            // Transcoding complete\n            this._updateProgressComponent('transcoding', 20, t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            this._updateProgressComponent('transcoding', 20, t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress(t) {\n        this._updateProgressComponent('ffmpegLoad', 0, t(\"loading_transcoder\"));\n        await this.transcoder.load();\n        this._updateProgressComponent('ffmpegLoad', 10, t(\"transcoder_ready\"));\n        return true;\n    }\n    /**\n     * Skip FFmpeg loading for webm format\n     */ async _skipFFmpegLoad() {\n        this._updateProgressComponent('ffmpegLoad', 10, \"FFmpeg not needed for webm\");\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts (0-20% range)\n            const progressStep = Math.min(20, (retries + 1) * (20 / maxRetries));\n            this._updateProgressComponent('urlFetch', progressStep, t(\"fetching_audio\"));\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        this._updateProgressComponent('urlFetch', 20, t(\"download_url_ready\"));\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with progress tracking using axios\n     */ async _fetchAudioWithProgress(url, t) {\n        try {\n            this._updateProgressComponent('audioDownload', 0, t(\"downloading_files\"));\n            const response = await (0,axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                method: 'GET',\n                url: url,\n                responseType: 'blob',\n                signal: this.abortController.signal,\n                onDownloadProgress: (progressEvent)=>{\n                    if (progressEvent.lengthComputable) {\n                        // Map download progress to 0-50% range for audioDownload component\n                        const downloadPercent = progressEvent.loaded / progressEvent.total * 100;\n                        const mappedProgress = downloadPercent / 100 * 50; // 0-50%\n                        this._updateProgressComponent('audioDownload', mappedProgress, t(\"downloading_files\"));\n                    } else {\n                        // If we can't track progress, show incremental updates\n                        const currentTime = Date.now();\n                        if (!this.downloadStartTime) {\n                            this.downloadStartTime = currentTime;\n                        }\n                        const elapsed = (currentTime - this.downloadStartTime) / 1000;\n                        const estimatedProgress = Math.min(45, elapsed * 2); // Slow increment up to 45%\n                        this._updateProgressComponent('audioDownload', estimatedProgress, t(\"downloading_files\"));\n                    }\n                }\n            });\n            // Download completed\n            this._updateProgressComponent('audioDownload', 50, t(\"download_complete_preparing\"));\n            return response.data;\n        } catch (error) {\n            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n    }\n    /**\n     * Fetch audio file with error handling (fallback method)\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.progressComponentCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});