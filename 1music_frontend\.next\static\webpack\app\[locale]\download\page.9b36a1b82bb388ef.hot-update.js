"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/audioTranscoder.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"(app-pages-browser)/./node_modules/@ffmpeg/ffmpeg/dist/esm/index.js\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ffmpeg/util */ \"(app-pages-browser)/./node_modules/@ffmpeg/util/dist/esm/index.js\");\n/* harmony import */ var _imageProcessor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./imageProcessor */ \"(app-pages-browser)/./src/app/[locale]/utils/imageProcessor.js\");\n\n\n\nclass AudioTranscoder {\n    async load() {\n        // Return existing promise if already loading\n        if (this.loadPromise) {\n            return this.loadPromise;\n        }\n        // Return immediately if already loaded\n        if (this.isLoaded) {\n            return Promise.resolve();\n        }\n        // Create and cache the load promise\n        this.loadPromise = this._loadFFmpeg();\n        try {\n            await this.loadPromise;\n            this.isLoaded = true;\n        } catch (error) {\n            // Reset promise on failure so it can be retried\n            this.loadPromise = null;\n            throw error;\n        }\n    }\n    async _loadFFmpeg() {\n        this.ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__.FFmpeg();\n        // Load FFmpeg with CDN URLs in parallel\n        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';\n        const [coreURL, wasmURL] = await Promise.all([\n            (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.js\"), 'text/javascript'),\n            (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.wasm\"), 'application/wasm')\n        ]);\n        await this.ffmpeg.load({\n            coreURL,\n            wasmURL\n        });\n    }\n    async cropImageToSquare(imageFile) {\n        // Use Canvas API for image processing instead of ffmpeg\n        return await _imageProcessor__WEBPACK_IMPORTED_MODULE_2__.ImageProcessor.cropToSquareJPEG(imageFile, 500, 0.9);\n    }\n    async transcodeAudio(audioFile, coverImageFile, format) {\n        let metadata = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n        // Ensure FFmpeg is loaded\n        await this.load();\n        const inputAudioName = 'input_audio';\n        const inputImageName = 'input_image.jpg';\n        const outputName = \"output.\".concat(format);\n        try {\n            // Prepare file operations in parallel\n            const fileOperations = [\n                this.ffmpeg.writeFile(inputAudioName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(audioFile))\n            ];\n            // Process cover image if provided (in parallel with audio file writing)\n            let processedImageBlob = null;\n            if (coverImageFile) {\n                // Start image processing while audio file is being written\n                const imageProcessPromise = this.cropImageToSquare(coverImageFile);\n                // Wait for both audio file writing and image processing\n                const [, processedImage] = await Promise.all([\n                    fileOperations[0],\n                    imageProcessPromise\n                ]);\n                processedImageBlob = processedImage;\n                // Write processed image to FFmpeg\n                await this.ffmpeg.writeFile(inputImageName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(processedImageBlob));\n            } else {\n                // Just wait for audio file writing\n                await fileOperations[0];\n            }\n            // Build FFmpeg command based on format\n            let command = [\n                '-i',\n                inputAudioName\n            ];\n            if (coverImageFile) {\n                command.push('-i', inputImageName);\n                command.push('-map', '0:a', '-map', '1');\n            }\n            if (format === 'mp3') {\n                command.push('-codec:a', 'libmp3lame', '-b:a', '320k');\n                if (coverImageFile) {\n                    command.push('-c:v', 'mjpeg', '-id3v2_version', '3', '-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-metadata:s:v', 'handler_name=Album cover');\n                }\n            } else if (format === 'flac') {\n                command.push('-codec:a', 'flac');\n                if (coverImageFile) {\n                    command.push('-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-disposition:v', 'attached_pic');\n                }\n            } else {\n                throw new Error(\"Unsupported format: \".concat(format));\n            }\n            // Add metadata\n            if (metadata.title) command.push('-metadata', \"title=\".concat(metadata.title));\n            if (metadata.artist) command.push('-metadata', \"artist=\".concat(metadata.artist));\n            if (metadata.album) command.push('-metadata', \"album=\".concat(metadata.album));\n            // Add custom metadata\n            command.push('-metadata', 'PURL=1music.cc', '-metadata', 'COMMENT=1music.cc');\n            command.push('-y', outputName);\n            // Execute transcoding\n            await this.ffmpeg.exec(command);\n            // Read output file\n            const data = await this.ffmpeg.readFile(outputName);\n            // Clean up\n            await this.ffmpeg.deleteFile(inputAudioName);\n            if (coverImageFile) {\n                await this.ffmpeg.deleteFile(inputImageName);\n            }\n            await this.ffmpeg.deleteFile(outputName);\n            return new Uint8Array(data);\n        } catch (error) {\n            // Clean up on error\n            try {\n                await this.ffmpeg.deleteFile(inputAudioName);\n                if (coverImageFile) {\n                    await this.ffmpeg.deleteFile(inputImageName);\n                }\n                await this.ffmpeg.deleteFile(outputName);\n            } catch (cleanupError) {\n            // Ignore cleanup errors\n            }\n            throw error;\n        }\n    }\n    setProgressCallback(callback) {\n        if (this.ffmpeg) {\n            this.ffmpeg.on('progress', callback);\n        }\n    }\n    terminate() {\n        if (this.ffmpeg) {\n            this.ffmpeg.terminate();\n            this.isLoaded = false;\n        }\n    }\n    constructor(){\n        this.ffmpeg = null;\n        this.isLoaded = false;\n        this.loadPromise = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AudioTranscoder);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\n"));

/***/ })

});