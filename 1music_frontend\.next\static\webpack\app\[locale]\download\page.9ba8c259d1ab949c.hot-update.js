"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\n\nclass DownloadManager {\n    /**\n     * Calculate total progress from all parallel operations\n     */ _calculateTotalProgress() {\n        const { urlFetch, ffmpegLoad, audioDownload, transcoding } = this.progressState;\n        return Math.min(100, urlFetch + ffmpegLoad + audioDownload + transcoding);\n    }\n    /**\n     * Update specific progress component and recalculate total\n     * @param {string} component - Progress component name\n     * @param {number} progress - Progress value for this component\n     * @param {string} status - Status message\n     */ _updateProgressComponent(component, progress, status) {\n        this.progressState[component] = progress;\n        const totalProgress = this._calculateTotalProgress();\n        if (this.progressCallback) {\n            this.progressCallback(totalProgress);\n        }\n        if (this.statusCallback) {\n            this.statusCallback(status);\n        }\n        console.log(\"Progress Update - \".concat(component, \": \").concat(progress, \"%, Total: \").concat(totalProgress, \"%\"));\n    }\n    /**\n     * Process download with optimized parallel operations\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgress - Progress callback\n     * @param {Function} onStatus - Status update callback\n     * @param {Function} t - Translation function\n     */ async processDownload(songData, requestFormat, onProgress, onStatus, t) {\n        this.abortController = new AbortController();\n        this.progressCallback = onProgress;\n        this.statusCallback = onStatus;\n        try {\n            // Reset progress state\n            this.progressState = {\n                urlFetch: 0,\n                ffmpegLoad: 0,\n                audioDownload: 0,\n                transcoding: 0\n            };\n            this._updateProgressComponent('urlFetch', 0, t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 0-20% of transcoding component)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = progress * 20; // 0-20%\n                this._updateProgressComponent('transcoding', transcodingProgress, t(\"transcoding_audio\"));\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : this._skipFFmpegLoad();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgressComponent('transcoding', 20, t(\"preparing_download\"));\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            // Start transcoding\n            this._updateProgressComponent('transcoding', 0, t(\"transcoding_audio\"));\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            // Transcoding complete\n            this._updateProgressComponent('transcoding', 20, t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            this._updateProgressComponent('transcoding', 20, t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress(t) {\n        this._updateProgressComponent('ffmpegLoad', 0, t(\"loading_transcoder\"));\n        await this.transcoder.load();\n        this._updateProgressComponent('ffmpegLoad', 10, t(\"transcoder_ready\"));\n        return true;\n    }\n    /**\n     * Skip FFmpeg loading for webm format\n     */ async _skipFFmpegLoad() {\n        this._updateProgressComponent('ffmpegLoad', 10, \"FFmpeg not needed for webm\");\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts (0-20% range)\n            const progressStep = Math.min(20, (retries + 1) * (20 / maxRetries));\n            this._updateProgressComponent('urlFetch', progressStep, t(\"fetching_audio\"));\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        this._updateProgressComponent('urlFetch', 20, t(\"download_url_ready\"));\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with progress tracking using axios\n     */ async _fetchAudioWithProgress(url, t) {\n        try {\n            this._updateProgressComponent('audioDownload', 0, t(\"downloading_files\"));\n            const response = await (0,axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                method: 'GET',\n                url: url,\n                responseType: 'blob',\n                signal: this.abortController.signal,\n                onDownloadProgress: (progressEvent)=>{\n                    if (progressEvent.lengthComputable) {\n                        // Map download progress to 0-50% range for audioDownload component\n                        const downloadPercent = progressEvent.loaded / progressEvent.total * 100;\n                        const mappedProgress = downloadPercent / 100 * 50; // 0-50%\n                        this._updateProgressComponent('audioDownload', mappedProgress, t(\"downloading_files\"));\n                    } else {\n                        // If we can't track progress, show incremental updates\n                        const currentTime = Date.now();\n                        if (!this.downloadStartTime) {\n                            this.downloadStartTime = currentTime;\n                        }\n                        const elapsed = (currentTime - this.downloadStartTime) / 1000;\n                        const estimatedProgress = Math.min(45, elapsed * 2); // Slow increment up to 45%\n                        this._updateProgressComponent('audioDownload', estimatedProgress, t(\"downloading_files\"));\n                    }\n                }\n            });\n            // Download completed\n            this._updateProgressComponent('audioDownload', 50, t(\"download_complete_preparing\"));\n            return response.data;\n        } catch (error) {\n            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n    }\n    /**\n     * Fetch audio file with error handling (fallback method)\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.progressState = {\n            urlFetch: 0,\n            ffmpegLoad: 0,\n            audioDownload: 0,\n            transcoding: 0 // 转码进度 (0-20%)\n        };\n        this.progressCallback = null;\n        this.statusCallback = null;\n        this.progressComponentCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vdXRpbHMvZG93bmxvYWRNYW5hZ2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7OztDQUdDLEdBRStDO0FBQ2tCO0FBQ3hDO0FBRW5CLE1BQU1JO0lBZVQ7O0tBRUMsR0FDREMsMEJBQTBCO1FBQ3RCLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxVQUFVLEVBQUVDLGFBQWEsRUFBRUMsV0FBVyxFQUFFLEdBQUcsSUFBSSxDQUFDQyxhQUFhO1FBQy9FLE9BQU9DLEtBQUtDLEdBQUcsQ0FBQyxLQUFLTixXQUFXQyxhQUFhQyxnQkFBZ0JDO0lBQ2pFO0lBRUE7Ozs7O0tBS0MsR0FDREkseUJBQXlCQyxTQUFTLEVBQUVDLFFBQVEsRUFBRUMsTUFBTSxFQUFFO1FBQ2xELElBQUksQ0FBQ04sYUFBYSxDQUFDSSxVQUFVLEdBQUdDO1FBQ2hDLE1BQU1FLGdCQUFnQixJQUFJLENBQUNaLHVCQUF1QjtRQUVsRCxJQUFJLElBQUksQ0FBQ2EsZ0JBQWdCLEVBQUU7WUFDdkIsSUFBSSxDQUFDQSxnQkFBZ0IsQ0FBQ0Q7UUFDMUI7UUFDQSxJQUFJLElBQUksQ0FBQ0UsY0FBYyxFQUFFO1lBQ3JCLElBQUksQ0FBQ0EsY0FBYyxDQUFDSDtRQUN4QjtRQUVBSSxRQUFRQyxHQUFHLENBQUMscUJBQW1DTixPQUFkRCxXQUFVLE1BQXlCRyxPQUFyQkYsVUFBUyxjQUEwQixPQUFkRSxlQUFjO0lBQ3RGO0lBRUE7Ozs7Ozs7S0FPQyxHQUNELE1BQU1LLGdCQUFnQkMsUUFBUSxFQUFFQyxhQUFhLEVBQUVDLFVBQVUsRUFBRUMsUUFBUSxFQUFFQyxDQUFDLEVBQUU7UUFDcEUsSUFBSSxDQUFDQyxlQUFlLEdBQUcsSUFBSUM7UUFDM0IsSUFBSSxDQUFDWCxnQkFBZ0IsR0FBR087UUFDeEIsSUFBSSxDQUFDTixjQUFjLEdBQUdPO1FBRXRCLElBQUk7WUFDQSx1QkFBdUI7WUFDdkIsSUFBSSxDQUFDaEIsYUFBYSxHQUFHO2dCQUNqQkosVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsZUFBZTtnQkFDZkMsYUFBYTtZQUNqQjtZQUVBLElBQUksQ0FBQ0ksd0JBQXdCLENBQUMsWUFBWSxHQUFHYyxFQUFFO1lBRS9DLHdCQUF3QjtZQUN4QixJQUFJLENBQUNHLFVBQVUsR0FBRyxJQUFJOUIsd0RBQWVBO1lBRXJDLGdGQUFnRjtZQUNoRixJQUFJLENBQUM4QixVQUFVLENBQUNDLG1CQUFtQixDQUFDO29CQUFDLEVBQUVoQixRQUFRLEVBQUU7Z0JBQzdDLE1BQU1pQixzQkFBc0JqQixXQUFXLElBQUksUUFBUTtnQkFDbkQsSUFBSSxDQUFDRix3QkFBd0IsQ0FBQyxlQUFlbUIscUJBQXFCTCxFQUFFO1lBQ3hFO1lBRUEsNEJBQTRCO1lBQzVCLE1BQU1NLG9CQUFvQlQsa0JBQWtCLFNBQ3hDLElBQUksQ0FBQ1UsdUJBQXVCLENBQUNQLEtBQzdCLElBQUksQ0FBQ1EsZUFBZTtZQUV4QixNQUFNQyxxQkFBcUIsSUFBSSxDQUFDQywyQkFBMkIsQ0FBQ2QsVUFBVUk7WUFFdEUseURBQXlEO1lBQ3pELE1BQU1XLG1CQUFtQmYsU0FBU2dCLFNBQVMsR0FDdkMsSUFBSSxDQUFDQyxlQUFlLENBQUNqQixTQUFTZ0IsU0FBUyxJQUN2Q0UsUUFBUUMsT0FBTyxDQUFDO1lBRXBCLG1FQUFtRTtZQUNuRSxNQUFNQyxtQkFBbUIsTUFBTVA7WUFFL0IscURBQXFEO1lBQ3JELE1BQU1RLHVCQUF1QixJQUFJLENBQUNDLHVCQUF1QixDQUFDRixrQkFBa0JoQjtZQUU1RSw0RUFBNEU7WUFDNUUsSUFBSUgsa0JBQWtCLFFBQVE7Z0JBQzFCLE1BQU1zQixZQUFZLE1BQU1GO2dCQUN4QixJQUFJLENBQUMvQix3QkFBd0IsQ0FBQyxlQUFlLElBQUljLEVBQUU7Z0JBQ25ELElBQUksQ0FBQ29CLGFBQWEsQ0FBQ0QsV0FBVyxHQUF1QnZCLE9BQXBCQSxTQUFTeUIsS0FBSyxFQUFDLE9BQXFCLE9BQWhCekIsU0FBUzBCLE1BQU0sRUFBQztnQkFDckU7WUFDSjtZQUVBLHNDQUFzQztZQUN0QyxNQUFNaEI7WUFFTixzQ0FBc0M7WUFDdEMsTUFBTSxDQUFDYSxXQUFXSSxVQUFVLEdBQUcsTUFBTVQsUUFBUVUsR0FBRyxDQUFDO2dCQUM3Q1A7Z0JBQ0FOO2FBQ0g7WUFFRCxvQkFBb0I7WUFDcEIsSUFBSSxDQUFDekIsd0JBQXdCLENBQUMsZUFBZSxHQUFHYyxFQUFFO1lBRWxELDREQUE0RDtZQUM1RCxNQUFNeUIsaUJBQWlCLE1BQU0sSUFBSSxDQUFDdEIsVUFBVSxDQUFDdUIsY0FBYyxDQUN2RFAsV0FDQUksV0FDQTFCLGVBQ0E7Z0JBQ0l3QixPQUFPekIsU0FBU3lCLEtBQUs7Z0JBQ3JCQyxRQUFRMUIsU0FBUzBCLE1BQU07Z0JBQ3ZCSyxPQUFPL0IsU0FBUytCLEtBQUs7WUFDekI7WUFHSix1QkFBdUI7WUFDdkIsSUFBSSxDQUFDekMsd0JBQXdCLENBQUMsZUFBZSxJQUFJYyxFQUFFO1lBRW5ELHNDQUFzQztZQUN0QyxNQUFNNEIsT0FBTyxJQUFJQyxLQUFLO2dCQUFDSjthQUFlLEVBQUU7Z0JBQ3BDSyxNQUFNakMsa0JBQWtCLFFBQVEsZUFBZTtZQUNuRDtZQUVBLElBQUksQ0FBQ3VCLGFBQWEsQ0FBQ1EsTUFBTSxHQUF1QmhDLE9BQXBCQSxTQUFTeUIsS0FBSyxFQUFDLE9BQXdCeEIsT0FBbkJELFNBQVMwQixNQUFNLEVBQUMsS0FBaUIsT0FBZHpCO1lBQ25FLElBQUksQ0FBQ1gsd0JBQXdCLENBQUMsZUFBZSxJQUFJYyxFQUFFO1FBRXZELEVBQUUsT0FBTytCLE9BQU87WUFDWixJQUFJQSxNQUFNQyxJQUFJLEtBQUssY0FBYztnQkFDN0IsTUFBTSxJQUFJQyxNQUFNakMsRUFBRTtZQUN0QjtZQUNBLE1BQU0rQjtRQUNWO0lBQ0o7SUFFQTs7S0FFQyxHQUNELE1BQU14Qix3QkFBd0JQLENBQUMsRUFBRTtRQUM3QixJQUFJLENBQUNkLHdCQUF3QixDQUFDLGNBQWMsR0FBR2MsRUFBRTtRQUNqRCxNQUFNLElBQUksQ0FBQ0csVUFBVSxDQUFDK0IsSUFBSTtRQUMxQixJQUFJLENBQUNoRCx3QkFBd0IsQ0FBQyxjQUFjLElBQUljLEVBQUU7UUFDbEQsT0FBTztJQUNYO0lBRUE7O0tBRUMsR0FDRCxNQUFNUSxrQkFBa0I7UUFDcEIsSUFBSSxDQUFDdEIsd0JBQXdCLENBQUMsY0FBYyxJQUFJO1FBQ2hELE9BQU87SUFDWDtJQUVBOztLQUVDLEdBQ0QsTUFBTXdCLDRCQUE0QmQsUUFBUSxFQUFFSSxDQUFDLEVBQUU7UUFDM0MsTUFBTW1DLGFBQWE7UUFDbkIsTUFBTUMsYUFBYTtRQUVuQixJQUFLLElBQUlDLFVBQVUsR0FBR0EsVUFBVUYsWUFBWUUsVUFBVztZQUNuRCxJQUFJLElBQUksQ0FBQ3BDLGVBQWUsQ0FBQ3FDLE1BQU0sQ0FBQ0MsT0FBTyxFQUFFO2dCQUNyQyxNQUFNLElBQUlOLE1BQU07WUFDcEI7WUFFQSx3REFBd0Q7WUFDeEQsTUFBTU8sZUFBZXhELEtBQUtDLEdBQUcsQ0FBQyxJQUFJLENBQUNvRCxVQUFVLEtBQU0sTUFBS0YsVUFBUztZQUNqRSxJQUFJLENBQUNqRCx3QkFBd0IsQ0FBQyxZQUFZc0QsY0FBY3hDLEVBQUU7WUFFMUQsSUFBSTtnQkFDQSxNQUFNWCxTQUFTLE1BQU1mLDJEQUFtQkEsQ0FBQ3NCLFVBQVU7Z0JBQ25ELElBQUlQLE9BQU9vRCxZQUFZLEVBQUU7b0JBQ3JCLE1BQU1DLFVBQVUsTUFBTW5FLHlEQUFpQkEsQ0FBQ2MsT0FBT29ELFlBQVk7b0JBQzNELElBQUlDLFNBQVM7d0JBQ1QsSUFBSSxDQUFDeEQsd0JBQXdCLENBQUMsWUFBWSxJQUFJYyxFQUFFO3dCQUNoRCxPQUFPWCxPQUFPb0QsWUFBWTtvQkFDOUI7Z0JBQ0o7WUFDSixFQUFFLE9BQU9WLE9BQU87Z0JBQ1p0QyxRQUFRa0QsSUFBSSxDQUFDLDhCQUEwQyxPQUFaTixVQUFVLEdBQUUsYUFBV047WUFDdEU7WUFFQSxvQkFBb0I7WUFDcEIsTUFBTSxJQUFJLENBQUNhLE1BQU0sQ0FBQ1I7UUFDdEI7UUFFQSxNQUFNLElBQUlILE1BQU1qQyxFQUFFO0lBQ3RCO0lBRUE7O0tBRUMsR0FDRCxNQUFNa0Isd0JBQXdCMkIsR0FBRyxFQUFFN0MsQ0FBQyxFQUFFO1FBQ2xDLElBQUk7WUFDQSxJQUFJLENBQUNkLHdCQUF3QixDQUFDLGlCQUFpQixHQUFHYyxFQUFFO1lBRXBELE1BQU04QyxXQUFXLE1BQU10RSxpREFBS0EsQ0FBQztnQkFDekJ1RSxRQUFRO2dCQUNSRixLQUFLQTtnQkFDTEcsY0FBYztnQkFDZFYsUUFBUSxJQUFJLENBQUNyQyxlQUFlLENBQUNxQyxNQUFNO2dCQUNuQ1csb0JBQW9CLENBQUNDO29CQUNqQixJQUFJQSxjQUFjQyxnQkFBZ0IsRUFBRTt3QkFDaEMsbUVBQW1FO3dCQUNuRSxNQUFNQyxrQkFBa0IsY0FBZUMsTUFBTSxHQUFHSCxjQUFjSSxLQUFLLEdBQUk7d0JBQ3ZFLE1BQU1DLGlCQUFpQixrQkFBbUIsTUFBTyxJQUFJLFFBQVE7d0JBQzdELElBQUksQ0FBQ3JFLHdCQUF3QixDQUFDLGlCQUFpQnFFLGdCQUFnQnZELEVBQUU7b0JBQ3JFLE9BQU87d0JBQ0gsdURBQXVEO3dCQUN2RCxNQUFNd0QsY0FBY0MsS0FBS0MsR0FBRzt3QkFDNUIsSUFBSSxDQUFDLElBQUksQ0FBQ0MsaUJBQWlCLEVBQUU7NEJBQ3pCLElBQUksQ0FBQ0EsaUJBQWlCLEdBQUdIO3dCQUM3Qjt3QkFDQSxNQUFNSSxVQUFVLENBQUNKLGNBQWMsSUFBSSxDQUFDRyxpQkFBaUIsSUFBSTt3QkFDekQsTUFBTUUsb0JBQW9CN0UsS0FBS0MsR0FBRyxDQUFDLElBQUkyRSxVQUFVLElBQUksMkJBQTJCO3dCQUNoRixJQUFJLENBQUMxRSx3QkFBd0IsQ0FBQyxpQkFBaUIyRSxtQkFBbUI3RCxFQUFFO29CQUN4RTtnQkFDSjtZQUNKO1lBRUEscUJBQXFCO1lBQ3JCLElBQUksQ0FBQ2Qsd0JBQXdCLENBQUMsaUJBQWlCLElBQUljLEVBQUU7WUFDckQsT0FBTzhDLFNBQVNnQixJQUFJO1FBRXhCLEVBQUUsT0FBTy9CLE9BQU87WUFDWixJQUFJQSxNQUFNQyxJQUFJLEtBQUssZ0JBQWdCRCxNQUFNZ0MsSUFBSSxLQUFLLGdCQUFnQjtnQkFDOUQsTUFBTSxJQUFJOUIsTUFBTWpDLEVBQUU7WUFDdEI7WUFDQSxNQUFNLElBQUlpQyxNQUFNakMsRUFBRTtRQUN0QjtJQUNKO0lBRUE7O0tBRUMsR0FDRCxNQUFNZ0UsWUFBWW5CLEdBQUcsRUFBRTdDLENBQUMsRUFBRTtRQUN0QixNQUFNOEMsV0FBVyxNQUFNbUIsTUFBTXBCLEtBQUs7WUFDOUJQLFFBQVEsSUFBSSxDQUFDckMsZUFBZSxDQUFDcUMsTUFBTTtRQUN2QztRQUVBLElBQUksQ0FBQ1EsU0FBU29CLEVBQUUsRUFBRTtZQUNkLE1BQU0sSUFBSWpDLE1BQU1qQyxFQUFFO1FBQ3RCO1FBRUEsT0FBTzhDLFNBQVNsQixJQUFJO0lBQ3hCO0lBRUE7O0tBRUMsR0FDRCxNQUFNZixnQkFBZ0JzRCxZQUFZLEVBQUU7UUFDaEMsSUFBSTtZQUNBLE1BQU1yQixXQUFXLE1BQU1tQixNQUFNRSxjQUFjO2dCQUN2QzdCLFFBQVEsSUFBSSxDQUFDckMsZUFBZSxDQUFDcUMsTUFBTTtZQUN2QztZQUVBLE9BQU9RLFNBQVNvQixFQUFFLEdBQUdwQixTQUFTbEIsSUFBSSxLQUFLO1FBQzNDLEVBQUUsT0FBT0csT0FBTztZQUNadEMsUUFBUWtELElBQUksQ0FBQywyQkFBMkJaO1lBQ3hDLE9BQU87UUFDWDtJQUNKO0lBRUE7O0tBRUMsR0FDRFgsY0FBY1EsSUFBSSxFQUFFd0MsUUFBUSxFQUFFO1FBQzFCLE1BQU12QixNQUFNd0IsSUFBSUMsZUFBZSxDQUFDMUM7UUFDaEMsTUFBTTJDLElBQUlDLFNBQVNDLGFBQWEsQ0FBQztRQUNqQ0YsRUFBRUcsSUFBSSxHQUFHN0I7UUFDVDBCLEVBQUVJLFFBQVEsR0FBR1A7UUFDYkcsRUFBRUssS0FBSyxDQUFDQyxPQUFPLEdBQUc7UUFFbEJMLFNBQVNNLElBQUksQ0FBQ0MsV0FBVyxDQUFDUjtRQUMxQkEsRUFBRVMsS0FBSztRQUNQUixTQUFTTSxJQUFJLENBQUNHLFdBQVcsQ0FBQ1Y7UUFFMUIsc0JBQXNCO1FBQ3RCVyxXQUFXLElBQU1iLElBQUljLGVBQWUsQ0FBQ3RDLE1BQU07SUFDL0M7SUFFQTs7S0FFQyxHQUNERCxPQUFPd0MsRUFBRSxFQUFFO1FBQ1AsT0FBTyxJQUFJdEUsUUFBUUMsQ0FBQUE7WUFDZixNQUFNc0UsWUFBWUgsV0FBV25FLFNBQVNxRTtZQUV0QyxxQkFBcUI7WUFDckIsSUFBSSxDQUFDbkYsZUFBZSxDQUFDcUMsTUFBTSxDQUFDZ0QsZ0JBQWdCLENBQUMsU0FBUztnQkFDbERDLGFBQWFGO2dCQUNidEU7WUFDSjtRQUNKO0lBQ0o7SUFFQTs7S0FFQyxHQUNEeUUsU0FBUztRQUNMLElBQUksSUFBSSxDQUFDdkYsZUFBZSxFQUFFO1lBQ3RCLElBQUksQ0FBQ0EsZUFBZSxDQUFDd0YsS0FBSztRQUM5QjtJQUNKO0lBRUE7O0tBRUMsR0FDREMsVUFBVTtRQUNOLElBQUksQ0FBQ0YsTUFBTTtRQUVYLElBQUksSUFBSSxDQUFDckYsVUFBVSxFQUFFO1lBQ2pCLElBQUksQ0FBQ0EsVUFBVSxDQUFDd0YsU0FBUztZQUN6QixJQUFJLENBQUN4RixVQUFVLEdBQUc7UUFDdEI7UUFFQSxJQUFJLENBQUNGLGVBQWUsR0FBRztJQUMzQjtJQXRVQTJGLGFBQWM7UUFDVixJQUFJLENBQUN6RixVQUFVLEdBQUc7UUFDbEIsSUFBSSxDQUFDRixlQUFlLEdBQUc7UUFDdkIsSUFBSSxDQUFDbEIsYUFBYSxHQUFHO1lBQ2pCSixVQUFVO1lBQ1ZDLFlBQVk7WUFDWkMsZUFBZTtZQUNmQyxhQUFhLEVBQU8sZUFBZTtRQUN2QztRQUNBLElBQUksQ0FBQ1MsZ0JBQWdCLEdBQUc7UUFDeEIsSUFBSSxDQUFDQyxjQUFjLEdBQUc7UUFDdEIsSUFBSSxDQUFDcUcseUJBQXlCLEdBQUc7SUFDckM7QUEyVEo7QUFFQSxpRUFBZXBILGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxzcmNcXGFwcFxcW2xvY2FsZV1cXHV0aWxzXFxkb3dubG9hZE1hbmFnZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBPcHRpbWl6ZWQgRG93bmxvYWQgTWFuYWdlciB3aXRoIHBhcmFsbGVsIHByb2Nlc3NpbmdcbiAqIEhhbmRsZXMgY29uY3VycmVudCBmZm1wZWcgbG9hZGluZywgZG93bmxvYWQgVVJMIGZldGNoaW5nLCBhbmQgZmlsZSBkb3dubG9hZHNcbiAqL1xuXG5pbXBvcnQgQXVkaW9UcmFuc2NvZGVyIGZyb20gJy4vYXVkaW9UcmFuc2NvZGVyJztcbmltcG9ydCB7IGZldGNoRG93bmxvYWRTdGF0dXMsIGNoZWNrRG93bmxvYWRMaW5rIH0gZnJvbSAnLi4vdXRpbHMnO1xuaW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJztcblxuZXhwb3J0IGNsYXNzIERvd25sb2FkTWFuYWdlciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMudHJhbnNjb2RlciA9IG51bGw7XG4gICAgICAgIHRoaXMuYWJvcnRDb250cm9sbGVyID0gbnVsbDtcbiAgICAgICAgdGhpcy5wcm9ncmVzc1N0YXRlID0ge1xuICAgICAgICAgICAgdXJsRmV0Y2g6IDAsICAgICAgICAvLyDojrflj5bpk77mjqXov5vluqYgKDAtMjAlKVxuICAgICAgICAgICAgZmZtcGVnTG9hZDogMCwgICAgICAvLyBGRm1wZWfliqDovb3ov5vluqYgKDAtMTAlKVxuICAgICAgICAgICAgYXVkaW9Eb3dubG9hZDogMCwgICAvLyDpn7PpopHkuIvovb3ov5vluqYgKDAtNTAlKVxuICAgICAgICAgICAgdHJhbnNjb2Rpbmc6IDAgICAgICAvLyDovaznoIHov5vluqYgKDAtMjAlKVxuICAgICAgICB9O1xuICAgICAgICB0aGlzLnByb2dyZXNzQ2FsbGJhY2sgPSBudWxsO1xuICAgICAgICB0aGlzLnN0YXR1c0NhbGxiYWNrID0gbnVsbDtcbiAgICAgICAgdGhpcy5wcm9ncmVzc0NvbXBvbmVudENhbGxiYWNrID0gbnVsbDtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBDYWxjdWxhdGUgdG90YWwgcHJvZ3Jlc3MgZnJvbSBhbGwgcGFyYWxsZWwgb3BlcmF0aW9uc1xuICAgICAqL1xuICAgIF9jYWxjdWxhdGVUb3RhbFByb2dyZXNzKCkge1xuICAgICAgICBjb25zdCB7IHVybEZldGNoLCBmZm1wZWdMb2FkLCBhdWRpb0Rvd25sb2FkLCB0cmFuc2NvZGluZyB9ID0gdGhpcy5wcm9ncmVzc1N0YXRlO1xuICAgICAgICByZXR1cm4gTWF0aC5taW4oMTAwLCB1cmxGZXRjaCArIGZmbXBlZ0xvYWQgKyBhdWRpb0Rvd25sb2FkICsgdHJhbnNjb2RpbmcpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBzcGVjaWZpYyBwcm9ncmVzcyBjb21wb25lbnQgYW5kIHJlY2FsY3VsYXRlIHRvdGFsXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGNvbXBvbmVudCAtIFByb2dyZXNzIGNvbXBvbmVudCBuYW1lXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHByb2dyZXNzIC0gUHJvZ3Jlc3MgdmFsdWUgZm9yIHRoaXMgY29tcG9uZW50XG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHN0YXR1cyAtIFN0YXR1cyBtZXNzYWdlXG4gICAgICovXG4gICAgX3VwZGF0ZVByb2dyZXNzQ29tcG9uZW50KGNvbXBvbmVudCwgcHJvZ3Jlc3MsIHN0YXR1cykge1xuICAgICAgICB0aGlzLnByb2dyZXNzU3RhdGVbY29tcG9uZW50XSA9IHByb2dyZXNzO1xuICAgICAgICBjb25zdCB0b3RhbFByb2dyZXNzID0gdGhpcy5fY2FsY3VsYXRlVG90YWxQcm9ncmVzcygpO1xuXG4gICAgICAgIGlmICh0aGlzLnByb2dyZXNzQ2FsbGJhY2spIHtcbiAgICAgICAgICAgIHRoaXMucHJvZ3Jlc3NDYWxsYmFjayh0b3RhbFByb2dyZXNzKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5zdGF0dXNDYWxsYmFjaykge1xuICAgICAgICAgICAgdGhpcy5zdGF0dXNDYWxsYmFjayhzdGF0dXMpO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc29sZS5sb2coYFByb2dyZXNzIFVwZGF0ZSAtICR7Y29tcG9uZW50fTogJHtwcm9ncmVzc30lLCBUb3RhbDogJHt0b3RhbFByb2dyZXNzfSVgKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBQcm9jZXNzIGRvd25sb2FkIHdpdGggb3B0aW1pemVkIHBhcmFsbGVsIG9wZXJhdGlvbnNcbiAgICAgKiBAcGFyYW0ge09iamVjdH0gc29uZ0RhdGEgLSBTb25nIGluZm9ybWF0aW9uXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHJlcXVlc3RGb3JtYXQgLSBSZXF1ZXN0ZWQgYXVkaW8gZm9ybWF0XG4gICAgICogQHBhcmFtIHtGdW5jdGlvbn0gb25Qcm9ncmVzcyAtIFByb2dyZXNzIGNhbGxiYWNrXG4gICAgICogQHBhcmFtIHtGdW5jdGlvbn0gb25TdGF0dXMgLSBTdGF0dXMgdXBkYXRlIGNhbGxiYWNrXG4gICAgICogQHBhcmFtIHtGdW5jdGlvbn0gdCAtIFRyYW5zbGF0aW9uIGZ1bmN0aW9uXG4gICAgICovXG4gICAgYXN5bmMgcHJvY2Vzc0Rvd25sb2FkKHNvbmdEYXRhLCByZXF1ZXN0Rm9ybWF0LCBvblByb2dyZXNzLCBvblN0YXR1cywgdCkge1xuICAgICAgICB0aGlzLmFib3J0Q29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICAgICAgdGhpcy5wcm9ncmVzc0NhbGxiYWNrID0gb25Qcm9ncmVzcztcbiAgICAgICAgdGhpcy5zdGF0dXNDYWxsYmFjayA9IG9uU3RhdHVzO1xuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBSZXNldCBwcm9ncmVzcyBzdGF0ZVxuICAgICAgICAgICAgdGhpcy5wcm9ncmVzc1N0YXRlID0ge1xuICAgICAgICAgICAgICAgIHVybEZldGNoOiAwLFxuICAgICAgICAgICAgICAgIGZmbXBlZ0xvYWQ6IDAsXG4gICAgICAgICAgICAgICAgYXVkaW9Eb3dubG9hZDogMCxcbiAgICAgICAgICAgICAgICB0cmFuc2NvZGluZzogMFxuICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgdGhpcy5fdXBkYXRlUHJvZ3Jlc3NDb21wb25lbnQoJ3VybEZldGNoJywgMCwgdChcInByZXBhcmluZ19kb3dubG9hZFwiKSk7XG5cbiAgICAgICAgICAgIC8vIEluaXRpYWxpemUgdHJhbnNjb2RlclxuICAgICAgICAgICAgdGhpcy50cmFuc2NvZGVyID0gbmV3IEF1ZGlvVHJhbnNjb2RlcigpO1xuXG4gICAgICAgICAgICAvLyBTZXQgdXAgdHJhbnNjb2RpbmcgcHJvZ3Jlc3MgY2FsbGJhY2sgKG1hcHMgdG8gMC0yMCUgb2YgdHJhbnNjb2RpbmcgY29tcG9uZW50KVxuICAgICAgICAgICAgdGhpcy50cmFuc2NvZGVyLnNldFByb2dyZXNzQ2FsbGJhY2soKHsgcHJvZ3Jlc3MgfSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHRyYW5zY29kaW5nUHJvZ3Jlc3MgPSBwcm9ncmVzcyAqIDIwOyAvLyAwLTIwJVxuICAgICAgICAgICAgICAgIHRoaXMuX3VwZGF0ZVByb2dyZXNzQ29tcG9uZW50KCd0cmFuc2NvZGluZycsIHRyYW5zY29kaW5nUHJvZ3Jlc3MsIHQoXCJ0cmFuc2NvZGluZ19hdWRpb1wiKSk7XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgLy8gU3RhcnQgcGFyYWxsZWwgb3BlcmF0aW9uc1xuICAgICAgICAgICAgY29uc3QgZmZtcGVnTG9hZFByb21pc2UgPSByZXF1ZXN0Rm9ybWF0ICE9PSAnd2VibScgP1xuICAgICAgICAgICAgICAgIHRoaXMuX2xvYWRGRm1wZWdXaXRoUHJvZ3Jlc3ModCkgOlxuICAgICAgICAgICAgICAgIHRoaXMuX3NraXBGRm1wZWdMb2FkKCk7XG5cbiAgICAgICAgICAgIGNvbnN0IGRvd25sb2FkVXJsUHJvbWlzZSA9IHRoaXMuX2dldERvd25sb2FkVXJsV2l0aFByb2dyZXNzKHNvbmdEYXRhLCB0KTtcblxuICAgICAgICAgICAgLy8gUHJlLWZldGNoIHRodW1ibmFpbCAob3B0aW9uYWwsIGRvbid0IGJsb2NrIG9uIGZhaWx1cmUpXG4gICAgICAgICAgICBjb25zdCB0aHVtYm5haWxQcm9taXNlID0gc29uZ0RhdGEudGh1bWJuYWlsID9cbiAgICAgICAgICAgICAgICB0aGlzLl9mZXRjaFRodW1ibmFpbChzb25nRGF0YS50aHVtYm5haWwpIDpcbiAgICAgICAgICAgICAgICBQcm9taXNlLnJlc29sdmUobnVsbCk7XG5cbiAgICAgICAgICAgIC8vIEdldCBkb3dubG9hZCBVUkwgZmlyc3QgKGRvbid0IHdhaXQgZm9yIEZGbXBlZyBpZiBub3QgbmVlZGVkIHlldClcbiAgICAgICAgICAgIGNvbnN0IG9yaWdpbmFsQXVkaW9VcmwgPSBhd2FpdCBkb3dubG9hZFVybFByb21pc2U7XG5cbiAgICAgICAgICAgIC8vIFN0YXJ0IGF1ZGlvIGRvd25sb2FkIGltbWVkaWF0ZWx5IGFmdGVyIGdldHRpbmcgVVJMXG4gICAgICAgICAgICBjb25zdCBhdWRpb0Rvd25sb2FkUHJvbWlzZSA9IHRoaXMuX2ZldGNoQXVkaW9XaXRoUHJvZ3Jlc3Mob3JpZ2luYWxBdWRpb1VybCwgdCk7XG5cbiAgICAgICAgICAgIC8vIEZvciB3ZWJtIGZvcm1hdCwgd2UgZG9uJ3QgbmVlZCBGRm1wZWcsIHNvIGRvd25sb2FkIGFuZCByZXR1cm4gaW1tZWRpYXRlbHlcbiAgICAgICAgICAgIGlmIChyZXF1ZXN0Rm9ybWF0ID09PSAnd2VibScpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBhdWRpb0Jsb2IgPSBhd2FpdCBhdWRpb0Rvd25sb2FkUHJvbWlzZTtcbiAgICAgICAgICAgICAgICB0aGlzLl91cGRhdGVQcm9ncmVzc0NvbXBvbmVudCgndHJhbnNjb2RpbmcnLCAyMCwgdChcInByZXBhcmluZ19kb3dubG9hZFwiKSk7XG4gICAgICAgICAgICAgICAgdGhpcy5fZG93bmxvYWRGaWxlKGF1ZGlvQmxvYiwgYCR7c29uZ0RhdGEudGl0bGV9IC0gJHtzb25nRGF0YS5hcnRpc3R9LndlYm1gKTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIFdhaXQgZm9yIEZGbXBlZyBsb2FkaW5nIHRvIGNvbXBsZXRlXG4gICAgICAgICAgICBhd2FpdCBmZm1wZWdMb2FkUHJvbWlzZTtcblxuICAgICAgICAgICAgLy8gV2FpdCBmb3IgYXVkaW8gZG93bmxvYWQgdG8gY29tcGxldGVcbiAgICAgICAgICAgIGNvbnN0IFthdWRpb0Jsb2IsIGltYWdlQmxvYl0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgICAgICAgICAgYXVkaW9Eb3dubG9hZFByb21pc2UsXG4gICAgICAgICAgICAgICAgdGh1bWJuYWlsUHJvbWlzZVxuICAgICAgICAgICAgXSk7XG5cbiAgICAgICAgICAgIC8vIFN0YXJ0IHRyYW5zY29kaW5nXG4gICAgICAgICAgICB0aGlzLl91cGRhdGVQcm9ncmVzc0NvbXBvbmVudCgndHJhbnNjb2RpbmcnLCAwLCB0KFwidHJhbnNjb2RpbmdfYXVkaW9cIikpO1xuXG4gICAgICAgICAgICAvLyBUcmFuc2NvZGUgYXVkaW8gKHByb2dyZXNzIGhhbmRsZWQgYnkgdHJhbnNjb2RlciBjYWxsYmFjaylcbiAgICAgICAgICAgIGNvbnN0IHRyYW5zY29kZWREYXRhID0gYXdhaXQgdGhpcy50cmFuc2NvZGVyLnRyYW5zY29kZUF1ZGlvKFxuICAgICAgICAgICAgICAgIGF1ZGlvQmxvYixcbiAgICAgICAgICAgICAgICBpbWFnZUJsb2IsXG4gICAgICAgICAgICAgICAgcmVxdWVzdEZvcm1hdCxcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIHRpdGxlOiBzb25nRGF0YS50aXRsZSxcbiAgICAgICAgICAgICAgICAgICAgYXJ0aXN0OiBzb25nRGF0YS5hcnRpc3QsXG4gICAgICAgICAgICAgICAgICAgIGFsYnVtOiBzb25nRGF0YS5hbGJ1bVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICk7XG5cbiAgICAgICAgICAgIC8vIFRyYW5zY29kaW5nIGNvbXBsZXRlXG4gICAgICAgICAgICB0aGlzLl91cGRhdGVQcm9ncmVzc0NvbXBvbmVudCgndHJhbnNjb2RpbmcnLCAyMCwgdChcInByZXBhcmluZ19kb3dubG9hZFwiKSk7XG5cbiAgICAgICAgICAgIC8vIENyZWF0ZSBhbmQgZG93bmxvYWQgdHJhbnNjb2RlZCBmaWxlXG4gICAgICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW3RyYW5zY29kZWREYXRhXSwge1xuICAgICAgICAgICAgICAgIHR5cGU6IHJlcXVlc3RGb3JtYXQgPT09ICdtcDMnID8gJ2F1ZGlvL21wZWcnIDogJ2F1ZGlvL2ZsYWMnXG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgdGhpcy5fZG93bmxvYWRGaWxlKGJsb2IsIGAke3NvbmdEYXRhLnRpdGxlfSAtICR7c29uZ0RhdGEuYXJ0aXN0fS4ke3JlcXVlc3RGb3JtYXR9YCk7XG4gICAgICAgICAgICB0aGlzLl91cGRhdGVQcm9ncmVzc0NvbXBvbmVudCgndHJhbnNjb2RpbmcnLCAyMCwgdChcImRvd25sb2FkX2NvbXBsZXRlXCIpKTtcblxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgaWYgKGVycm9yLm5hbWUgPT09ICdBYm9ydEVycm9yJykge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcih0KFwiZG93bmxvYWRfY2FuY2VsbGVkXCIpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogTG9hZCBGRm1wZWcgd2l0aCBwcm9ncmVzcyB1cGRhdGVzXG4gICAgICovXG4gICAgYXN5bmMgX2xvYWRGRm1wZWdXaXRoUHJvZ3Jlc3ModCkge1xuICAgICAgICB0aGlzLl91cGRhdGVQcm9ncmVzc0NvbXBvbmVudCgnZmZtcGVnTG9hZCcsIDAsIHQoXCJsb2FkaW5nX3RyYW5zY29kZXJcIikpO1xuICAgICAgICBhd2FpdCB0aGlzLnRyYW5zY29kZXIubG9hZCgpO1xuICAgICAgICB0aGlzLl91cGRhdGVQcm9ncmVzc0NvbXBvbmVudCgnZmZtcGVnTG9hZCcsIDEwLCB0KFwidHJhbnNjb2Rlcl9yZWFkeVwiKSk7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIFNraXAgRkZtcGVnIGxvYWRpbmcgZm9yIHdlYm0gZm9ybWF0XG4gICAgICovXG4gICAgYXN5bmMgX3NraXBGRm1wZWdMb2FkKCkge1xuICAgICAgICB0aGlzLl91cGRhdGVQcm9ncmVzc0NvbXBvbmVudCgnZmZtcGVnTG9hZCcsIDEwLCBcIkZGbXBlZyBub3QgbmVlZGVkIGZvciB3ZWJtXCIpO1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBHZXQgZG93bmxvYWQgVVJMIHdpdGggcmV0cmllcyBhbmQgcHJvZ3Jlc3MgdXBkYXRlc1xuICAgICAqL1xuICAgIGFzeW5jIF9nZXREb3dubG9hZFVybFdpdGhQcm9ncmVzcyhzb25nRGF0YSwgdCkge1xuICAgICAgICBjb25zdCBtYXhSZXRyaWVzID0gMjA7XG4gICAgICAgIGNvbnN0IHJldHJ5RGVsYXkgPSAyMDAwO1xuXG4gICAgICAgIGZvciAobGV0IHJldHJpZXMgPSAwOyByZXRyaWVzIDwgbWF4UmV0cmllczsgcmV0cmllcysrKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5hYm9ydENvbnRyb2xsZXIuc2lnbmFsLmFib3J0ZWQpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Rvd25sb2FkIGNhbmNlbGxlZCcpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBVcGRhdGUgcHJvZ3Jlc3MgYmFzZWQgb24gcmV0cnkgYXR0ZW1wdHMgKDAtMjAlIHJhbmdlKVxuICAgICAgICAgICAgY29uc3QgcHJvZ3Jlc3NTdGVwID0gTWF0aC5taW4oMjAsIChyZXRyaWVzICsgMSkgKiAoMjAgLyBtYXhSZXRyaWVzKSk7XG4gICAgICAgICAgICB0aGlzLl91cGRhdGVQcm9ncmVzc0NvbXBvbmVudCgndXJsRmV0Y2gnLCBwcm9ncmVzc1N0ZXAsIHQoXCJmZXRjaGluZ19hdWRpb1wiKSk7XG5cbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgc3RhdHVzID0gYXdhaXQgZmV0Y2hEb3dubG9hZFN0YXR1cyhzb25nRGF0YSwgJ2Rvd25sb2FkJyk7XG4gICAgICAgICAgICAgICAgaWYgKHN0YXR1cy5kb3dubG9hZF91cmwpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNSZWFkeSA9IGF3YWl0IGNoZWNrRG93bmxvYWRMaW5rKHN0YXR1cy5kb3dubG9hZF91cmwpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoaXNSZWFkeSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5fdXBkYXRlUHJvZ3Jlc3NDb21wb25lbnQoJ3VybEZldGNoJywgMjAsIHQoXCJkb3dubG9hZF91cmxfcmVhZHlcIikpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHN0YXR1cy5kb3dubG9hZF91cmw7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihgRG93bmxvYWQgVVJMIGZldGNoIGF0dGVtcHQgJHtyZXRyaWVzICsgMX0gZmFpbGVkOmAsIGVycm9yKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8gV2FpdCBiZWZvcmUgcmV0cnlcbiAgICAgICAgICAgIGF3YWl0IHRoaXMuX2RlbGF5KHJldHJ5RGVsYXkpO1xuICAgICAgICB9XG5cbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHQoXCJkb3dubG9hZF90aW1lb3V0XCIpKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBGZXRjaCBhdWRpbyBmaWxlIHdpdGggcHJvZ3Jlc3MgdHJhY2tpbmcgdXNpbmcgYXhpb3NcbiAgICAgKi9cbiAgICBhc3luYyBfZmV0Y2hBdWRpb1dpdGhQcm9ncmVzcyh1cmwsIHQpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIHRoaXMuX3VwZGF0ZVByb2dyZXNzQ29tcG9uZW50KCdhdWRpb0Rvd25sb2FkJywgMCwgdChcImRvd25sb2FkaW5nX2ZpbGVzXCIpKTtcblxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcyh7XG4gICAgICAgICAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgICAgICAgICB1cmw6IHVybCxcbiAgICAgICAgICAgICAgICByZXNwb25zZVR5cGU6ICdibG9iJyxcbiAgICAgICAgICAgICAgICBzaWduYWw6IHRoaXMuYWJvcnRDb250cm9sbGVyLnNpZ25hbCxcbiAgICAgICAgICAgICAgICBvbkRvd25sb2FkUHJvZ3Jlc3M6IChwcm9ncmVzc0V2ZW50KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChwcm9ncmVzc0V2ZW50Lmxlbmd0aENvbXB1dGFibGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIE1hcCBkb3dubG9hZCBwcm9ncmVzcyB0byAwLTUwJSByYW5nZSBmb3IgYXVkaW9Eb3dubG9hZCBjb21wb25lbnRcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRvd25sb2FkUGVyY2VudCA9IChwcm9ncmVzc0V2ZW50LmxvYWRlZCAvIHByb2dyZXNzRXZlbnQudG90YWwpICogMTAwO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbWFwcGVkUHJvZ3Jlc3MgPSAoZG93bmxvYWRQZXJjZW50IC8gMTAwKSAqIDUwOyAvLyAwLTUwJVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5fdXBkYXRlUHJvZ3Jlc3NDb21wb25lbnQoJ2F1ZGlvRG93bmxvYWQnLCBtYXBwZWRQcm9ncmVzcywgdChcImRvd25sb2FkaW5nX2ZpbGVzXCIpKTtcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIElmIHdlIGNhbid0IHRyYWNrIHByb2dyZXNzLCBzaG93IGluY3JlbWVudGFsIHVwZGF0ZXNcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gRGF0ZS5ub3coKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghdGhpcy5kb3dubG9hZFN0YXJ0VGltZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZG93bmxvYWRTdGFydFRpbWUgPSBjdXJyZW50VGltZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGVsYXBzZWQgPSAoY3VycmVudFRpbWUgLSB0aGlzLmRvd25sb2FkU3RhcnRUaW1lKSAvIDEwMDA7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBlc3RpbWF0ZWRQcm9ncmVzcyA9IE1hdGgubWluKDQ1LCBlbGFwc2VkICogMik7IC8vIFNsb3cgaW5jcmVtZW50IHVwIHRvIDQ1JVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5fdXBkYXRlUHJvZ3Jlc3NDb21wb25lbnQoJ2F1ZGlvRG93bmxvYWQnLCBlc3RpbWF0ZWRQcm9ncmVzcywgdChcImRvd25sb2FkaW5nX2ZpbGVzXCIpKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAvLyBEb3dubG9hZCBjb21wbGV0ZWRcbiAgICAgICAgICAgIHRoaXMuX3VwZGF0ZVByb2dyZXNzQ29tcG9uZW50KCdhdWRpb0Rvd25sb2FkJywgNTAsIHQoXCJkb3dubG9hZF9jb21wbGV0ZV9wcmVwYXJpbmdcIikpO1xuICAgICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG5cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGlmIChlcnJvci5uYW1lID09PSAnQWJvcnRFcnJvcicgfHwgZXJyb3IuY29kZSA9PT0gJ0VSUl9DQU5DRUxFRCcpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IodChcImRvd25sb2FkX2NhbmNlbGxlZFwiKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IodChcImZhaWxlZF90b19kb3dubG9hZF9hdWRpb1wiKSk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBGZXRjaCBhdWRpbyBmaWxlIHdpdGggZXJyb3IgaGFuZGxpbmcgKGZhbGxiYWNrIG1ldGhvZClcbiAgICAgKi9cbiAgICBhc3luYyBfZmV0Y2hBdWRpbyh1cmwsIHQpIHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh1cmwsIHtcbiAgICAgICAgICAgIHNpZ25hbDogdGhpcy5hYm9ydENvbnRyb2xsZXIuc2lnbmFsXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcih0KFwiZmFpbGVkX3RvX2Rvd25sb2FkX2F1ZGlvXCIpKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiByZXNwb25zZS5ibG9iKCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogRmV0Y2ggdGh1bWJuYWlsIHdpdGggZ3JhY2VmdWwgZmFpbHVyZVxuICAgICAqL1xuICAgIGFzeW5jIF9mZXRjaFRodW1ibmFpbCh0aHVtYm5haWxVcmwpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godGh1bWJuYWlsVXJsLCB7XG4gICAgICAgICAgICAgICAgc2lnbmFsOiB0aGlzLmFib3J0Q29udHJvbGxlci5zaWduYWxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2Uub2sgPyByZXNwb25zZS5ibG9iKCkgOiBudWxsO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKCdUaHVtYm5haWwgZmV0Y2ggZmFpbGVkOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogRG93bmxvYWQgZmlsZSB0byB1c2VyJ3MgZGV2aWNlXG4gICAgICovXG4gICAgX2Rvd25sb2FkRmlsZShibG9iLCBmaWxlbmFtZSkge1xuICAgICAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICAgICAgICBjb25zdCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpO1xuICAgICAgICBhLmhyZWYgPSB1cmw7XG4gICAgICAgIGEuZG93bmxvYWQgPSBmaWxlbmFtZTtcbiAgICAgICAgYS5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgICAgICBcbiAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChhKTtcbiAgICAgICAgYS5jbGljaygpO1xuICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGEpO1xuICAgICAgICBcbiAgICAgICAgLy8gQ2xlYW4gdXAgb2JqZWN0IFVSTFxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IFVSTC5yZXZva2VPYmplY3RVUkwodXJsKSwgMTAwKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBVdGlsaXR5IGRlbGF5IGZ1bmN0aW9uXG4gICAgICovXG4gICAgX2RlbGF5KG1zKSB7XG4gICAgICAgIHJldHVybiBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQocmVzb2x2ZSwgbXMpO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICAvLyBBbGxvdyBjYW5jZWxsYXRpb25cbiAgICAgICAgICAgIHRoaXMuYWJvcnRDb250cm9sbGVyLnNpZ25hbC5hZGRFdmVudExpc3RlbmVyKCdhYm9ydCcsICgpID0+IHtcbiAgICAgICAgICAgICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKTtcbiAgICAgICAgICAgICAgICByZXNvbHZlKCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogQ2FuY2VsIG9uZ29pbmcgZG93bmxvYWRcbiAgICAgKi9cbiAgICBjYW5jZWwoKSB7XG4gICAgICAgIGlmICh0aGlzLmFib3J0Q29udHJvbGxlcikge1xuICAgICAgICAgICAgdGhpcy5hYm9ydENvbnRyb2xsZXIuYWJvcnQoKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIENsZWFuIHVwIHJlc291cmNlc1xuICAgICAqL1xuICAgIGNsZWFudXAoKSB7XG4gICAgICAgIHRoaXMuY2FuY2VsKCk7XG4gICAgICAgIFxuICAgICAgICBpZiAodGhpcy50cmFuc2NvZGVyKSB7XG4gICAgICAgICAgICB0aGlzLnRyYW5zY29kZXIudGVybWluYXRlKCk7XG4gICAgICAgICAgICB0aGlzLnRyYW5zY29kZXIgPSBudWxsO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICB0aGlzLmFib3J0Q29udHJvbGxlciA9IG51bGw7XG4gICAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBEb3dubG9hZE1hbmFnZXI7XG4iXSwibmFtZXMiOlsiQXVkaW9UcmFuc2NvZGVyIiwiZmV0Y2hEb3dubG9hZFN0YXR1cyIsImNoZWNrRG93bmxvYWRMaW5rIiwiYXhpb3MiLCJEb3dubG9hZE1hbmFnZXIiLCJfY2FsY3VsYXRlVG90YWxQcm9ncmVzcyIsInVybEZldGNoIiwiZmZtcGVnTG9hZCIsImF1ZGlvRG93bmxvYWQiLCJ0cmFuc2NvZGluZyIsInByb2dyZXNzU3RhdGUiLCJNYXRoIiwibWluIiwiX3VwZGF0ZVByb2dyZXNzQ29tcG9uZW50IiwiY29tcG9uZW50IiwicHJvZ3Jlc3MiLCJzdGF0dXMiLCJ0b3RhbFByb2dyZXNzIiwicHJvZ3Jlc3NDYWxsYmFjayIsInN0YXR1c0NhbGxiYWNrIiwiY29uc29sZSIsImxvZyIsInByb2Nlc3NEb3dubG9hZCIsInNvbmdEYXRhIiwicmVxdWVzdEZvcm1hdCIsIm9uUHJvZ3Jlc3MiLCJvblN0YXR1cyIsInQiLCJhYm9ydENvbnRyb2xsZXIiLCJBYm9ydENvbnRyb2xsZXIiLCJ0cmFuc2NvZGVyIiwic2V0UHJvZ3Jlc3NDYWxsYmFjayIsInRyYW5zY29kaW5nUHJvZ3Jlc3MiLCJmZm1wZWdMb2FkUHJvbWlzZSIsIl9sb2FkRkZtcGVnV2l0aFByb2dyZXNzIiwiX3NraXBGRm1wZWdMb2FkIiwiZG93bmxvYWRVcmxQcm9taXNlIiwiX2dldERvd25sb2FkVXJsV2l0aFByb2dyZXNzIiwidGh1bWJuYWlsUHJvbWlzZSIsInRodW1ibmFpbCIsIl9mZXRjaFRodW1ibmFpbCIsIlByb21pc2UiLCJyZXNvbHZlIiwib3JpZ2luYWxBdWRpb1VybCIsImF1ZGlvRG93bmxvYWRQcm9taXNlIiwiX2ZldGNoQXVkaW9XaXRoUHJvZ3Jlc3MiLCJhdWRpb0Jsb2IiLCJfZG93bmxvYWRGaWxlIiwidGl0bGUiLCJhcnRpc3QiLCJpbWFnZUJsb2IiLCJhbGwiLCJ0cmFuc2NvZGVkRGF0YSIsInRyYW5zY29kZUF1ZGlvIiwiYWxidW0iLCJibG9iIiwiQmxvYiIsInR5cGUiLCJlcnJvciIsIm5hbWUiLCJFcnJvciIsImxvYWQiLCJtYXhSZXRyaWVzIiwicmV0cnlEZWxheSIsInJldHJpZXMiLCJzaWduYWwiLCJhYm9ydGVkIiwicHJvZ3Jlc3NTdGVwIiwiZG93bmxvYWRfdXJsIiwiaXNSZWFkeSIsIndhcm4iLCJfZGVsYXkiLCJ1cmwiLCJyZXNwb25zZSIsIm1ldGhvZCIsInJlc3BvbnNlVHlwZSIsIm9uRG93bmxvYWRQcm9ncmVzcyIsInByb2dyZXNzRXZlbnQiLCJsZW5ndGhDb21wdXRhYmxlIiwiZG93bmxvYWRQZXJjZW50IiwibG9hZGVkIiwidG90YWwiLCJtYXBwZWRQcm9ncmVzcyIsImN1cnJlbnRUaW1lIiwiRGF0ZSIsIm5vdyIsImRvd25sb2FkU3RhcnRUaW1lIiwiZWxhcHNlZCIsImVzdGltYXRlZFByb2dyZXNzIiwiZGF0YSIsImNvZGUiLCJfZmV0Y2hBdWRpbyIsImZldGNoIiwib2siLCJ0aHVtYm5haWxVcmwiLCJmaWxlbmFtZSIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImEiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJzdHlsZSIsImRpc3BsYXkiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInJlbW92ZUNoaWxkIiwic2V0VGltZW91dCIsInJldm9rZU9iamVjdFVSTCIsIm1zIiwidGltZW91dElkIiwiYWRkRXZlbnRMaXN0ZW5lciIsImNsZWFyVGltZW91dCIsImNhbmNlbCIsImFib3J0IiwiY2xlYW51cCIsInRlcm1pbmF0ZSIsImNvbnN0cnVjdG9yIiwicHJvZ3Jlc3NDb21wb25lbnRDYWxsYmFjayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});