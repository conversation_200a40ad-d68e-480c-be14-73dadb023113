"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\nclass DownloadManager {\n    /**\n     * Process download with optimized parallel operations\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgress - Progress callback\n     * @param {Function} onStatus - Status update callback\n     * @param {Function} t - Translation function\n     */ async processDownload(songData, requestFormat, onProgress, onStatus, t) {\n        this.abortController = new AbortController();\n        try {\n            onStatus(t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                onProgress(Math.round(progress * 100));\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this.transcoder.load() : Promise.resolve();\n            const downloadUrlPromise = this._getDownloadUrl(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            onStatus(t(\"fetching_audio\"));\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            onStatus(t(\"downloading_files\"));\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudio(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const [audioBlob, imageBlob] = await Promise.all([\n                    audioDownloadPromise,\n                    thumbnailPromise\n                ]);\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                onStatus(t(\"download_complete\"));\n                return;\n            }\n            // For transcoding formats, wait for both audio download and FFmpeg loading\n            const [audioBlob, , imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                ffmpegLoadPromise,\n                thumbnailPromise\n            ]);\n            // Handle direct download for webm format\n            if (requestFormat === 'webm') {\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                onStatus(t(\"download_complete\"));\n                return;\n            }\n            onStatus(t(\"transcoding_audio\"));\n            // Transcode audio\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            onStatus(t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            onStatus(t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get download URL with retries and timeout\n     */ async _getDownloadUrl(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with error handling\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});