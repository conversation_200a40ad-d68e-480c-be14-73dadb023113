"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\nclass DownloadManager {\n    /**\n     * Update progress with specific percentage\n     * @param {number} progress - Progress percentage (0-100)\n     * @param {string} status - Status message\n     */ _updateProgress(progress, status) {\n        this.currentProgress = progress;\n        if (this.progressCallback) {\n            this.progressCallback(progress);\n        }\n        if (this.statusCallback) {\n            this.statusCallback(status);\n        }\n    }\n    /**\n     * Process download with optimized parallel operations\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgress - Progress callback\n     * @param {Function} onStatus - Status update callback\n     * @param {Function} t - Translation function\n     */ async processDownload(songData, requestFormat, onProgress, onStatus, t) {\n        this.abortController = new AbortController();\n        this.progressCallback = onProgress;\n        this.statusCallback = onStatus;\n        try {\n            this._updateProgress(0, t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 70-90% range)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = 70 + progress * 20; // 70-90%\n                this._updateProgress(Math.round(transcodingProgress), t(\"transcoding_audio\"));\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : Promise.resolve();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            this._updateProgress(5, t(\"fetching_audio\"));\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            this._updateProgress(20, t(\"download_url_ready\"));\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgress(90, t(\"preparing_download\"));\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                this._updateProgress(100, t(\"download_complete\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            this._updateProgress(30, t(\"transcoder_ready\"));\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            this._updateProgress(70, t(\"transcoding_audio\"));\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            this._updateProgress(90, t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            this._updateProgress(100, t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress(t) {\n        this._updateProgress(10, t(\"loading_transcoder\"));\n        await this.transcoder.load();\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts\n            const progressStep = Math.min(15, 5 + retries * 0.5);\n            this._updateProgress(progressStep, t(\"fetching_audio\"));\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with error handling\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.currentProgress = 0;\n        this.progressCallback = null;\n        this.statusCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});