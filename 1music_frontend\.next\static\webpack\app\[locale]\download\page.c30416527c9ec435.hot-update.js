"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\nclass DownloadManager {\n    /**\n     * Update progress with specific percentage\n     * @param {number} progress - Progress percentage (0-100)\n     * @param {string} status - Status message\n     */ _updateProgress(progress, status) {\n        this.currentProgress = progress;\n        if (this.progressCallback) {\n            this.progressCallback(progress);\n        }\n        if (this.statusCallback) {\n            this.statusCallback(status);\n        }\n    }\n    /**\n     * Process download with optimized parallel operations\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgress - Progress callback\n     * @param {Function} onStatus - Status update callback\n     * @param {Function} t - Translation function\n     */ async processDownload(songData, requestFormat, onProgress, onStatus, t) {\n        this.abortController = new AbortController();\n        this.progressCallback = onProgress;\n        this.statusCallback = onStatus;\n        try {\n            this._updateProgress(0, t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 70-90% range)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = 70 + progress * 20; // 70-90%\n                this._updateProgress(Math.round(transcodingProgress), t(\"transcoding_audio\"));\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : Promise.resolve();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            this._updateProgress(5, t(\"fetching_audio\"));\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            this._updateProgress(20, t(\"download_url_ready\"));\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgress(90, t(\"preparing_download\"));\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                this._updateProgress(100, t(\"download_complete\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            this._updateProgress(30, t(\"transcoder_ready\"));\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            onStatus(t(\"transcoding_audio\"));\n            // Transcode audio\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            onStatus(t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            onStatus(t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get download URL with retries and timeout\n     */ async _getDownloadUrl(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with error handling\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.currentProgress = 0;\n        this.progressCallback = null;\n        this.statusCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});