"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(app-pages-browser)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/utils/downloadManager */ \"(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DownloadPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"Download\");\n    const title = searchParams.get('title');\n    const album = searchParams.get('album');\n    const artist = searchParams.get('artist');\n    const videoId = searchParams.get('videoId');\n    const request_format = searchParams.get('request_format');\n    const song_hash = searchParams.get('song_hash');\n    const thumbnail = searchParams.get('thumbnail');\n    // Use the custom hook for progress management\n    const { progress, status, isLoading, error, updateProgressComponent, resetProgress, setLoadingState, setErrorState, completeDownload, getProgressBreakdown } = useDownloadProgress();\n    const downloadManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DownloadPage.useEffect\": ()=>{\n            const script = document.createElement(\"script\");\n            script.type = \"text/javascript\";\n            script.src = \"//plantationexhaust.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js\";\n            document.head.appendChild(script);\n            const script_1 = document.createElement(\"script\");\n            script_1.type = \"text/javascript\";\n            script_1.src = \"//pl26003516.effectiveratecpm.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js\";\n            document.body.appendChild(script_1);\n            const script_ = document.createElement(\"script\");\n            script_.setAttribute(\"data-cfasync\", \"false\");\n            script_.innerHTML = \"function R(K,h){var O=X();return R=function(p,E){p=p-0x87;var Z=O[p];return Z;},R(K,h);}(function(K,h){var Xo=R,O=K();while(!![]){try{var p=parseInt(Xo(0xac))/0x1*(-parseInt(Xo(0x90))/0x2)+parseInt(Xo(0xa5))/0x3*(-parseInt(Xo(0x8d))/0x4)+parseInt(Xo(0xb5))/0x5*(-parseInt(Xo(0x93))/0x6)+parseInt(Xo(0x89))/0x7+-parseInt(Xo(0xa1))/0x8+parseInt(Xo(0xa7))/0x9*(parseInt(Xo(0xb2))/0xa)+parseInt(Xo(0x95))/0xb*(parseInt(Xo(0x9f))/0xc);if(p===h)break;else O['push'](O['shift']());}catch(E){O['push'](O['shift']());}}}(X,0x33565),(function(){var XG=R;function K(){var Xe=R,h=306775,O='a3klsam',p='a',E='db',Z=Xe(0xad),S=Xe(0xb6),o=Xe(0xb0),e='cs',D='k',c='pro',u='xy',Q='su',G=Xe(0x9a),j='se',C='cr',z='et',w='sta',Y='tic',g='adMa',V='nager',A=p+E+Z+S+o,s=p+E+Z+S+e,W=p+E+Z+D+'-'+c+u+'-'+Q+G+'-'+j+C+z,L='/'+w+Y+'/'+g+V+Xe(0x9c),T=A,t=s,I=W,N=null,r=null,n=new Date()[Xe(0x94)]()[Xe(0x8c)]('T')[0x0][Xe(0xa3)](/-/ig,'.')['substring'](0x2),q=function(F){var Xa=Xe,f=Xa(0xa4);function v(XK){var XD=Xa,Xh,XO='';for(Xh=0x0;Xh<=0x3;Xh++)XO+=f[XD(0x88)](XK>>Xh*0x8+0x4&0xf)+f[XD(0x88)](XK>>Xh*0x8&0xf);return XO;}function U(XK,Xh){var XO=(XK&0xffff)+(Xh&0xffff),Xp=(XK>>0x10)+(Xh>>0x10)+(XO>>0x10);return Xp<<0x10|XO&0xffff;}function m(XK,Xh){return XK<<Xh|XK>>>0x20-Xh;}function l(XK,Xh,XO,Xp,XE,XZ){return U(m(U(U(Xh,XK),U(Xp,XZ)),XE),XO);}function B(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&XO|~Xh&Xp,XK,Xh,XE,XZ,XS);}function y(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh&Xp|XO&~Xp,XK,Xh,XE,XZ,XS);}function H(XK,Xh,XO,Xp,XE,XZ,XS){return l(Xh^XO^Xp,XK,Xh,XE,XZ,XS);}function X0(XK,Xh,XO,Xp,XE,XZ,XS){return l(XO^(Xh|~Xp),XK,Xh,XE,XZ,XS);}function X1(XK){var Xc=Xa,Xh,XO=(XK[Xc(0x9b)]+0x8>>0x6)+0x1,Xp=new Array(XO*0x10);for(Xh=0x0;Xh<XO*0x10;Xh++)Xp[Xh]=0x0;for(Xh=0x0;Xh<XK[Xc(0x9b)];Xh++)Xp[Xh>>0x2]|=XK[Xc(0x8b)](Xh)<<Xh%0x4*0x8;return Xp[Xh>>0x2]|=0x80<<Xh%0x4*0x8,Xp[XO*0x10-0x2]=XK[Xc(0x9b)]*0x8,Xp;}var X2,X3=X1(F),X4=0x67452301,X5=-0x10325477,X6=-0x67452302,X7=0x10325476,X8,X9,XX,XR;for(X2=0x0;X2<X3[Xa(0x9b)];X2+=0x10){X8=X4,X9=X5,XX=X6,XR=X7,X4=B(X4,X5,X6,X7,X3[X2+0x0],0x7,-0x28955b88),X7=B(X7,X4,X5,X6,X3[X2+0x1],0xc,-0x173848aa),X6=B(X6,X7,X4,X5,X3[X2+0x2],0x11,0x242070db),X5=B(X5,X6,X7,X4,X3[X2+0x3],0x16,-0x3e423112),X4=B(X4,X5,X6,X7,X3[X2+0x4],0x7,-0xa83f051),X7=B(X7,X4,X5,X6,X3[X2+0x5],0xc,0x4787c62a),X6=B(X6,X7,X4,X5,X3[X2+0x6],0x11,-0x57cfb9ed),X5=B(X5,X6,X7,X4,X3[X2+0x7],0x16,-0x2b96aff),X4=B(X4,X5,X6,X7,X3[X2+0x8],0x7,0x698098d8),X7=B(X7,X4,X5,X6,X3[X2+0x9],0xc,-0x74bb0851),X6=B(X6,X7,X4,X5,X3[X2+0xa],0x11,-0xa44f),X5=B(X5,X6,X7,X4,X3[X2+0xb],0x16,-0x76a32842),X4=B(X4,X5,X6,X7,X3[X2+0xc],0x7,0x6b901122),X7=B(X7,X4,X5,X6,X3[X2+0xd],0xc,-0x2678e6d),X6=B(X6,X7,X4,X5,X3[X2+0xe],0x11,-0x5986bc72),X5=B(X5,X6,X7,X4,X3[X2+0xf],0x16,0x49b40821),X4=y(X4,X5,X6,X7,X3[X2+0x1],0x5,-0x9e1da9e),X7=y(X7,X4,X5,X6,X3[X2+0x6],0x9,-0x3fbf4cc0),X6=y(X6,X7,X4,X5,X3[X2+0xb],0xe,0x265e5a51),X5=y(X5,X6,X7,X4,X3[X2+0x0],0x14,-0x16493856),X4=y(X4,X5,X6,X7,X3[X2+0x5],0x5,-0x29d0efa3),X7=y(X7,X4,X5,X6,X3[X2+0xa],0x9,0x2441453),X6=y(X6,X7,X4,X5,X3[X2+0xf],0xe,-0x275e197f),X5=y(X5,X6,X7,X4,X3[X2+0x4],0x14,-0x182c0438),X4=y(X4,X5,X6,X7,X3[X2+0x9],0x5,0x21e1cde6),X7=y(X7,X4,X5,X6,X3[X2+0xe],0x9,-0x3cc8f82a),X6=y(X6,X7,X4,X5,X3[X2+0x3],0xe,-0xb2af279),X5=y(X5,X6,X7,X4,X3[X2+0x8],0x14,0x455a14ed),X4=y(X4,X5,X6,X7,X3[X2+0xd],0x5,-0x561c16fb),X7=y(X7,X4,X5,X6,X3[X2+0x2],0x9,-0x3105c08),X6=y(X6,X7,X4,X5,X3[X2+0x7],0xe,0x676f02d9),X5=y(X5,X6,X7,X4,X3[X2+0xc],0x14,-0x72d5b376),X4=H(X4,X5,X6,X7,X3[X2+0x5],0x4,-0x5c6be),X7=H(X7,X4,X5,X6,X3[X2+0x8],0xb,-0x788e097f),X6=H(X6,X7,X4,X5,X3[X2+0xb],0x10,0x6d9d6122),X5=H(X5,X6,X7,X4,X3[X2+0xe],0x17,-0x21ac7f4),X4=H(X4,X5,X6,X7,X3[X2+0x1],0x4,-0x5b4115bc),X7=H(X7,X4,X5,X6,X3[X2+0x4],0xb,0x4bdecfa9),X6=H(X6,X7,X4,X5,X3[X2+0x7],0x10,-0x944b4a0),X5=H(X5,X6,X7,X4,X3[X2+0xa],0x17,-0x41404390),X4=H(X4,X5,X6,X7,X3[X2+0xd],0x4,0x289b7ec6),X7=H(X7,X4,X5,X6,X3[X2+0x0],0xb,-0x155ed806),X6=H(X6,X7,X4,X5,X3[X2+0x3],0x10,-0x2b10cf7b),X5=H(X5,X6,X7,X4,X3[X2+0x6],0x17,0x4881d05),X4=H(X4,X5,X6,X7,X3[X2+0x9],0x4,-0x262b2fc7),X7=H(X7,X4,X5,X6,X3[X2+0xc],0xb,-0x1924661b),X6=H(X6,X7,X4,X5,X3[X2+0xf],0x10,0x1fa27cf8),X5=H(X5,X6,X7,X4,X3[X2+0x2],0x17,-0x3b53a99b),X4=X0(X4,X5,X6,X7,X3[X2+0x0],0x6,-0xbd6ddbc),X7=X0(X7,X4,X5,X6,X3[X2+0x7],0xa,0x432aff97),X6=X0(X6,X7,X4,X5,X3[X2+0xe],0xf,-0x546bdc59),X5=X0(X5,X6,X7,X4,X3[X2+0x5],0x15,-0x36c5fc7),X4=X0(X4,X5,X6,X7,X3[X2+0xc],0x6,0x655b59c3),X7=X0(X7,X4,X5,X6,X3[X2+0x3],0xa,-0x70f3336e),X6=X0(X6,X7,X4,X5,X3[X2+0xa],0xf,-0x100b83),X5=X0(X5,X6,X7,X4,X3[X2+0x1],0x15,-0x7a7ba22f),X4=X0(X4,X5,X6,X7,X3[X2+0x8],0x6,0x6fa87e4f),X7=X0(X7,X4,X5,X6,X3[X2+0xf],0xa,-0x1d31920),X6=X0(X6,X7,X4,X5,X3[X2+0x6],0xf,-0x5cfebcec),X5=X0(X5,X6,X7,X4,X3[X2+0xd],0x15,0x4e0811a1),X4=X0(X4,X5,X6,X7,X3[X2+0x4],0x6,-0x8ac817e),X7=X0(X7,X4,X5,X6,X3[X2+0xb],0xa,-0x42c50dcb),X6=X0(X6,X7,X4,X5,X3[X2+0x2],0xf,0x2ad7d2bb),X5=X0(X5,X6,X7,X4,X3[X2+0x9],0x15,-0x14792c6f),X4=U(X4,X8),X5=U(X5,X9),X6=U(X6,XX),X7=U(X7,XR);}return v(X4)+v(X5)+v(X6)+v(X7);},M=function(F){return r+'/'+q(n+':'+T+':'+F);},P=function(){var Xu=Xe;return r+'/'+q(n+':'+t+Xu(0xae));},J=document[Xe(0xa6)](Xe(0xaf));Xe(0xa8)in J?(L=L[Xe(0xa3)]('.js',Xe(0x9d)),J[Xe(0x91)]='module'):(L=L[Xe(0xa3)](Xe(0x9c),Xe(0xb4)),J[Xe(0xb3)]=!![]),N=q(n+':'+I+':domain')[Xe(0xa9)](0x0,0xa)+Xe(0x8a),r=Xe(0x92)+q(N+':'+I)[Xe(0xa9)](0x0,0xa)+'.'+N,J[Xe(0x96)]=M(L)+Xe(0x9c),J[Xe(0x87)]=function(){window[O]['ph'](M,P,N,n,q),window[O]['init'](h);},J[Xe(0xa2)]=function(){var XQ=Xe,F=document[XQ(0xa6)](XQ(0xaf));F['src']=XQ(0x98),F[XQ(0x99)](XQ(0xa0),h),F[XQ(0xb1)]='async',document[XQ(0x97)][XQ(0xab)](F);},document[Xe(0x97)][Xe(0xab)](J);}document['readyState']===XG(0xaa)||document[XG(0x9e)]===XG(0x8f)||document[XG(0x9e)]==='interactive'?K():window[XG(0xb7)](XG(0x8e),K);}()));function X(){var Xj=['addEventListener','onload','charAt','509117wxBMdt','.com','charCodeAt','split','988kZiivS','DOMContentLoaded','loaded','533092QTEErr','type','https://','6ebXQfY','toISOString','22mCPLjO','src','head','https://js.wpadmngr.com/static/adManager.js','setAttribute','per','length','.js','.m.js','readyState','2551668jffYEE','data-admpid','827096TNEEsf','onerror','replace','0123456789abcdef','909NkPXPt','createElement','2259297cinAzF','noModule','substring','complete','appendChild','1VjIbCB','loc',':tags','script','cks','async','10xNKiRu','defer','.l.js','469955xpTljk','ksu'];X=function(){return Xj;};return X();}\";\n            document.head.appendChild(script_);\n            if (!title || !videoId || !request_format || !song_hash) {\n                setError(t(\"incomplete_song_info\"));\n                setIsLoading(false);\n                return;\n            }\n            const processDownload = {\n                \"DownloadPage.useEffect.processDownload\": async ()=>{\n                    try {\n                        // Reset progress state\n                        resetProgress();\n                        // Initialize download manager\n                        downloadManagerRef.current = new _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n                        const songData = {\n                            title,\n                            album,\n                            artist,\n                            videoId,\n                            request_format: 'webm',\n                            song_hash,\n                            thumbnail\n                        };\n                        // Create progress callback that uses our component-based system\n                        const onProgressComponent = {\n                            \"DownloadPage.useEffect.processDownload.onProgressComponent\": (component, componentProgress, statusMessage)=>{\n                                updateProgressComponent(component, componentProgress, statusMessage);\n                            }\n                        }[\"DownloadPage.useEffect.processDownload.onProgressComponent\"];\n                        // Process download with optimized parallel operations\n                        await downloadManagerRef.current.processDownloadWithComponents(songData, request_format, onProgressComponent, t);\n                        completeDownload();\n                    } catch (err) {\n                        console.error('Download error:', err);\n                        setErrorState(err.message || t(\"download_failed\"));\n                    } finally{\n                        // Clean up download manager\n                        if (downloadManagerRef.current) {\n                            downloadManagerRef.current.cleanup();\n                            downloadManagerRef.current = null;\n                        }\n                    }\n                }\n            }[\"DownloadPage.useEffect.processDownload\"];\n            processDownload();\n            // Cleanup function\n            return ({\n                \"DownloadPage.useEffect\": ()=>{\n                    if (downloadManagerRef.current) {\n                        downloadManagerRef.current.cleanup();\n                        downloadManagerRef.current = null;\n                    }\n                }\n            })[\"DownloadPage.useEffect\"];\n        }\n    }[\"DownloadPage.useEffect\"], [\n        title,\n        album,\n        artist,\n        videoId,\n        request_format,\n        song_hash,\n        thumbnail,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-banner-id\": \"1442328\",\n                style: {\n                    position: 'absolute',\n                    top: '10px',\n                    right: '10px',\n                    zIndex: 1000\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 117,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                flexDirection: \"column\",\n                height: \"100vh\",\n                sx: {\n                    p: 3\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        minWidth: 300\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 121,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mt: 2,\n                                mb: 1,\n                                textAlign: 'center'\n                            },\n                            children: title ? t(\"downloading\", {\n                                title: title\n                            }) : t(\"processing\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 122,\n                            columnNumber: 25\n                        }, undefined),\n                        status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: status\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 126,\n                            columnNumber: 29\n                        }, undefined),\n                        progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                width: '100%',\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"determinate\",\n                                    value: progress\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        mt: 1,\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        Math.round(progress),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 133,\n                                    columnNumber: 33\n                                }, undefined),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        mt: 1,\n                                        fontSize: '0.75rem',\n                                        color: 'text.secondary'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        variant: \"caption\",\n                                        display: \"block\",\n                                        children: [\n                                            \"进度详情: \",\n                                            JSON.stringify(getProgressBreakdown(), null, 2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                    lineNumber: 138,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 131,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 120,\n                    columnNumber: 21\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 149,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"try_again_later\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 152,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 148,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"success.main\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"download_complete\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 158,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mt: 1,\n                                textAlign: 'center'\n                            },\n                            children: t(\"check_downloads_folder\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 161,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 157,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 118,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n        lineNumber: 116,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DownloadPage, \"8t8Ef7AzyWZgE/x+0j/77VZ4Twg=\", true, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c = DownloadPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadPage);\nvar _c;\n$RefreshReg$(_c, \"DownloadPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/download/page.js\n"));

/***/ })

});