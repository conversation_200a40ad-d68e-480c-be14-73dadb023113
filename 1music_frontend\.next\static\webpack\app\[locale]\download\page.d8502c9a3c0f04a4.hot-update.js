"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\n\nclass DownloadManager {\n    /**\n     * Update specific progress component\n     * @param {string} component - Progress component name\n     * @param {number} progress - Progress value for this component\n     */ _updateProgressComponent(component, progress) {\n        if (this.progressComponentCallback) {\n            this.progressComponentCallback(component, progress);\n        }\n    }\n    /**\n     * Process download with component-based progress tracking\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgressComponent - Component progress callback\n     * @param {Function} t - Translation function\n     */ async processDownloadWithComponents(songData, requestFormat, onProgressComponent, t) {\n        this.abortController = new AbortController();\n        this.progressComponentCallback = onProgressComponent;\n        try {\n            // Reset progress state\n            this.progressState = {\n                urlFetch: 0,\n                ffmpegLoad: 0,\n                audioDownload: 0,\n                transcoding: 0\n            };\n            this._updateProgressComponent('urlFetch', 0);\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 0-20% of transcoding component)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = progress * 20; // 0-20%\n                this._updateProgressComponent('transcoding', transcodingProgress);\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : this._skipFFmpegLoad();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgressComponent('transcoding', 20);\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            // Start transcoding\n            this._updateProgressComponent('transcoding', 0);\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            // Transcoding complete\n            this._updateProgressComponent('transcoding', 20, t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            this._updateProgressComponent('transcoding', 20, t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress(t) {\n        this._updateProgressComponent('ffmpegLoad', 0, t(\"loading_transcoder\"));\n        await this.transcoder.load();\n        this._updateProgressComponent('ffmpegLoad', 10, t(\"transcoder_ready\"));\n        return true;\n    }\n    /**\n     * Skip FFmpeg loading for webm format\n     */ async _skipFFmpegLoad() {\n        this._updateProgressComponent('ffmpegLoad', 10, \"FFmpeg not needed for webm\");\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts (0-20% range)\n            const progressStep = Math.min(20, (retries + 1) * (20 / maxRetries));\n            this._updateProgressComponent('urlFetch', progressStep, t(\"fetching_audio\"));\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        this._updateProgressComponent('urlFetch', 20, t(\"download_url_ready\"));\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with progress tracking using axios\n     */ async _fetchAudioWithProgress(url, t) {\n        try {\n            this._updateProgressComponent('audioDownload', 0, t(\"downloading_files\"));\n            const response = await (0,axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                method: 'GET',\n                url: url,\n                responseType: 'blob',\n                signal: this.abortController.signal,\n                onDownloadProgress: (progressEvent)=>{\n                    if (progressEvent.lengthComputable) {\n                        // Map download progress to 0-50% range for audioDownload component\n                        const downloadPercent = progressEvent.loaded / progressEvent.total * 100;\n                        const mappedProgress = downloadPercent / 100 * 50; // 0-50%\n                        this._updateProgressComponent('audioDownload', mappedProgress, t(\"downloading_files\"));\n                    } else {\n                        // If we can't track progress, show incremental updates\n                        const currentTime = Date.now();\n                        if (!this.downloadStartTime) {\n                            this.downloadStartTime = currentTime;\n                        }\n                        const elapsed = (currentTime - this.downloadStartTime) / 1000;\n                        const estimatedProgress = Math.min(45, elapsed * 2); // Slow increment up to 45%\n                        this._updateProgressComponent('audioDownload', estimatedProgress, t(\"downloading_files\"));\n                    }\n                }\n            });\n            // Download completed\n            this._updateProgressComponent('audioDownload', 50, t(\"download_complete_preparing\"));\n            return response.data;\n        } catch (error) {\n            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n    }\n    /**\n     * Fetch audio file with error handling (fallback method)\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.progressComponentCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});