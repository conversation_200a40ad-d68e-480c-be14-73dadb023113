"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(app-pages-browser)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/utils/downloadManager */ \"(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DownloadPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"Download\");\n    const title = searchParams.get('title');\n    const album = searchParams.get('album');\n    const artist = searchParams.get('artist');\n    const videoId = searchParams.get('videoId');\n    const request_format = searchParams.get('request_format');\n    const song_hash = searchParams.get('song_hash');\n    const thumbnail = searchParams.get('thumbnail');\n    // Simple state management for parallel progress\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Progress state for parallel operations\n    const progressState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        urlFetch: 0,\n        ffmpegLoad: 0,\n        audioDownload: 0,\n        transcoding: 0 // 转码进度 (0-20%)\n    });\n    const downloadManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Calculate total progress from all components\n    const calculateTotalProgress = ()=>{\n        const { urlFetch, ffmpegLoad, audioDownload, transcoding } = progressState.current;\n        return Math.min(100, urlFetch + ffmpegLoad + audioDownload + transcoding);\n    };\n    // Update specific progress component\n    const updateProgressComponent = (component, componentProgress)=>{\n        progressState.current[component] = componentProgress;\n        const totalProgress = calculateTotalProgress();\n        setProgress(totalProgress);\n        console.log(\"Progress Update - \".concat(component, \": \").concat(componentProgress, \"%, Total: \").concat(totalProgress, \"%\"));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DownloadPage.useEffect\": ()=>{\n            const script = document.createElement(\"script\");\n            script.type = \"text/javascript\";\n            script.src = \"//plantationexhaust.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js\";\n            document.head.appendChild(script);\n            const script_1 = document.createElement(\"script\");\n            script_1.type = \"text/javascript\";\n            script_1.src = \"//pl26003516.effectiveratecpm.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js\";\n            document.body.appendChild(script_1);\n            if (!title || !videoId || !request_format || !song_hash) {\n                setError(t(\"incomplete_song_info\"));\n                setIsLoading(false);\n                return;\n            }\n            const processDownload = {\n                \"DownloadPage.useEffect.processDownload\": async ()=>{\n                    try {\n                        // Reset progress state\n                        progressState.current = {\n                            urlFetch: 0,\n                            ffmpegLoad: 0,\n                            audioDownload: 0,\n                            transcoding: 0\n                        };\n                        setProgress(0);\n                        setError(null);\n                        // Initialize download manager\n                        downloadManagerRef.current = new _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n                        const songData = {\n                            title,\n                            album,\n                            artist,\n                            videoId,\n                            request_format: 'webm',\n                            song_hash,\n                            thumbnail\n                        };\n                        // Process download with component-based progress\n                        await downloadManagerRef.current.processDownloadWithComponents(songData, request_format, updateProgressComponent, t);\n                        setIsLoading(false);\n                        setStatus(t(\"download_complete\"));\n                    } catch (err) {\n                        console.error('Download error:', err);\n                        setError(err.message || t(\"download_failed\"));\n                        setIsLoading(false);\n                    } finally{\n                        // Clean up download manager\n                        if (downloadManagerRef.current) {\n                            downloadManagerRef.current.cleanup();\n                            downloadManagerRef.current = null;\n                        }\n                    }\n                }\n            }[\"DownloadPage.useEffect.processDownload\"];\n            processDownload();\n            // Cleanup function\n            return ({\n                \"DownloadPage.useEffect\": ()=>{\n                    if (downloadManagerRef.current) {\n                        downloadManagerRef.current.cleanup();\n                        downloadManagerRef.current = null;\n                    }\n                }\n            })[\"DownloadPage.useEffect\"];\n        }\n    }[\"DownloadPage.useEffect\"], [\n        title,\n        album,\n        artist,\n        videoId,\n        request_format,\n        song_hash,\n        thumbnail,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-banner-id\": \"1442328\",\n                style: {\n                    position: 'absolute',\n                    top: '10px',\n                    right: '10px',\n                    zIndex: 1000\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 129,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                flexDirection: \"column\",\n                height: \"100vh\",\n                sx: {\n                    p: 3\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    sx: {\n                        minWidth: 300\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 133,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mt: 2,\n                                mb: 3,\n                                textAlign: 'center'\n                            },\n                            children: title ? t(\"downloading\", {\n                                title: title\n                            }) : t(\"processing\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 134,\n                            columnNumber: 25\n                        }, undefined),\n                        progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                width: '100%',\n                                mt: 2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                variant: \"determinate\",\n                                value: progress\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                                lineNumber: 139,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 138,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 132,\n                    columnNumber: 21\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2,\n                                textAlign: 'center'\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 145,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"try_again_later\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 148,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 144,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"success.main\",\n                            sx: {\n                                textAlign: 'center'\n                            },\n                            children: t(\"download_complete\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 154,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            sx: {\n                                mt: 1,\n                                textAlign: 'center'\n                            },\n                            children: t(\"check_downloads_folder\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 157,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 153,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n        lineNumber: 128,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DownloadPage, \"ttHDDHzDTi3+1oWVB+Yd9IBeV24=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c = DownloadPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadPage);\nvar _c;\n$RefreshReg$(_c, \"DownloadPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/download/page.js\n"));

/***/ })

});