"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/audioTranscoder.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"(app-pages-browser)/./node_modules/@ffmpeg/ffmpeg/dist/esm/index.js\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ffmpeg/util */ \"(app-pages-browser)/./node_modules/@ffmpeg/util/dist/esm/index.js\");\n/* harmony import */ var _imageProcessor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./imageProcessor */ \"(app-pages-browser)/./src/app/[locale]/utils/imageProcessor.js\");\n\n\n\nclass AudioTranscoder {\n    async load() {\n        // Return existing promise if already loading\n        if (this.loadPromise) {\n            return this.loadPromise;\n        }\n        // Return immediately if already loaded\n        if (this.isLoaded) {\n            return Promise.resolve();\n        }\n        // Create and cache the load promise\n        this.loadPromise = this._loadFFmpeg();\n        try {\n            await this.loadPromise;\n            this.isLoaded = true;\n        } catch (error) {\n            // Reset promise on failure so it can be retried\n            this.loadPromise = null;\n            throw error;\n        }\n    }\n    async _loadFFmpeg() {\n        this.ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_0__.FFmpeg();\n        // Load FFmpeg with CDN URLs in parallel\n        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';\n        const [coreURL, wasmURL] = await Promise.all([\n            (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.js\"), 'text/javascript'),\n            (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.wasm\"), 'application/wasm')\n        ]);\n        await this.ffmpeg.load({\n            coreURL,\n            wasmURL\n        });\n    }\n    async cropImageToSquare(imageFile) {\n        // Use Canvas API for image processing instead of ffmpeg\n        return await _imageProcessor__WEBPACK_IMPORTED_MODULE_2__.ImageProcessor.cropToSquareJPEG(imageFile, 500, 0.9);\n    }\n    async transcodeAudio(audioFile, coverImageFile, format) {\n        let metadata = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n        // Ensure FFmpeg is loaded\n        await this.load();\n        const inputAudioName = 'input_audio';\n        const inputImageName = 'input_image.jpg';\n        const outputName = \"output.\".concat(format);\n        try {\n            // Prepare file operations in parallel\n            const fileOperations = [\n                this.ffmpeg.writeFile(inputAudioName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(audioFile))\n            ];\n            // Process cover image if provided (in parallel with audio file writing)\n            let processedImageBlob = null;\n            if (coverImageFile) {\n                // Start image processing while audio file is being written\n                const imageProcessPromise = this.cropImageToSquare(coverImageFile);\n                // Wait for both audio file writing and image processing\n                const [, processedImage] = await Promise.all([\n                    fileOperations[0],\n                    imageProcessPromise\n                ]);\n                processedImageBlob = processedImage;\n                // Write processed image to FFmpeg\n                await this.ffmpeg.writeFile(inputImageName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_1__.fetchFile)(processedImageBlob));\n            } else {\n                // Just wait for audio file writing\n                await fileOperations[0];\n            }\n            // Build FFmpeg command based on format\n            let command = [\n                '-i',\n                inputAudioName\n            ];\n            if (coverImageFile) {\n                command.push('-i', inputImageName);\n                command.push('-map', '0:a', '-map', '1');\n            }\n            if (format === 'mp3') {\n                command.push('-codec:a', 'libmp3lame', '-b:a', '320k');\n                if (coverImageFile) {\n                    command.push('-c:v', 'mjpeg', '-id3v2_version', '3', '-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-metadata:s:v', 'handler_name=Album cover');\n                }\n            } else if (format === 'flac') {\n                command.push('-codec:a', 'flac');\n                if (coverImageFile) {\n                    command.push('-metadata:s:v', 'title=Album cover', '-metadata:s:v', 'comment=Cover (front)', '-disposition:v', 'attached_pic');\n                }\n            } else {\n                throw new Error(\"Unsupported format: \".concat(format));\n            }\n            // Add metadata\n            if (metadata.title) command.push('-metadata', \"title=\".concat(metadata.title));\n            if (metadata.artist) command.push('-metadata', \"artist=\".concat(metadata.artist));\n            if (metadata.album) command.push('-metadata', \"album=\".concat(metadata.album));\n            // Add custom metadata\n            command.push('-metadata', 'PURL=1music.cc', '-metadata', 'COMMENT=1music.cc');\n            command.push('-y', outputName);\n            // Execute transcoding\n            await this.ffmpeg.exec(command);\n            // Read output file\n            const data = await this.ffmpeg.readFile(outputName);\n            // Clean up\n            await this.ffmpeg.deleteFile(inputAudioName);\n            if (coverImageFile) {\n                await this.ffmpeg.deleteFile(inputImageName);\n            }\n            await this.ffmpeg.deleteFile(outputName);\n            return new Uint8Array(data);\n        } catch (error) {\n            // Clean up on error\n            try {\n                await this.ffmpeg.deleteFile(inputAudioName);\n                if (coverImageFile) {\n                    await this.ffmpeg.deleteFile(inputImageName);\n                }\n                await this.ffmpeg.deleteFile(outputName);\n            } catch (cleanupError) {\n            // Ignore cleanup errors\n            }\n            throw error;\n        }\n    }\n    setProgressCallback(callback) {\n        if (this.ffmpeg) {\n            this.ffmpeg.on('progress', callback);\n        }\n    }\n    terminate() {\n        if (this.ffmpeg) {\n            try {\n                this.ffmpeg.terminate();\n            } catch (error) {\n                console.warn('Error terminating FFmpeg:', error);\n            }\n        }\n        // Reset all state\n        this.ffmpeg = null;\n        this.isLoaded = false;\n        this.loadPromise = null;\n    }\n    constructor(){\n        this.ffmpeg = null;\n        this.isLoaded = false;\n        this.loadPromise = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AudioTranscoder);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vdXRpbHMvYXVkaW9UcmFuc2NvZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0M7QUFDWTtBQUNGO0FBRWxELE1BQU1JO0lBT0YsTUFBTUMsT0FBTztRQUNULDZDQUE2QztRQUM3QyxJQUFJLElBQUksQ0FBQ0MsV0FBVyxFQUFFO1lBQ2xCLE9BQU8sSUFBSSxDQUFDQSxXQUFXO1FBQzNCO1FBRUEsdUNBQXVDO1FBQ3ZDLElBQUksSUFBSSxDQUFDQyxRQUFRLEVBQUU7WUFDZixPQUFPQyxRQUFRQyxPQUFPO1FBQzFCO1FBRUEsb0NBQW9DO1FBQ3BDLElBQUksQ0FBQ0gsV0FBVyxHQUFHLElBQUksQ0FBQ0ksV0FBVztRQUVuQyxJQUFJO1lBQ0EsTUFBTSxJQUFJLENBQUNKLFdBQVc7WUFDdEIsSUFBSSxDQUFDQyxRQUFRLEdBQUc7UUFDcEIsRUFBRSxPQUFPSSxPQUFPO1lBQ1osZ0RBQWdEO1lBQ2hELElBQUksQ0FBQ0wsV0FBVyxHQUFHO1lBQ25CLE1BQU1LO1FBQ1Y7SUFDSjtJQUVBLE1BQU1ELGNBQWM7UUFDaEIsSUFBSSxDQUFDRSxNQUFNLEdBQUcsSUFBSVosa0RBQU1BO1FBRXhCLHdDQUF3QztRQUN4QyxNQUFNYSxVQUFVO1FBRWhCLE1BQU0sQ0FBQ0MsU0FBU0MsUUFBUSxHQUFHLE1BQU1QLFFBQVFRLEdBQUcsQ0FBQztZQUN6Q2QsdURBQVNBLENBQUMsR0FBVyxPQUFSVyxTQUFRLG9CQUFrQjtZQUN2Q1gsdURBQVNBLENBQUMsR0FBVyxPQUFSVyxTQUFRLHNCQUFvQjtTQUM1QztRQUVELE1BQU0sSUFBSSxDQUFDRCxNQUFNLENBQUNQLElBQUksQ0FBQztZQUNuQlM7WUFDQUM7UUFDSjtJQUNKO0lBRUEsTUFBTUUsa0JBQWtCQyxTQUFTLEVBQUU7UUFDL0Isd0RBQXdEO1FBQ3hELE9BQU8sTUFBTWYsMkRBQWNBLENBQUNnQixnQkFBZ0IsQ0FBQ0QsV0FBVyxLQUFLO0lBQ2pFO0lBRUEsTUFBTUUsZUFBZUMsU0FBUyxFQUFFQyxjQUFjLEVBQUVDLE1BQU0sRUFBaUI7WUFBZkMsV0FBQUEsaUVBQVcsQ0FBQztRQUNoRSwwQkFBMEI7UUFDMUIsTUFBTSxJQUFJLENBQUNuQixJQUFJO1FBRWYsTUFBTW9CLGlCQUFpQjtRQUN2QixNQUFNQyxpQkFBaUI7UUFDdkIsTUFBTUMsYUFBYSxVQUFpQixPQUFQSjtRQUU3QixJQUFJO1lBQ0Esc0NBQXNDO1lBQ3RDLE1BQU1LLGlCQUFpQjtnQkFDbkIsSUFBSSxDQUFDaEIsTUFBTSxDQUFDaUIsU0FBUyxDQUFDSixnQkFBZ0IsTUFBTXhCLHVEQUFTQSxDQUFDb0I7YUFDekQ7WUFFRCx3RUFBd0U7WUFDeEUsSUFBSVMscUJBQXFCO1lBQ3pCLElBQUlSLGdCQUFnQjtnQkFDaEIsMkRBQTJEO2dCQUMzRCxNQUFNUyxzQkFBc0IsSUFBSSxDQUFDZCxpQkFBaUIsQ0FBQ0s7Z0JBRW5ELHdEQUF3RDtnQkFDeEQsTUFBTSxHQUFHVSxlQUFlLEdBQUcsTUFBTXhCLFFBQVFRLEdBQUcsQ0FBQztvQkFDekNZLGNBQWMsQ0FBQyxFQUFFO29CQUNqQkc7aUJBQ0g7Z0JBRURELHFCQUFxQkU7Z0JBQ3JCLGtDQUFrQztnQkFDbEMsTUFBTSxJQUFJLENBQUNwQixNQUFNLENBQUNpQixTQUFTLENBQUNILGdCQUFnQixNQUFNekIsdURBQVNBLENBQUM2QjtZQUNoRSxPQUFPO2dCQUNILG1DQUFtQztnQkFDbkMsTUFBTUYsY0FBYyxDQUFDLEVBQUU7WUFDM0I7WUFFQSx1Q0FBdUM7WUFDdkMsSUFBSUssVUFBVTtnQkFBQztnQkFBTVI7YUFBZTtZQUVwQyxJQUFJSCxnQkFBZ0I7Z0JBQ2hCVyxRQUFRQyxJQUFJLENBQUMsTUFBTVI7Z0JBQ25CTyxRQUFRQyxJQUFJLENBQUMsUUFBUSxPQUFPLFFBQVE7WUFDeEM7WUFFQSxJQUFJWCxXQUFXLE9BQU87Z0JBQ2xCVSxRQUFRQyxJQUFJLENBQ1IsWUFBWSxjQUNaLFFBQVE7Z0JBRVosSUFBSVosZ0JBQWdCO29CQUNoQlcsUUFBUUMsSUFBSSxDQUNSLFFBQVEsU0FDUixrQkFBa0IsS0FDbEIsaUJBQWlCLHFCQUNqQixpQkFBaUIseUJBQ2pCLGlCQUFpQjtnQkFFekI7WUFDSixPQUFPLElBQUlYLFdBQVcsUUFBUTtnQkFDMUJVLFFBQVFDLElBQUksQ0FBQyxZQUFZO2dCQUN6QixJQUFJWixnQkFBZ0I7b0JBQ2hCVyxRQUFRQyxJQUFJLENBQ1IsaUJBQWlCLHFCQUNqQixpQkFBaUIseUJBQ2pCLGtCQUFrQjtnQkFFMUI7WUFDSixPQUFPO2dCQUNILE1BQU0sSUFBSUMsTUFBTSx1QkFBOEIsT0FBUFo7WUFDM0M7WUFFQSxlQUFlO1lBQ2YsSUFBSUMsU0FBU1ksS0FBSyxFQUFFSCxRQUFRQyxJQUFJLENBQUMsYUFBYSxTQUF3QixPQUFmVixTQUFTWSxLQUFLO1lBQ3JFLElBQUlaLFNBQVNhLE1BQU0sRUFBRUosUUFBUUMsSUFBSSxDQUFDLGFBQWEsVUFBMEIsT0FBaEJWLFNBQVNhLE1BQU07WUFDeEUsSUFBSWIsU0FBU2MsS0FBSyxFQUFFTCxRQUFRQyxJQUFJLENBQUMsYUFBYSxTQUF3QixPQUFmVixTQUFTYyxLQUFLO1lBRXJFLHNCQUFzQjtZQUN0QkwsUUFBUUMsSUFBSSxDQUNSLGFBQWEsa0JBQ2IsYUFBYTtZQUdqQkQsUUFBUUMsSUFBSSxDQUFDLE1BQU1QO1lBRW5CLHNCQUFzQjtZQUN0QixNQUFNLElBQUksQ0FBQ2YsTUFBTSxDQUFDMkIsSUFBSSxDQUFDTjtZQUV2QixtQkFBbUI7WUFDbkIsTUFBTU8sT0FBTyxNQUFNLElBQUksQ0FBQzVCLE1BQU0sQ0FBQzZCLFFBQVEsQ0FBQ2Q7WUFFeEMsV0FBVztZQUNYLE1BQU0sSUFBSSxDQUFDZixNQUFNLENBQUM4QixVQUFVLENBQUNqQjtZQUM3QixJQUFJSCxnQkFBZ0I7Z0JBQ2hCLE1BQU0sSUFBSSxDQUFDVixNQUFNLENBQUM4QixVQUFVLENBQUNoQjtZQUNqQztZQUNBLE1BQU0sSUFBSSxDQUFDZCxNQUFNLENBQUM4QixVQUFVLENBQUNmO1lBRTdCLE9BQU8sSUFBSWdCLFdBQVdIO1FBRTFCLEVBQUUsT0FBTzdCLE9BQU87WUFDWixvQkFBb0I7WUFDcEIsSUFBSTtnQkFDQSxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDOEIsVUFBVSxDQUFDakI7Z0JBQzdCLElBQUlILGdCQUFnQjtvQkFDaEIsTUFBTSxJQUFJLENBQUNWLE1BQU0sQ0FBQzhCLFVBQVUsQ0FBQ2hCO2dCQUNqQztnQkFDQSxNQUFNLElBQUksQ0FBQ2QsTUFBTSxDQUFDOEIsVUFBVSxDQUFDZjtZQUNqQyxFQUFFLE9BQU9pQixjQUFjO1lBQ25CLHdCQUF3QjtZQUM1QjtZQUNBLE1BQU1qQztRQUNWO0lBQ0o7SUFFQWtDLG9CQUFvQkMsUUFBUSxFQUFFO1FBQzFCLElBQUksSUFBSSxDQUFDbEMsTUFBTSxFQUFFO1lBQ2IsSUFBSSxDQUFDQSxNQUFNLENBQUNtQyxFQUFFLENBQUMsWUFBWUQ7UUFDL0I7SUFDSjtJQUVBRSxZQUFZO1FBQ1IsSUFBSSxJQUFJLENBQUNwQyxNQUFNLEVBQUU7WUFDYixJQUFJO2dCQUNBLElBQUksQ0FBQ0EsTUFBTSxDQUFDb0MsU0FBUztZQUN6QixFQUFFLE9BQU9yQyxPQUFPO2dCQUNac0MsUUFBUUMsSUFBSSxDQUFDLDZCQUE2QnZDO1lBQzlDO1FBQ0o7UUFFQSxrQkFBa0I7UUFDbEIsSUFBSSxDQUFDQyxNQUFNLEdBQUc7UUFDZCxJQUFJLENBQUNMLFFBQVEsR0FBRztRQUNoQixJQUFJLENBQUNELFdBQVcsR0FBRztJQUN2QjtJQXZMQTZDLGFBQWM7UUFDVixJQUFJLENBQUN2QyxNQUFNLEdBQUc7UUFDZCxJQUFJLENBQUNMLFFBQVEsR0FBRztRQUNoQixJQUFJLENBQUNELFdBQVcsR0FBRztJQUN2QjtBQW9MSjtBQUVBLGlFQUFlRixlQUFlQSxFQUFDIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcd2ViXFwxbXVzaWNcXDFtdXNpY19mcm9udGVuZFxcc3JjXFxhcHBcXFtsb2NhbGVdXFx1dGlsc1xcYXVkaW9UcmFuc2NvZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZGbXBlZyB9IGZyb20gJ0BmZm1wZWcvZmZtcGVnJztcbmltcG9ydCB7IGZldGNoRmlsZSwgdG9CbG9iVVJMIH0gZnJvbSAnQGZmbXBlZy91dGlsJztcbmltcG9ydCB7IEltYWdlUHJvY2Vzc29yIH0gZnJvbSAnLi9pbWFnZVByb2Nlc3Nvcic7XG5cbmNsYXNzIEF1ZGlvVHJhbnNjb2RlciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMuZmZtcGVnID0gbnVsbDtcbiAgICAgICAgdGhpcy5pc0xvYWRlZCA9IGZhbHNlO1xuICAgICAgICB0aGlzLmxvYWRQcm9taXNlID0gbnVsbDtcbiAgICB9XG5cbiAgICBhc3luYyBsb2FkKCkge1xuICAgICAgICAvLyBSZXR1cm4gZXhpc3RpbmcgcHJvbWlzZSBpZiBhbHJlYWR5IGxvYWRpbmdcbiAgICAgICAgaWYgKHRoaXMubG9hZFByb21pc2UpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmxvYWRQcm9taXNlO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gUmV0dXJuIGltbWVkaWF0ZWx5IGlmIGFscmVhZHkgbG9hZGVkXG4gICAgICAgIGlmICh0aGlzLmlzTG9hZGVkKSB7XG4gICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBDcmVhdGUgYW5kIGNhY2hlIHRoZSBsb2FkIHByb21pc2VcbiAgICAgICAgdGhpcy5sb2FkUHJvbWlzZSA9IHRoaXMuX2xvYWRGRm1wZWcoKTtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgYXdhaXQgdGhpcy5sb2FkUHJvbWlzZTtcbiAgICAgICAgICAgIHRoaXMuaXNMb2FkZWQgPSB0cnVlO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgLy8gUmVzZXQgcHJvbWlzZSBvbiBmYWlsdXJlIHNvIGl0IGNhbiBiZSByZXRyaWVkXG4gICAgICAgICAgICB0aGlzLmxvYWRQcm9taXNlID0gbnVsbDtcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgYXN5bmMgX2xvYWRGRm1wZWcoKSB7XG4gICAgICAgIHRoaXMuZmZtcGVnID0gbmV3IEZGbXBlZygpO1xuXG4gICAgICAgIC8vIExvYWQgRkZtcGVnIHdpdGggQ0ROIFVSTHMgaW4gcGFyYWxsZWxcbiAgICAgICAgY29uc3QgYmFzZVVSTCA9ICdodHRwczovL3VucGtnLmNvbS9AZmZtcGVnL2NvcmVAMC4xMi42L2Rpc3QvdW1kJztcblxuICAgICAgICBjb25zdCBbY29yZVVSTCwgd2FzbVVSTF0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgICAgICB0b0Jsb2JVUkwoYCR7YmFzZVVSTH0vZmZtcGVnLWNvcmUuanNgLCAndGV4dC9qYXZhc2NyaXB0JyksXG4gICAgICAgICAgICB0b0Jsb2JVUkwoYCR7YmFzZVVSTH0vZmZtcGVnLWNvcmUud2FzbWAsICdhcHBsaWNhdGlvbi93YXNtJylcbiAgICAgICAgXSk7XG5cbiAgICAgICAgYXdhaXQgdGhpcy5mZm1wZWcubG9hZCh7XG4gICAgICAgICAgICBjb3JlVVJMLFxuICAgICAgICAgICAgd2FzbVVSTCxcbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgYXN5bmMgY3JvcEltYWdlVG9TcXVhcmUoaW1hZ2VGaWxlKSB7XG4gICAgICAgIC8vIFVzZSBDYW52YXMgQVBJIGZvciBpbWFnZSBwcm9jZXNzaW5nIGluc3RlYWQgb2YgZmZtcGVnXG4gICAgICAgIHJldHVybiBhd2FpdCBJbWFnZVByb2Nlc3Nvci5jcm9wVG9TcXVhcmVKUEVHKGltYWdlRmlsZSwgNTAwLCAwLjkpO1xuICAgIH1cblxuICAgIGFzeW5jIHRyYW5zY29kZUF1ZGlvKGF1ZGlvRmlsZSwgY292ZXJJbWFnZUZpbGUsIGZvcm1hdCwgbWV0YWRhdGEgPSB7fSkge1xuICAgICAgICAvLyBFbnN1cmUgRkZtcGVnIGlzIGxvYWRlZFxuICAgICAgICBhd2FpdCB0aGlzLmxvYWQoKTtcblxuICAgICAgICBjb25zdCBpbnB1dEF1ZGlvTmFtZSA9ICdpbnB1dF9hdWRpbyc7XG4gICAgICAgIGNvbnN0IGlucHV0SW1hZ2VOYW1lID0gJ2lucHV0X2ltYWdlLmpwZyc7XG4gICAgICAgIGNvbnN0IG91dHB1dE5hbWUgPSBgb3V0cHV0LiR7Zm9ybWF0fWA7XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIFByZXBhcmUgZmlsZSBvcGVyYXRpb25zIGluIHBhcmFsbGVsXG4gICAgICAgICAgICBjb25zdCBmaWxlT3BlcmF0aW9ucyA9IFtcbiAgICAgICAgICAgICAgICB0aGlzLmZmbXBlZy53cml0ZUZpbGUoaW5wdXRBdWRpb05hbWUsIGF3YWl0IGZldGNoRmlsZShhdWRpb0ZpbGUpKVxuICAgICAgICAgICAgXTtcblxuICAgICAgICAgICAgLy8gUHJvY2VzcyBjb3ZlciBpbWFnZSBpZiBwcm92aWRlZCAoaW4gcGFyYWxsZWwgd2l0aCBhdWRpbyBmaWxlIHdyaXRpbmcpXG4gICAgICAgICAgICBsZXQgcHJvY2Vzc2VkSW1hZ2VCbG9iID0gbnVsbDtcbiAgICAgICAgICAgIGlmIChjb3ZlckltYWdlRmlsZSkge1xuICAgICAgICAgICAgICAgIC8vIFN0YXJ0IGltYWdlIHByb2Nlc3Npbmcgd2hpbGUgYXVkaW8gZmlsZSBpcyBiZWluZyB3cml0dGVuXG4gICAgICAgICAgICAgICAgY29uc3QgaW1hZ2VQcm9jZXNzUHJvbWlzZSA9IHRoaXMuY3JvcEltYWdlVG9TcXVhcmUoY292ZXJJbWFnZUZpbGUpO1xuXG4gICAgICAgICAgICAgICAgLy8gV2FpdCBmb3IgYm90aCBhdWRpbyBmaWxlIHdyaXRpbmcgYW5kIGltYWdlIHByb2Nlc3NpbmdcbiAgICAgICAgICAgICAgICBjb25zdCBbLCBwcm9jZXNzZWRJbWFnZV0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgICAgICAgICAgICAgIGZpbGVPcGVyYXRpb25zWzBdLCAvLyBBdWRpbyBmaWxlIHdyaXRpbmdcbiAgICAgICAgICAgICAgICAgICAgaW1hZ2VQcm9jZXNzUHJvbWlzZVxuICAgICAgICAgICAgICAgIF0pO1xuXG4gICAgICAgICAgICAgICAgcHJvY2Vzc2VkSW1hZ2VCbG9iID0gcHJvY2Vzc2VkSW1hZ2U7XG4gICAgICAgICAgICAgICAgLy8gV3JpdGUgcHJvY2Vzc2VkIGltYWdlIHRvIEZGbXBlZ1xuICAgICAgICAgICAgICAgIGF3YWl0IHRoaXMuZmZtcGVnLndyaXRlRmlsZShpbnB1dEltYWdlTmFtZSwgYXdhaXQgZmV0Y2hGaWxlKHByb2Nlc3NlZEltYWdlQmxvYikpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAvLyBKdXN0IHdhaXQgZm9yIGF1ZGlvIGZpbGUgd3JpdGluZ1xuICAgICAgICAgICAgICAgIGF3YWl0IGZpbGVPcGVyYXRpb25zWzBdO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBCdWlsZCBGRm1wZWcgY29tbWFuZCBiYXNlZCBvbiBmb3JtYXRcbiAgICAgICAgICAgIGxldCBjb21tYW5kID0gWyctaScsIGlucHV0QXVkaW9OYW1lXTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgaWYgKGNvdmVySW1hZ2VGaWxlKSB7XG4gICAgICAgICAgICAgICAgY29tbWFuZC5wdXNoKCctaScsIGlucHV0SW1hZ2VOYW1lKTtcbiAgICAgICAgICAgICAgICBjb21tYW5kLnB1c2goJy1tYXAnLCAnMDphJywgJy1tYXAnLCAnMScpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoZm9ybWF0ID09PSAnbXAzJykge1xuICAgICAgICAgICAgICAgIGNvbW1hbmQucHVzaChcbiAgICAgICAgICAgICAgICAgICAgJy1jb2RlYzphJywgJ2xpYm1wM2xhbWUnLFxuICAgICAgICAgICAgICAgICAgICAnLWI6YScsICczMjBrJ1xuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgaWYgKGNvdmVySW1hZ2VGaWxlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbW1hbmQucHVzaChcbiAgICAgICAgICAgICAgICAgICAgICAgICctYzp2JywgJ21qcGVnJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICctaWQzdjJfdmVyc2lvbicsICczJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICctbWV0YWRhdGE6czp2JywgJ3RpdGxlPUFsYnVtIGNvdmVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICctbWV0YWRhdGE6czp2JywgJ2NvbW1lbnQ9Q292ZXIgKGZyb250KScsXG4gICAgICAgICAgICAgICAgICAgICAgICAnLW1ldGFkYXRhOnM6dicsICdoYW5kbGVyX25hbWU9QWxidW0gY292ZXInXG4gICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIGlmIChmb3JtYXQgPT09ICdmbGFjJykge1xuICAgICAgICAgICAgICAgIGNvbW1hbmQucHVzaCgnLWNvZGVjOmEnLCAnZmxhYycpO1xuICAgICAgICAgICAgICAgIGlmIChjb3ZlckltYWdlRmlsZSkge1xuICAgICAgICAgICAgICAgICAgICBjb21tYW5kLnB1c2goXG4gICAgICAgICAgICAgICAgICAgICAgICAnLW1ldGFkYXRhOnM6dicsICd0aXRsZT1BbGJ1bSBjb3ZlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICAnLW1ldGFkYXRhOnM6dicsICdjb21tZW50PUNvdmVyIChmcm9udCknLFxuICAgICAgICAgICAgICAgICAgICAgICAgJy1kaXNwb3NpdGlvbjp2JywgJ2F0dGFjaGVkX3BpYydcbiAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5zdXBwb3J0ZWQgZm9ybWF0OiAke2Zvcm1hdH1gKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8gQWRkIG1ldGFkYXRhXG4gICAgICAgICAgICBpZiAobWV0YWRhdGEudGl0bGUpIGNvbW1hbmQucHVzaCgnLW1ldGFkYXRhJywgYHRpdGxlPSR7bWV0YWRhdGEudGl0bGV9YCk7XG4gICAgICAgICAgICBpZiAobWV0YWRhdGEuYXJ0aXN0KSBjb21tYW5kLnB1c2goJy1tZXRhZGF0YScsIGBhcnRpc3Q9JHttZXRhZGF0YS5hcnRpc3R9YCk7XG4gICAgICAgICAgICBpZiAobWV0YWRhdGEuYWxidW0pIGNvbW1hbmQucHVzaCgnLW1ldGFkYXRhJywgYGFsYnVtPSR7bWV0YWRhdGEuYWxidW19YCk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIEFkZCBjdXN0b20gbWV0YWRhdGFcbiAgICAgICAgICAgIGNvbW1hbmQucHVzaChcbiAgICAgICAgICAgICAgICAnLW1ldGFkYXRhJywgJ1BVUkw9MW11c2ljLmNjJyxcbiAgICAgICAgICAgICAgICAnLW1ldGFkYXRhJywgJ0NPTU1FTlQ9MW11c2ljLmNjJ1xuICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgY29tbWFuZC5wdXNoKCcteScsIG91dHB1dE5hbWUpO1xuXG4gICAgICAgICAgICAvLyBFeGVjdXRlIHRyYW5zY29kaW5nXG4gICAgICAgICAgICBhd2FpdCB0aGlzLmZmbXBlZy5leGVjKGNvbW1hbmQpO1xuXG4gICAgICAgICAgICAvLyBSZWFkIG91dHB1dCBmaWxlXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdGhpcy5mZm1wZWcucmVhZEZpbGUob3V0cHV0TmFtZSk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIENsZWFuIHVwXG4gICAgICAgICAgICBhd2FpdCB0aGlzLmZmbXBlZy5kZWxldGVGaWxlKGlucHV0QXVkaW9OYW1lKTtcbiAgICAgICAgICAgIGlmIChjb3ZlckltYWdlRmlsZSkge1xuICAgICAgICAgICAgICAgIGF3YWl0IHRoaXMuZmZtcGVnLmRlbGV0ZUZpbGUoaW5wdXRJbWFnZU5hbWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYXdhaXQgdGhpcy5mZm1wZWcuZGVsZXRlRmlsZShvdXRwdXROYW1lKTtcblxuICAgICAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGRhdGEpO1xuXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAvLyBDbGVhbiB1cCBvbiBlcnJvclxuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBhd2FpdCB0aGlzLmZmbXBlZy5kZWxldGVGaWxlKGlucHV0QXVkaW9OYW1lKTtcbiAgICAgICAgICAgICAgICBpZiAoY292ZXJJbWFnZUZpbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgdGhpcy5mZm1wZWcuZGVsZXRlRmlsZShpbnB1dEltYWdlTmFtZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGF3YWl0IHRoaXMuZmZtcGVnLmRlbGV0ZUZpbGUob3V0cHV0TmFtZSk7XG4gICAgICAgICAgICB9IGNhdGNoIChjbGVhbnVwRXJyb3IpIHtcbiAgICAgICAgICAgICAgICAvLyBJZ25vcmUgY2xlYW51cCBlcnJvcnNcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgc2V0UHJvZ3Jlc3NDYWxsYmFjayhjYWxsYmFjaykge1xuICAgICAgICBpZiAodGhpcy5mZm1wZWcpIHtcbiAgICAgICAgICAgIHRoaXMuZmZtcGVnLm9uKCdwcm9ncmVzcycsIGNhbGxiYWNrKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHRlcm1pbmF0ZSgpIHtcbiAgICAgICAgaWYgKHRoaXMuZmZtcGVnKSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIHRoaXMuZmZtcGVnLnRlcm1pbmF0ZSgpO1xuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ0Vycm9yIHRlcm1pbmF0aW5nIEZGbXBlZzonLCBlcnJvcik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBSZXNldCBhbGwgc3RhdGVcbiAgICAgICAgdGhpcy5mZm1wZWcgPSBudWxsO1xuICAgICAgICB0aGlzLmlzTG9hZGVkID0gZmFsc2U7XG4gICAgICAgIHRoaXMubG9hZFByb21pc2UgPSBudWxsO1xuICAgIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgQXVkaW9UcmFuc2NvZGVyO1xuIl0sIm5hbWVzIjpbIkZGbXBlZyIsImZldGNoRmlsZSIsInRvQmxvYlVSTCIsIkltYWdlUHJvY2Vzc29yIiwiQXVkaW9UcmFuc2NvZGVyIiwibG9hZCIsImxvYWRQcm9taXNlIiwiaXNMb2FkZWQiLCJQcm9taXNlIiwicmVzb2x2ZSIsIl9sb2FkRkZtcGVnIiwiZXJyb3IiLCJmZm1wZWciLCJiYXNlVVJMIiwiY29yZVVSTCIsIndhc21VUkwiLCJhbGwiLCJjcm9wSW1hZ2VUb1NxdWFyZSIsImltYWdlRmlsZSIsImNyb3BUb1NxdWFyZUpQRUciLCJ0cmFuc2NvZGVBdWRpbyIsImF1ZGlvRmlsZSIsImNvdmVySW1hZ2VGaWxlIiwiZm9ybWF0IiwibWV0YWRhdGEiLCJpbnB1dEF1ZGlvTmFtZSIsImlucHV0SW1hZ2VOYW1lIiwib3V0cHV0TmFtZSIsImZpbGVPcGVyYXRpb25zIiwid3JpdGVGaWxlIiwicHJvY2Vzc2VkSW1hZ2VCbG9iIiwiaW1hZ2VQcm9jZXNzUHJvbWlzZSIsInByb2Nlc3NlZEltYWdlIiwiY29tbWFuZCIsInB1c2giLCJFcnJvciIsInRpdGxlIiwiYXJ0aXN0IiwiYWxidW0iLCJleGVjIiwiZGF0YSIsInJlYWRGaWxlIiwiZGVsZXRlRmlsZSIsIlVpbnQ4QXJyYXkiLCJjbGVhbnVwRXJyb3IiLCJzZXRQcm9ncmVzc0NhbGxiYWNrIiwiY2FsbGJhY2siLCJvbiIsInRlcm1pbmF0ZSIsImNvbnNvbGUiLCJ3YXJuIiwiY29uc3RydWN0b3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\n"));

/***/ })

});