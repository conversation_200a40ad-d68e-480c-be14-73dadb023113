"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\n\nclass DownloadManager {\n    /**\n     * Update progress with specific percentage\n     * @param {number} progress - Progress percentage (0-100)\n     * @param {string} status - Status message\n     */ _updateProgress(progress, status) {\n        this.currentProgress = progress;\n        if (this.progressCallback) {\n            this.progressCallback(progress);\n        }\n        if (this.statusCallback) {\n            this.statusCallback(status);\n        }\n    }\n    /**\n     * Process download with optimized parallel operations\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgress - Progress callback\n     * @param {Function} onStatus - Status update callback\n     * @param {Function} t - Translation function\n     */ async processDownload(songData, requestFormat, onProgress, onStatus, t) {\n        this.abortController = new AbortController();\n        this.progressCallback = onProgress;\n        this.statusCallback = onStatus;\n        try {\n            this._updateProgress(0, t(\"preparing_download\"));\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 70-90% range)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = 70 + progress * 20; // 70-90%\n                this._updateProgress(Math.round(transcodingProgress), t(\"transcoding_audio\"));\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress(t) : Promise.resolve();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            this._updateProgress(5, t(\"fetching_audio\"));\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            this._updateProgress(20, t(\"download_url_ready\"));\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgress(90, t(\"preparing_download\"));\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                this._updateProgress(100, t(\"download_complete\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            this._updateProgress(30, t(\"transcoder_ready\"));\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            this._updateProgress(70, t(\"transcoding_audio\"));\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            this._updateProgress(90, t(\"preparing_download\"));\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            this._updateProgress(100, t(\"download_complete\"));\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress(t) {\n        this._updateProgress(10, t(\"loading_transcoder\"));\n        await this.transcoder.load();\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData, t) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts\n            const progressStep = Math.min(15, 5 + retries * 0.5);\n            this._updateProgress(progressStep, t(\"fetching_audio\"));\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with progress tracking using axios\n     */ async _fetchAudioWithProgress(url, t) {\n        try {\n            const response = await (0,axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                method: 'GET',\n                url: url,\n                responseType: 'blob',\n                signal: this.abortController.signal,\n                onDownloadProgress: (progressEvent)=>{\n                    if (progressEvent.lengthComputable) {\n                        // Map download progress to 20-70% range (50% total)\n                        const downloadPercent = progressEvent.loaded / progressEvent.total * 100;\n                        const mappedProgress = 20 + downloadPercent * 0.5; // 20% + (0-50%)\n                        this._updateProgress(Math.round(mappedProgress), t(\"downloading_files\"));\n                    } else {\n                        // If we can't track progress, show incremental updates\n                        const currentTime = Date.now();\n                        if (!this.downloadStartTime) {\n                            this.downloadStartTime = currentTime;\n                        }\n                        const elapsed = (currentTime - this.downloadStartTime) / 1000;\n                        const estimatedProgress = Math.min(65, 20 + elapsed * 2); // Slow increment\n                        this._updateProgress(Math.round(estimatedProgress), t(\"downloading_files\"));\n                    }\n                }\n            });\n            // Download completed\n            this._updateProgress(70, t(\"download_complete_preparing\"));\n            return response.data;\n        } catch (error) {\n            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n    }\n    /**\n     * Fetch audio file with error handling (fallback method)\n     */ async _fetchAudio(url, t) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.currentProgress = 0;\n        this.progressCallback = null;\n        this.statusCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});