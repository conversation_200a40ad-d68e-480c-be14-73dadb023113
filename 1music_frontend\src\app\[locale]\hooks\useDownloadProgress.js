/**
 * React Hook for managing download progress with parallel operations
 * Handles cumulative progress calculation for concurrent tasks
 */

import { useState, useCallback, useRef } from 'react';

export const useDownloadProgress = () => {
    const [progress, setProgress] = useState(0);
    const [status, setStatus] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    
    // Progress state for different components
    const progressState = useRef({
        urlFetch: 0,        // 获取链接进度 (0-20%)
        ffmpegLoad: 0,      // FFmpeg加载进度 (0-10%)
        audioDownload: 0,   // 音频下载进度 (0-50%)
        transcoding: 0      // 转码进度 (0-20%)
    });

    /**
     * Calculate total progress from all parallel operations
     */
    const calculateTotalProgress = useCallback(() => {
        const { urlFetch, ffmpegLoad, audioDownload, transcoding } = progressState.current;
        return Math.min(100, urlFetch + ffmpegLoad + audioDownload + transcoding);
    }, []);

    /**
     * Update specific progress component and recalculate total
     */
    const updateProgressComponent = useCallback((component, componentProgress, statusMessage) => {
        progressState.current[component] = componentProgress;
        const totalProgress = calculateTotalProgress();
        
        setProgress(totalProgress);
        if (statusMessage) {
            setStatus(statusMessage);
        }
        
        console.log(`Progress Update - ${component}: ${componentProgress}%, Total: ${totalProgress}%`);
        console.log('Progress State:', { ...progressState.current });
    }, [calculateTotalProgress]);

    /**
     * Reset progress state
     */
    const resetProgress = useCallback(() => {
        progressState.current = {
            urlFetch: 0,
            ffmpegLoad: 0,
            audioDownload: 0,
            transcoding: 0
        };
        setProgress(0);
        setStatus('');
        setError(null);
        setIsLoading(true);
    }, []);

    /**
     * Set loading state
     */
    const setLoadingState = useCallback((loading) => {
        setIsLoading(loading);
    }, []);

    /**
     * Set error state
     */
    const setErrorState = useCallback((errorMessage) => {
        setError(errorMessage);
        setIsLoading(false);
    }, []);

    /**
     * Complete download
     */
    const completeDownload = useCallback(() => {
        setProgress(100);
        setIsLoading(false);
        setStatus('Download complete!');
    }, []);

    /**
     * Get progress callbacks for DownloadManager
     */
    const getProgressCallbacks = useCallback(() => {
        return {
            onProgress: setProgress,
            onStatus: setStatus,
            onProgressComponent: updateProgressComponent
        };
    }, [updateProgressComponent]);

    /**
     * Get current progress breakdown
     */
    const getProgressBreakdown = useCallback(() => {
        return { ...progressState.current };
    }, []);

    return {
        // State
        progress,
        status,
        isLoading,
        error,
        
        // Actions
        updateProgressComponent,
        resetProgress,
        setLoadingState,
        setErrorState,
        completeDownload,
        getProgressCallbacks,
        getProgressBreakdown,
        
        // Computed
        totalProgress: calculateTotalProgress()
    };
};

export default useDownloadProgress;
