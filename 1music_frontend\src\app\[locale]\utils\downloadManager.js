/**
 * Optimized Download Manager with parallel processing
 * Handles concurrent ffmpeg loading, download URL fetching, and file downloads
 */

import AudioTranscoder from './audioTranscoder';
import { fetchDownloadStatus, checkDownloadLink } from '../utils';
import axios from 'axios';

export class DownloadManager {
    constructor() {
        this.transcoder = null;
        this.abortController = null;
        this.currentProgress = 0;
        this.progressCallback = null;
        this.statusCallback = null;
    }

    /**
     * Update progress with specific percentage
     * @param {number} progress - Progress percentage (0-100)
     * @param {string} status - Status message
     */
    _updateProgress(progress, status) {
        this.currentProgress = progress;
        if (this.progressCallback) {
            this.progressCallback(progress);
        }
        if (this.statusCallback) {
            this.statusCallback(status);
        }
    }

    /**
     * Process download with optimized parallel operations
     * @param {Object} songData - Song information
     * @param {string} requestFormat - Requested audio format
     * @param {Function} onProgress - Progress callback
     * @param {Function} onStatus - Status update callback
     * @param {Function} t - Translation function
     */
    async processDownload(songData, requestFormat, onProgress, onStatus, t) {
        this.abortController = new AbortController();
        this.progressCallback = onProgress;
        this.statusCallback = onStatus;

        try {
            this._updateProgress(0, t("preparing_download"));

            // Initialize transcoder
            this.transcoder = new AudioTranscoder();

            // Set up transcoding progress callback (maps to 70-90% range)
            this.transcoder.setProgressCallback(({ progress }) => {
                const transcodingProgress = 70 + (progress * 20); // 70-90%
                this._updateProgress(Math.round(transcodingProgress), t("transcoding_audio"));
            });

            // Start parallel operations
            const ffmpegLoadPromise = requestFormat !== 'webm' ?
                this._loadFFmpegWithProgress(t) :
                Promise.resolve();

            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);

            // Pre-fetch thumbnail (optional, don't block on failure)
            const thumbnailPromise = songData.thumbnail ?
                this._fetchThumbnail(songData.thumbnail) :
                Promise.resolve(null);

            this._updateProgress(5, t("fetching_audio"));

            // Get download URL first (don't wait for FFmpeg if not needed yet)
            const originalAudioUrl = await downloadUrlPromise;
            this._updateProgress(20, t("download_url_ready"));

            // Start audio download immediately after getting URL
            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);

            // For webm format, we don't need FFmpeg, so download and return immediately
            if (requestFormat === 'webm') {
                const audioBlob = await audioDownloadPromise;
                this._updateProgress(90, t("preparing_download"));
                this._downloadFile(audioBlob, `${songData.title} - ${songData.artist}.webm`);
                this._updateProgress(100, t("download_complete"));
                return;
            }

            // Wait for FFmpeg loading to complete
            await ffmpegLoadPromise;
            this._updateProgress(30, t("transcoder_ready"));

            // Wait for audio download to complete
            const [audioBlob, imageBlob] = await Promise.all([
                audioDownloadPromise,
                thumbnailPromise
            ]);

            this._updateProgress(70, t("transcoding_audio"));

            // Transcode audio (progress handled by transcoder callback)
            const transcodedData = await this.transcoder.transcodeAudio(
                audioBlob,
                imageBlob,
                requestFormat,
                {
                    title: songData.title,
                    artist: songData.artist,
                    album: songData.album
                }
            );

            this._updateProgress(90, t("preparing_download"));

            // Create and download transcoded file
            const blob = new Blob([transcodedData], {
                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'
            });

            this._downloadFile(blob, `${songData.title} - ${songData.artist}.${requestFormat}`);
            this._updateProgress(100, t("download_complete"));

        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error(t("download_cancelled"));
            }
            throw error;
        }
    }

    /**
     * Load FFmpeg with progress updates
     */
    async _loadFFmpegWithProgress(t) {
        this._updateProgress(10, t("loading_transcoder"));
        await this.transcoder.load();
        return true;
    }

    /**
     * Get download URL with retries and progress updates
     */
    async _getDownloadUrlWithProgress(songData, t) {
        const maxRetries = 20;
        const retryDelay = 2000;

        for (let retries = 0; retries < maxRetries; retries++) {
            if (this.abortController.signal.aborted) {
                throw new Error('Download cancelled');
            }

            // Update progress based on retry attempts
            const progressStep = Math.min(15, 5 + (retries * 0.5));
            this._updateProgress(progressStep, t("fetching_audio"));

            try {
                const status = await fetchDownloadStatus(songData, 'download');
                if (status.download_url) {
                    const isReady = await checkDownloadLink(status.download_url);
                    if (isReady) {
                        return status.download_url;
                    }
                }
            } catch (error) {
                console.warn(`Download URL fetch attempt ${retries + 1} failed:`, error);
            }

            // Wait before retry
            await this._delay(retryDelay);
        }

        throw new Error(t("download_timeout"));
    }

    /**
     * Fetch audio file with progress tracking using axios
     */
    async _fetchAudioWithProgress(url, t) {
        try {
            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'blob',
                signal: this.abortController.signal,
                onDownloadProgress: (progressEvent) => {
                    if (progressEvent.lengthComputable) {
                        // Map download progress to 20-70% range (50% total)
                        const downloadPercent = (progressEvent.loaded / progressEvent.total) * 100;
                        const mappedProgress = 20 + (downloadPercent * 0.5); // 20% + (0-50%)
                        this._updateProgress(Math.round(mappedProgress), t("downloading_files"));
                    } else {
                        // If we can't track progress, show incremental updates
                        const currentTime = Date.now();
                        if (!this.downloadStartTime) {
                            this.downloadStartTime = currentTime;
                        }
                        const elapsed = (currentTime - this.downloadStartTime) / 1000;
                        const estimatedProgress = Math.min(65, 20 + (elapsed * 2)); // Slow increment
                        this._updateProgress(Math.round(estimatedProgress), t("downloading_files"));
                    }
                }
            });

            // Download completed
            this._updateProgress(70, t("download_complete_preparing"));
            return response.data;

        } catch (error) {
            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
                throw new Error(t("download_cancelled"));
            }
            throw new Error(t("failed_to_download_audio"));
        }
    }

    /**
     * Fetch audio file with error handling (fallback method)
     */
    async _fetchAudio(url, t) {
        const response = await fetch(url, {
            signal: this.abortController.signal
        });

        if (!response.ok) {
            throw new Error(t("failed_to_download_audio"));
        }

        return response.blob();
    }

    /**
     * Fetch thumbnail with graceful failure
     */
    async _fetchThumbnail(thumbnailUrl) {
        try {
            const response = await fetch(thumbnailUrl, {
                signal: this.abortController.signal
            });
            
            return response.ok ? response.blob() : null;
        } catch (error) {
            console.warn('Thumbnail fetch failed:', error);
            return null;
        }
    }

    /**
     * Download file to user's device
     */
    _downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // Clean up object URL
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }

    /**
     * Utility delay function
     */
    _delay(ms) {
        return new Promise(resolve => {
            const timeoutId = setTimeout(resolve, ms);
            
            // Allow cancellation
            this.abortController.signal.addEventListener('abort', () => {
                clearTimeout(timeoutId);
                resolve();
            });
        });
    }

    /**
     * Cancel ongoing download
     */
    cancel() {
        if (this.abortController) {
            this.abortController.abort();
        }
    }

    /**
     * Clean up resources
     */
    cleanup() {
        this.cancel();
        
        if (this.transcoder) {
            this.transcoder.terminate();
            this.transcoder = null;
        }
        
        this.abortController = null;
    }
}

export default DownloadManager;
