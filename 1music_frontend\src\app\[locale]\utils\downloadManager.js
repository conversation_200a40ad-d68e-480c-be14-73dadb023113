/**
 * Optimized Download Manager with parallel processing
 * Handles concurrent ffmpeg loading, download URL fetching, and file downloads
 */

import AudioTranscoder from './audioTranscoder';
import { fetchDownloadStatus, checkDownloadLink } from '../utils';

export class DownloadManager {
    constructor() {
        this.transcoder = null;
        this.abortController = null;
    }

    /**
     * Process download with optimized parallel operations
     * @param {Object} songData - Song information
     * @param {string} requestFormat - Requested audio format
     * @param {Function} onProgress - Progress callback
     * @param {Function} onStatus - Status update callback
     * @param {Function} t - Translation function
     */
    async processDownload(songData, requestFormat, onProgress, onStatus, t) {
        this.abortController = new AbortController();
        
        try {
            onStatus(t("preparing_download"));

            // Initialize transcoder
            this.transcoder = new AudioTranscoder();
            this.transcoder.setProgressCallback(({ progress }) => {
                onProgress(Math.round(progress * 100));
            });

            // Start parallel operations
            const ffmpegLoadPromise = requestFormat !== 'webm' ?
                this.transcoder.load() :
                Promise.resolve();

            const downloadUrlPromise = this._getDownloadUrl(songData, t);

            // Pre-fetch thumbnail (optional, don't block on failure)
            const thumbnailPromise = songData.thumbnail ?
                this._fetchThumbnail(songData.thumbnail) :
                Promise.resolve(null);

            onStatus(t("fetching_audio"));

            // Get download URL first (don't wait for FFmpeg if not needed yet)
            const originalAudioUrl = await downloadUrlPromise;

            onStatus(t("downloading_files"));

            // Start audio download immediately after getting URL
            const audioDownloadPromise = this._fetchAudio(originalAudioUrl, t);

            // Update status based on what's still loading
            if (requestFormat !== 'webm') {
                // Check if FFmpeg is still loading
                const ffmpegStatus = await Promise.race([
                    ffmpegLoadPromise.then(() => 'loaded'),
                    new Promise(resolve => setTimeout(() => resolve('loading'), 100))
                ]);

                if (ffmpegStatus === 'loading') {
                    onStatus(t("loading_transcoder"));
                }
            }

            // For webm format, we don't need FFmpeg, so download and return immediately
            if (requestFormat === 'webm') {
                const audioBlob = await audioDownloadPromise;
                this._downloadFile(audioBlob, `${songData.title} - ${songData.artist}.webm`);
                onStatus(t("download_complete"));
                return;
            }

            // For transcoding formats, wait for both audio download and FFmpeg loading
            const [audioBlob, , imageBlob] = await Promise.all([
                audioDownloadPromise,
                ffmpegLoadPromise,  // Ensure FFmpeg is loaded before transcoding
                thumbnailPromise
            ]);

            onStatus(t("transcoding_audio"));

            // Transcode audio
            const transcodedData = await this.transcoder.transcodeAudio(
                audioBlob,
                imageBlob,
                requestFormat,
                {
                    title: songData.title,
                    artist: songData.artist,
                    album: songData.album
                }
            );

            onStatus(t("preparing_download"));

            // Create and download transcoded file
            const blob = new Blob([transcodedData], {
                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'
            });
            
            this._downloadFile(blob, `${songData.title} - ${songData.artist}.${requestFormat}`);
            onStatus(t("download_complete"));

        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error(t("download_cancelled"));
            }
            throw error;
        }
    }

    /**
     * Get download URL with retries and timeout
     */
    async _getDownloadUrl(songData, t) {
        const maxRetries = 20;
        const retryDelay = 2000;
        
        for (let retries = 0; retries < maxRetries; retries++) {
            if (this.abortController.signal.aborted) {
                throw new Error('Download cancelled');
            }

            try {
                const status = await fetchDownloadStatus(songData, 'download');
                if (status.download_url) {
                    const isReady = await checkDownloadLink(status.download_url);
                    if (isReady) {
                        return status.download_url;
                    }
                }
            } catch (error) {
                console.warn(`Download URL fetch attempt ${retries + 1} failed:`, error);
            }
            
            // Wait before retry
            await this._delay(retryDelay);
        }
        
        throw new Error(t("download_timeout"));
    }

    /**
     * Fetch audio file with error handling
     */
    async _fetchAudio(url, t) {
        const response = await fetch(url, {
            signal: this.abortController.signal
        });
        
        if (!response.ok) {
            throw new Error(t("failed_to_download_audio"));
        }
        
        return response.blob();
    }

    /**
     * Fetch thumbnail with graceful failure
     */
    async _fetchThumbnail(thumbnailUrl) {
        try {
            const response = await fetch(thumbnailUrl, {
                signal: this.abortController.signal
            });
            
            return response.ok ? response.blob() : null;
        } catch (error) {
            console.warn('Thumbnail fetch failed:', error);
            return null;
        }
    }

    /**
     * Download file to user's device
     */
    _downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // Clean up object URL
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }

    /**
     * Utility delay function
     */
    _delay(ms) {
        return new Promise(resolve => {
            const timeoutId = setTimeout(resolve, ms);
            
            // Allow cancellation
            this.abortController.signal.addEventListener('abort', () => {
                clearTimeout(timeoutId);
                resolve();
            });
        });
    }

    /**
     * Cancel ongoing download
     */
    cancel() {
        if (this.abortController) {
            this.abortController.abort();
        }
    }

    /**
     * Clean up resources
     */
    cleanup() {
        this.cancel();
        
        if (this.transcoder) {
            this.transcoder.terminate();
            this.transcoder = null;
        }
        
        this.abortController = null;
    }
}

export default DownloadManager;
