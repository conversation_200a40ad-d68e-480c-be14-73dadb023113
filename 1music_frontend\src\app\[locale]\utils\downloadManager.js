/**
 * Optimized Download Manager with parallel processing
 * Handles concurrent ffmpeg loading, download URL fetching, and file downloads
 */

import AudioTranscoder from './audioTranscoder';
import { fetchDownloadStatus, checkDownloadLink } from '../utils';
import axios from 'axios';

export class DownloadManager {
    constructor() {
        this.transcoder = null;
        this.abortController = null;
        this.progressState = {
            urlFetch: 0,        // 获取链接进度 (0-20%)
            ffmpegLoad: 0,      // FFmpeg加载进度 (0-10%)
            audioDownload: 0,   // 音频下载进度 (0-50%)
            transcoding: 0      // 转码进度 (0-20%)
        };
        this.progressCallback = null;
        this.statusCallback = null;
        this.progressComponentCallback = null;
    }

    /**
     * Calculate total progress from all parallel operations
     */
    _calculateTotalProgress() {
        const { urlFetch, ffmpegLoad, audioDownload, transcoding } = this.progressState;
        return Math.min(100, urlFetch + ffmpegLoad + audioDownload + transcoding);
    }

    /**
     * Update specific progress component and recalculate total
     * @param {string} component - Progress component name
     * @param {number} progress - Progress value for this component
     * @param {string} status - Status message
     */
    _updateProgressComponent(component, progress, status) {
        this.progressState[component] = progress;
        const totalProgress = this._calculateTotalProgress();

        if (this.progressCallback) {
            this.progressCallback(totalProgress);
        }
        if (this.statusCallback) {
            this.statusCallback(status);
        }
        if (this.progressComponentCallback) {
            this.progressComponentCallback(component, progress, status);
        }

        console.log(`Progress Update - ${component}: ${progress}%, Total: ${totalProgress}%`);
    }

    /**
     * Process download with component-based progress tracking
     * @param {Object} songData - Song information
     * @param {string} requestFormat - Requested audio format
     * @param {Function} onProgressComponent - Component progress callback
     * @param {Function} t - Translation function
     */
    async processDownloadWithComponents(songData, requestFormat, onProgressComponent, t) {
        this.abortController = new AbortController();
        this.progressComponentCallback = onProgressComponent;

        try {
            // Reset progress state
            this.progressState = {
                urlFetch: 0,
                ffmpegLoad: 0,
                audioDownload: 0,
                transcoding: 0
            };

            this._updateProgressComponent('urlFetch', 0, t("preparing_download"));

            // Initialize transcoder
            this.transcoder = new AudioTranscoder();

            // Set up transcoding progress callback (maps to 0-20% of transcoding component)
            this.transcoder.setProgressCallback(({ progress }) => {
                const transcodingProgress = progress * 20; // 0-20%
                this._updateProgressComponent('transcoding', transcodingProgress, t("transcoding_audio"));
            });

            // Start parallel operations
            const ffmpegLoadPromise = requestFormat !== 'webm' ?
                this._loadFFmpegWithProgress(t) :
                this._skipFFmpegLoad();

            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);

            // Pre-fetch thumbnail (optional, don't block on failure)
            const thumbnailPromise = songData.thumbnail ?
                this._fetchThumbnail(songData.thumbnail) :
                Promise.resolve(null);

            // Get download URL first (don't wait for FFmpeg if not needed yet)
            const originalAudioUrl = await downloadUrlPromise;

            // Start audio download immediately after getting URL
            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);

            // For webm format, we don't need FFmpeg, so download and return immediately
            if (requestFormat === 'webm') {
                const audioBlob = await audioDownloadPromise;
                this._updateProgressComponent('transcoding', 20, t("preparing_download"));
                this._downloadFile(audioBlob, `${songData.title} - ${songData.artist}.webm`);
                return;
            }

            // Wait for FFmpeg loading to complete
            await ffmpegLoadPromise;

            // Wait for audio download to complete
            const [audioBlob, imageBlob] = await Promise.all([
                audioDownloadPromise,
                thumbnailPromise
            ]);

            // Start transcoding
            this._updateProgressComponent('transcoding', 0, t("transcoding_audio"));

            // Transcode audio (progress handled by transcoder callback)
            const transcodedData = await this.transcoder.transcodeAudio(
                audioBlob,
                imageBlob,
                requestFormat,
                {
                    title: songData.title,
                    artist: songData.artist,
                    album: songData.album
                }
            );

            // Transcoding complete
            this._updateProgressComponent('transcoding', 20, t("preparing_download"));

            // Create and download transcoded file
            const blob = new Blob([transcodedData], {
                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'
            });

            this._downloadFile(blob, `${songData.title} - ${songData.artist}.${requestFormat}`);
            this._updateProgressComponent('transcoding', 20, t("download_complete"));

        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error(t("download_cancelled"));
            }
            throw error;
        }
    }

    /**
     * Process download with optimized parallel operations
     * @param {Object} songData - Song information
     * @param {string} requestFormat - Requested audio format
     * @param {Function} onProgress - Progress callback
     * @param {Function} onStatus - Status update callback
     * @param {Function} t - Translation function
     */
    async processDownload(songData, requestFormat, onProgress, onStatus, t) {
        this.abortController = new AbortController();
        this.progressCallback = onProgress;
        this.statusCallback = onStatus;

        try {
            // Reset progress state
            this.progressState = {
                urlFetch: 0,
                ffmpegLoad: 0,
                audioDownload: 0,
                transcoding: 0
            };

            this._updateProgressComponent('urlFetch', 0, t("preparing_download"));

            // Initialize transcoder
            this.transcoder = new AudioTranscoder();

            // Set up transcoding progress callback (maps to 0-20% of transcoding component)
            this.transcoder.setProgressCallback(({ progress }) => {
                const transcodingProgress = progress * 20; // 0-20%
                this._updateProgressComponent('transcoding', transcodingProgress, t("transcoding_audio"));
            });

            // Start parallel operations
            const ffmpegLoadPromise = requestFormat !== 'webm' ?
                this._loadFFmpegWithProgress(t) :
                this._skipFFmpegLoad();

            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData, t);

            // Pre-fetch thumbnail (optional, don't block on failure)
            const thumbnailPromise = songData.thumbnail ?
                this._fetchThumbnail(songData.thumbnail) :
                Promise.resolve(null);

            // Get download URL first (don't wait for FFmpeg if not needed yet)
            const originalAudioUrl = await downloadUrlPromise;

            // Start audio download immediately after getting URL
            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl, t);

            // For webm format, we don't need FFmpeg, so download and return immediately
            if (requestFormat === 'webm') {
                const audioBlob = await audioDownloadPromise;
                this._updateProgressComponent('transcoding', 20, t("preparing_download"));
                this._downloadFile(audioBlob, `${songData.title} - ${songData.artist}.webm`);
                return;
            }

            // Wait for FFmpeg loading to complete
            await ffmpegLoadPromise;

            // Wait for audio download to complete
            const [audioBlob, imageBlob] = await Promise.all([
                audioDownloadPromise,
                thumbnailPromise
            ]);

            // Start transcoding
            this._updateProgressComponent('transcoding', 0, t("transcoding_audio"));

            // Transcode audio (progress handled by transcoder callback)
            const transcodedData = await this.transcoder.transcodeAudio(
                audioBlob,
                imageBlob,
                requestFormat,
                {
                    title: songData.title,
                    artist: songData.artist,
                    album: songData.album
                }
            );

            // Transcoding complete
            this._updateProgressComponent('transcoding', 20, t("preparing_download"));

            // Create and download transcoded file
            const blob = new Blob([transcodedData], {
                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'
            });

            this._downloadFile(blob, `${songData.title} - ${songData.artist}.${requestFormat}`);
            this._updateProgressComponent('transcoding', 20, t("download_complete"));

        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error(t("download_cancelled"));
            }
            throw error;
        }
    }

    /**
     * Load FFmpeg with progress updates
     */
    async _loadFFmpegWithProgress(t) {
        this._updateProgressComponent('ffmpegLoad', 0, t("loading_transcoder"));
        await this.transcoder.load();
        this._updateProgressComponent('ffmpegLoad', 10, t("transcoder_ready"));
        return true;
    }

    /**
     * Skip FFmpeg loading for webm format
     */
    async _skipFFmpegLoad() {
        this._updateProgressComponent('ffmpegLoad', 10, "FFmpeg not needed for webm");
        return true;
    }

    /**
     * Get download URL with retries and progress updates
     */
    async _getDownloadUrlWithProgress(songData, t) {
        const maxRetries = 20;
        const retryDelay = 2000;

        for (let retries = 0; retries < maxRetries; retries++) {
            if (this.abortController.signal.aborted) {
                throw new Error('Download cancelled');
            }

            // Update progress based on retry attempts (0-20% range)
            const progressStep = Math.min(20, (retries + 1) * (20 / maxRetries));
            this._updateProgressComponent('urlFetch', progressStep, t("fetching_audio"));

            try {
                const status = await fetchDownloadStatus(songData, 'download');
                if (status.download_url) {
                    const isReady = await checkDownloadLink(status.download_url);
                    if (isReady) {
                        this._updateProgressComponent('urlFetch', 20, t("download_url_ready"));
                        return status.download_url;
                    }
                }
            } catch (error) {
                console.warn(`Download URL fetch attempt ${retries + 1} failed:`, error);
            }

            // Wait before retry
            await this._delay(retryDelay);
        }

        throw new Error(t("download_timeout"));
    }

    /**
     * Fetch audio file with progress tracking using axios
     */
    async _fetchAudioWithProgress(url, t) {
        try {
            this._updateProgressComponent('audioDownload', 0, t("downloading_files"));

            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'blob',
                signal: this.abortController.signal,
                onDownloadProgress: (progressEvent) => {
                    if (progressEvent.lengthComputable) {
                        // Map download progress to 0-50% range for audioDownload component
                        const downloadPercent = (progressEvent.loaded / progressEvent.total) * 100;
                        const mappedProgress = (downloadPercent / 100) * 50; // 0-50%
                        this._updateProgressComponent('audioDownload', mappedProgress, t("downloading_files"));
                    } else {
                        // If we can't track progress, show incremental updates
                        const currentTime = Date.now();
                        if (!this.downloadStartTime) {
                            this.downloadStartTime = currentTime;
                        }
                        const elapsed = (currentTime - this.downloadStartTime) / 1000;
                        const estimatedProgress = Math.min(45, elapsed * 2); // Slow increment up to 45%
                        this._updateProgressComponent('audioDownload', estimatedProgress, t("downloading_files"));
                    }
                }
            });

            // Download completed
            this._updateProgressComponent('audioDownload', 50, t("download_complete_preparing"));
            return response.data;

        } catch (error) {
            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
                throw new Error(t("download_cancelled"));
            }
            throw new Error(t("failed_to_download_audio"));
        }
    }

    /**
     * Fetch audio file with error handling (fallback method)
     */
    async _fetchAudio(url, t) {
        const response = await fetch(url, {
            signal: this.abortController.signal
        });

        if (!response.ok) {
            throw new Error(t("failed_to_download_audio"));
        }

        return response.blob();
    }

    /**
     * Fetch thumbnail with graceful failure
     */
    async _fetchThumbnail(thumbnailUrl) {
        try {
            const response = await fetch(thumbnailUrl, {
                signal: this.abortController.signal
            });
            
            return response.ok ? response.blob() : null;
        } catch (error) {
            console.warn('Thumbnail fetch failed:', error);
            return null;
        }
    }

    /**
     * Download file to user's device
     */
    _downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // Clean up object URL
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }

    /**
     * Utility delay function
     */
    _delay(ms) {
        return new Promise(resolve => {
            const timeoutId = setTimeout(resolve, ms);
            
            // Allow cancellation
            this.abortController.signal.addEventListener('abort', () => {
                clearTimeout(timeoutId);
                resolve();
            });
        });
    }

    /**
     * Cancel ongoing download
     */
    cancel() {
        if (this.abortController) {
            this.abortController.abort();
        }
    }

    /**
     * Clean up resources
     */
    cleanup() {
        this.cancel();
        
        if (this.transcoder) {
            this.transcoder.terminate();
            this.transcoder = null;
        }
        
        this.abortController = null;
    }
}

export default DownloadManager;
